{"version": 3, "sources": ["../../../src/vs/base/common/errors.ts", "../../../src/vs/base/common/functional.ts", "../../../src/vs/base/common/arraysFind.ts", "../../../src/vs/base/common/arrays.ts", "../../../src/vs/base/common/collections.ts", "../../../src/vs/base/common/map.ts", "../../../src/vs/base/common/iterator.ts", "../../../src/vs/base/common/lifecycle.ts", "../../../src/vs/base/common/linkedList.ts", "../../../src/vs/base/common/stopwatch.ts", "../../../src/vs/base/common/event.ts", "../src/SearchAddon.ts"], "sourcesContent": ["/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nexport interface ErrorListenerCallback {\n\t(error: any): void;\n}\n\nexport interface ErrorListenerUnbind {\n\t(): void;\n}\n\n// Avoid circular dependency on EventEmitter by implementing a subset of the interface.\nexport class ErrorHandler {\n\tprivate unexpectedErrorHandler: (e: any) => void;\n\tprivate listeners: ErrorListenerCallback[];\n\n\tconstructor() {\n\n\t\tthis.listeners = [];\n\n\t\tthis.unexpectedErrorHandler = function (e: any) {\n\t\t\tsetTimeout(() => {\n\t\t\t\tif (e.stack) {\n\t\t\t\t\tif (ErrorNoTelemetry.isErrorNoTelemetry(e)) {\n\t\t\t\t\t\tthrow new ErrorNoTelemetry(e.message + '\\n\\n' + e.stack);\n\t\t\t\t\t}\n\n\t\t\t\t\tthrow new Error(e.message + '\\n\\n' + e.stack);\n\t\t\t\t}\n\n\t\t\t\tthrow e;\n\t\t\t}, 0);\n\t\t};\n\t}\n\n\taddListener(listener: ErrorListenerCallback): ErrorListenerUnbind {\n\t\tthis.listeners.push(listener);\n\n\t\treturn () => {\n\t\t\tthis._removeListener(listener);\n\t\t};\n\t}\n\n\tprivate emit(e: any): void {\n\t\tthis.listeners.forEach((listener) => {\n\t\t\tlistener(e);\n\t\t});\n\t}\n\n\tprivate _removeListener(listener: ErrorListenerCallback): void {\n\t\tthis.listeners.splice(this.listeners.indexOf(listener), 1);\n\t}\n\n\tsetUnexpectedErrorHandler(newUnexpectedErrorHandler: (e: any) => void): void {\n\t\tthis.unexpectedErrorHandler = newUnexpectedErrorHandler;\n\t}\n\n\tgetUnexpectedErrorHandler(): (e: any) => void {\n\t\treturn this.unexpectedErrorHandler;\n\t}\n\n\tonUnexpectedError(e: any): void {\n\t\tthis.unexpectedErrorHandler(e);\n\t\tthis.emit(e);\n\t}\n\n\t// For external errors, we don't want the listeners to be called\n\tonUnexpectedExternalError(e: any): void {\n\t\tthis.unexpectedErrorHandler(e);\n\t}\n}\n\nexport const errorHandler = new ErrorHandler();\n\n/** @skipMangle */\nexport function setUnexpectedErrorHandler(newUnexpectedErrorHandler: (e: any) => void): void {\n\terrorHandler.setUnexpectedErrorHandler(newUnexpectedErrorHandler);\n}\n\n/**\n * Returns if the error is a SIGPIPE error. SIGPIPE errors should generally be\n * logged at most once, to avoid a loop.\n *\n * @see https://github.com/microsoft/vscode-remote-release/issues/6481\n */\nexport function isSigPipeError(e: unknown): e is Error {\n\tif (!e || typeof e !== 'object') {\n\t\treturn false;\n\t}\n\n\tconst cast = e as Record<string, string | undefined>;\n\treturn cast.code === 'EPIPE' && cast.syscall?.toUpperCase() === 'WRITE';\n}\n\nexport function onUnexpectedError(e: any): undefined {\n\t// ignore errors from cancelled promises\n\tif (!isCancellationError(e)) {\n\t\terrorHandler.onUnexpectedError(e);\n\t}\n\treturn undefined;\n}\n\nexport function onUnexpectedExternalError(e: any): undefined {\n\t// ignore errors from cancelled promises\n\tif (!isCancellationError(e)) {\n\t\terrorHandler.onUnexpectedExternalError(e);\n\t}\n\treturn undefined;\n}\n\nexport interface SerializedError {\n\treadonly $isError: true;\n\treadonly name: string;\n\treadonly message: string;\n\treadonly stack: string;\n\treadonly noTelemetry: boolean;\n}\n\nexport function transformErrorForSerialization(error: Error): SerializedError;\nexport function transformErrorForSerialization(error: any): any;\nexport function transformErrorForSerialization(error: any): any {\n\tif (error instanceof Error) {\n\t\tconst { name, message } = error;\n\t\tconst stack: string = (<any>error).stacktrace || (<any>error).stack;\n\t\treturn {\n\t\t\t$isError: true,\n\t\t\tname,\n\t\t\tmessage,\n\t\t\tstack,\n\t\t\tnoTelemetry: ErrorNoTelemetry.isErrorNoTelemetry(error)\n\t\t};\n\t}\n\n\t// return as is\n\treturn error;\n}\n\nexport function transformErrorFromSerialization(data: SerializedError): Error {\n\tlet error: Error;\n\tif (data.noTelemetry) {\n\t\terror = new ErrorNoTelemetry();\n\t} else {\n\t\terror = new Error();\n\t\terror.name = data.name;\n\t}\n\terror.message = data.message;\n\terror.stack = data.stack;\n\treturn error;\n}\n\n// see https://github.com/v8/v8/wiki/Stack%20Trace%20API#basic-stack-traces\nexport interface V8CallSite {\n\tgetThis(): unknown;\n\tgetTypeName(): string | null;\n\tgetFunction(): Function | undefined;\n\tgetFunctionName(): string | null;\n\tgetMethodName(): string | null;\n\tgetFileName(): string | null;\n\tgetLineNumber(): number | null;\n\tgetColumnNumber(): number | null;\n\tgetEvalOrigin(): string | undefined;\n\tisToplevel(): boolean;\n\tisEval(): boolean;\n\tisNative(): boolean;\n\tisConstructor(): boolean;\n\ttoString(): string;\n}\n\nconst canceledName = 'Canceled';\n\n/**\n * Checks if the given error is a promise in canceled state\n */\nexport function isCancellationError(error: any): boolean {\n\tif (error instanceof CancellationError) {\n\t\treturn true;\n\t}\n\treturn error instanceof Error && error.name === canceledName && error.message === canceledName;\n}\n\n// !!!IMPORTANT!!!\n// Do NOT change this class because it is also used as an API-type.\nexport class CancellationError extends Error {\n\tconstructor() {\n\t\tsuper(canceledName);\n\t\tthis.name = this.message;\n\t}\n}\n\n/**\n * @deprecated use {@link CancellationError `new CancellationError()`} instead\n */\nexport function canceled(): Error {\n\tconst error = new Error(canceledName);\n\terror.name = error.message;\n\treturn error;\n}\n\nexport function illegalArgument(name?: string): Error {\n\tif (name) {\n\t\treturn new Error(`Illegal argument: ${name}`);\n\t} else {\n\t\treturn new Error('Illegal argument');\n\t}\n}\n\nexport function illegalState(name?: string): Error {\n\tif (name) {\n\t\treturn new Error(`Illegal state: ${name}`);\n\t} else {\n\t\treturn new Error('Illegal state');\n\t}\n}\n\nexport class ReadonlyError extends TypeError {\n\tconstructor(name?: string) {\n\t\tsuper(name ? `${name} is read-only and cannot be changed` : 'Cannot change read-only property');\n\t}\n}\n\nexport function getErrorMessage(err: any): string {\n\tif (!err) {\n\t\treturn 'Error';\n\t}\n\n\tif (err.message) {\n\t\treturn err.message;\n\t}\n\n\tif (err.stack) {\n\t\treturn err.stack.split('\\n')[0];\n\t}\n\n\treturn String(err);\n}\n\nexport class NotImplementedError extends Error {\n\tconstructor(message?: string) {\n\t\tsuper('NotImplemented');\n\t\tif (message) {\n\t\t\tthis.message = message;\n\t\t}\n\t}\n}\n\nexport class NotSupportedError extends Error {\n\tconstructor(message?: string) {\n\t\tsuper('NotSupported');\n\t\tif (message) {\n\t\t\tthis.message = message;\n\t\t}\n\t}\n}\n\nexport class ExpectedError extends Error {\n\treadonly isExpected = true;\n}\n\n/**\n * Error that when thrown won't be logged in telemetry as an unhandled error.\n */\nexport class ErrorNoTelemetry extends Error {\n\toverride readonly name: string;\n\n\tconstructor(msg?: string) {\n\t\tsuper(msg);\n\t\tthis.name = 'CodeExpectedError';\n\t}\n\n\tpublic static fromError(err: Error): ErrorNoTelemetry {\n\t\tif (err instanceof ErrorNoTelemetry) {\n\t\t\treturn err;\n\t\t}\n\n\t\tconst result = new ErrorNoTelemetry();\n\t\tresult.message = err.message;\n\t\tresult.stack = err.stack;\n\t\treturn result;\n\t}\n\n\tpublic static isErrorNoTelemetry(err: Error): err is ErrorNoTelemetry {\n\t\treturn err.name === 'CodeExpectedError';\n\t}\n}\n\n/**\n * This error indicates a bug.\n * Do not throw this for invalid user input.\n * Only catch this error to recover gracefully from bugs.\n */\nexport class BugIndicatingError extends Error {\n\tconstructor(message?: string) {\n\t\tsuper(message || 'An unexpected bug occurred.');\n\t\tObject.setPrototypeOf(this, BugIndicatingError.prototype);\n\n\t\t// Because we know for sure only buggy code throws this,\n\t\t// we definitely want to break here and fix the bug.\n\t\t// eslint-disable-next-line no-debugger\n\t\t// debugger;\n\t}\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n/**\n * Given a function, returns a function that is only calling that function once.\n */\nexport function createSingleCallFunction<T extends Function>(this: unknown, fn: T, fnDidRunCallback?: () => void): T {\n\tconst _this = this;\n\tlet didCall = false;\n\tlet result: unknown;\n\n\treturn function () {\n\t\tif (didCall) {\n\t\t\treturn result;\n\t\t}\n\n\t\tdidCall = true;\n\t\tif (fnDidRunCallback) {\n\t\t\ttry {\n\t\t\t\tresult = fn.apply(_this, arguments);\n\t\t\t} finally {\n\t\t\t\tfnDidRunCallback();\n\t\t\t}\n\t\t} else {\n\t\t\tresult = fn.apply(_this, arguments);\n\t\t}\n\n\t\treturn result;\n\t} as unknown as T;\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { Comparator } from './arrays';\n\nexport function findLast<T>(array: readonly T[], predicate: (item: T) => boolean): T | undefined {\n\tconst idx = findLastIdx(array, predicate);\n\tif (idx === -1) {\n\t\treturn undefined;\n\t}\n\treturn array[idx];\n}\n\nexport function findLastIdx<T>(array: readonly T[], predicate: (item: T) => boolean, fromIndex = array.length - 1): number {\n\tfor (let i = fromIndex; i >= 0; i--) {\n\t\tconst element = array[i];\n\n\t\tif (predicate(element)) {\n\t\t\treturn i;\n\t\t}\n\t}\n\n\treturn -1;\n}\n\n/**\n * Finds the last item where predicate is true using binary search.\n * `predicate` must be monotonous, i.e. `arr.map(predicate)` must be like `[true, ..., true, false, ..., false]`!\n *\n * @returns `undefined` if no item matches, otherwise the last item that matches the predicate.\n */\nexport function findLastMonotonous<T>(array: readonly T[], predicate: (item: T) => boolean): T | undefined {\n\tconst idx = findLastIdxMonotonous(array, predicate);\n\treturn idx === -1 ? undefined : array[idx];\n}\n\n/**\n * Finds the last item where predicate is true using binary search.\n * `predicate` must be monotonous, i.e. `arr.map(predicate)` must be like `[true, ..., true, false, ..., false]`!\n *\n * @returns `startIdx - 1` if predicate is false for all items, otherwise the index of the last item that matches the predicate.\n */\nexport function findLastIdxMonotonous<T>(array: readonly T[], predicate: (item: T) => boolean, startIdx = 0, endIdxEx = array.length): number {\n\tlet i = startIdx;\n\tlet j = endIdxEx;\n\twhile (i < j) {\n\t\tconst k = Math.floor((i + j) / 2);\n\t\tif (predicate(array[k])) {\n\t\t\ti = k + 1;\n\t\t} else {\n\t\t\tj = k;\n\t\t}\n\t}\n\treturn i - 1;\n}\n\n/**\n * Finds the first item where predicate is true using binary search.\n * `predicate` must be monotonous, i.e. `arr.map(predicate)` must be like `[false, ..., false, true, ..., true]`!\n *\n * @returns `undefined` if no item matches, otherwise the first item that matches the predicate.\n */\nexport function findFirstMonotonous<T>(array: readonly T[], predicate: (item: T) => boolean): T | undefined {\n\tconst idx = findFirstIdxMonotonousOrArrLen(array, predicate);\n\treturn idx === array.length ? undefined : array[idx];\n}\n\n/**\n * Finds the first item where predicate is true using binary search.\n * `predicate` must be monotonous, i.e. `arr.map(predicate)` must be like `[false, ..., false, true, ..., true]`!\n *\n * @returns `endIdxEx` if predicate is false for all items, otherwise the index of the first item that matches the predicate.\n */\nexport function findFirstIdxMonotonousOrArrLen<T>(array: readonly T[], predicate: (item: T) => boolean, startIdx = 0, endIdxEx = array.length): number {\n\tlet i = startIdx;\n\tlet j = endIdxEx;\n\twhile (i < j) {\n\t\tconst k = Math.floor((i + j) / 2);\n\t\tif (predicate(array[k])) {\n\t\t\tj = k;\n\t\t} else {\n\t\t\ti = k + 1;\n\t\t}\n\t}\n\treturn i;\n}\n\nexport function findFirstIdxMonotonous<T>(array: readonly T[], predicate: (item: T) => boolean, startIdx = 0, endIdxEx = array.length): number {\n\tconst idx = findFirstIdxMonotonousOrArrLen(array, predicate, startIdx, endIdxEx);\n\treturn idx === array.length ? -1 : idx;\n}\n\n/**\n * Use this when\n * * You have a sorted array\n * * You query this array with a monotonous predicate to find the last item that has a certain property.\n * * You query this array multiple times with monotonous predicates that get weaker and weaker.\n */\nexport class MonotonousArray<T> {\n\tpublic static assertInvariants = false;\n\n\tprivate _findLastMonotonousLastIdx = 0;\n\tprivate _prevFindLastPredicate: ((item: T) => boolean) | undefined;\n\n\tconstructor(private readonly _array: readonly T[]) {\n\t}\n\n\t/**\n\t * The predicate must be monotonous, i.e. `arr.map(predicate)` must be like `[true, ..., true, false, ..., false]`!\n\t * For subsequent calls, current predicate must be weaker than (or equal to) the previous predicate, i.e. more entries must be `true`.\n\t */\n\tfindLastMonotonous(predicate: (item: T) => boolean): T | undefined {\n\t\tif (MonotonousArray.assertInvariants) {\n\t\t\tif (this._prevFindLastPredicate) {\n\t\t\t\tfor (const item of this._array) {\n\t\t\t\t\tif (this._prevFindLastPredicate(item) && !predicate(item)) {\n\t\t\t\t\t\tthrow new Error('MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis._prevFindLastPredicate = predicate;\n\t\t}\n\n\t\tconst idx = findLastIdxMonotonous(this._array, predicate, this._findLastMonotonousLastIdx);\n\t\tthis._findLastMonotonousLastIdx = idx + 1;\n\t\treturn idx === -1 ? undefined : this._array[idx];\n\t}\n}\n\n/**\n * Returns the first item that is equal to or greater than every other item.\n*/\nexport function findFirstMax<T>(array: readonly T[], comparator: Comparator<T>): T | undefined {\n\tif (array.length === 0) {\n\t\treturn undefined;\n\t}\n\n\tlet max = array[0];\n\tfor (let i = 1; i < array.length; i++) {\n\t\tconst item = array[i];\n\t\tif (comparator(item, max) > 0) {\n\t\t\tmax = item;\n\t\t}\n\t}\n\treturn max;\n}\n\n/**\n * Returns the last item that is equal to or greater than every other item.\n*/\nexport function findLastMax<T>(array: readonly T[], comparator: Comparator<T>): T | undefined {\n\tif (array.length === 0) {\n\t\treturn undefined;\n\t}\n\n\tlet max = array[0];\n\tfor (let i = 1; i < array.length; i++) {\n\t\tconst item = array[i];\n\t\tif (comparator(item, max) >= 0) {\n\t\t\tmax = item;\n\t\t}\n\t}\n\treturn max;\n}\n\n/**\n * Returns the first item that is equal to or less than every other item.\n*/\nexport function findFirstMin<T>(array: readonly T[], comparator: Comparator<T>): T | undefined {\n\treturn findFirstMax(array, (a, b) => -comparator(a, b));\n}\n\nexport function findMaxIdx<T>(array: readonly T[], comparator: Comparator<T>): number {\n\tif (array.length === 0) {\n\t\treturn -1;\n\t}\n\n\tlet maxIdx = 0;\n\tfor (let i = 1; i < array.length; i++) {\n\t\tconst item = array[i];\n\t\tif (comparator(item, array[maxIdx]) > 0) {\n\t\t\tmaxIdx = i;\n\t\t}\n\t}\n\treturn maxIdx;\n}\n\n/**\n * Returns the first mapped value of the array which is not undefined.\n */\nexport function mapFindFirst<T, R>(items: Iterable<T>, mapFn: (value: T) => R | undefined): R | undefined {\n\tfor (const value of items) {\n\t\tconst mapped = mapFn(value);\n\t\tif (mapped !== undefined) {\n\t\t\treturn mapped;\n\t\t}\n\t}\n\n\treturn undefined;\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { CancellationToken } from 'vs/base/common/cancellation';\nimport { CancellationError } from 'vs/base/common/errors';\nimport { ISplice } from 'vs/base/common/sequence';\nimport { findFirstIdxMonotonousOrArrLen } from './arraysFind';\n\n/**\n * Returns the last element of an array.\n * @param array The array.\n * @param n Which element from the end (default is zero).\n */\nexport function tail<T>(array: ArrayLike<T>, n: number = 0): T | undefined {\n\treturn array[array.length - (1 + n)];\n}\n\nexport function tail2<T>(arr: T[]): [T[], T] {\n\tif (arr.length === 0) {\n\t\tthrow new Error('Invalid tail call');\n\t}\n\n\treturn [arr.slice(0, arr.length - 1), arr[arr.length - 1]];\n}\n\nexport function equals<T>(one: ReadonlyArray<T> | undefined, other: ReadonlyArray<T> | undefined, itemEquals: (a: T, b: T) => boolean = (a, b) => a === b): boolean {\n\tif (one === other) {\n\t\treturn true;\n\t}\n\n\tif (!one || !other) {\n\t\treturn false;\n\t}\n\n\tif (one.length !== other.length) {\n\t\treturn false;\n\t}\n\n\tfor (let i = 0, len = one.length; i < len; i++) {\n\t\tif (!itemEquals(one[i], other[i])) {\n\t\t\treturn false;\n\t\t}\n\t}\n\n\treturn true;\n}\n\n/**\n * Remove the element at `index` by replacing it with the last element. This is faster than `splice`\n * but changes the order of the array\n */\nexport function removeFastWithoutKeepingOrder<T>(array: T[], index: number) {\n\tconst last = array.length - 1;\n\tif (index < last) {\n\t\tarray[index] = array[last];\n\t}\n\tarray.pop();\n}\n\n/**\n * Performs a binary search algorithm over a sorted array.\n *\n * @param array The array being searched.\n * @param key The value we search for.\n * @param comparator A function that takes two array elements and returns zero\n *   if they are equal, a negative number if the first element precedes the\n *   second one in the sorting order, or a positive number if the second element\n *   precedes the first one.\n * @return See {@link binarySearch2}\n */\nexport function binarySearch<T>(array: ReadonlyArray<T>, key: T, comparator: (op1: T, op2: T) => number): number {\n\treturn binarySearch2(array.length, i => comparator(array[i], key));\n}\n\n/**\n * Performs a binary search algorithm over a sorted collection. Useful for cases\n * when we need to perform a binary search over something that isn't actually an\n * array, and converting data to an array would defeat the use of binary search\n * in the first place.\n *\n * @param length The collection length.\n * @param compareToKey A function that takes an index of an element in the\n *   collection and returns zero if the value at this index is equal to the\n *   search key, a negative number if the value precedes the search key in the\n *   sorting order, or a positive number if the search key precedes the value.\n * @return A non-negative index of an element, if found. If not found, the\n *   result is -(n+1) (or ~n, using bitwise notation), where n is the index\n *   where the key should be inserted to maintain the sorting order.\n */\nexport function binarySearch2(length: number, compareToKey: (index: number) => number): number {\n\tlet low = 0,\n\t\thigh = length - 1;\n\n\twhile (low <= high) {\n\t\tconst mid = ((low + high) / 2) | 0;\n\t\tconst comp = compareToKey(mid);\n\t\tif (comp < 0) {\n\t\t\tlow = mid + 1;\n\t\t} else if (comp > 0) {\n\t\t\thigh = mid - 1;\n\t\t} else {\n\t\t\treturn mid;\n\t\t}\n\t}\n\treturn -(low + 1);\n}\n\ntype Compare<T> = (a: T, b: T) => number;\n\n\nexport function quickSelect<T>(nth: number, data: T[], compare: Compare<T>): T {\n\n\tnth = nth | 0;\n\n\tif (nth >= data.length) {\n\t\tthrow new TypeError('invalid index');\n\t}\n\n\tconst pivotValue = data[Math.floor(data.length * Math.random())];\n\tconst lower: T[] = [];\n\tconst higher: T[] = [];\n\tconst pivots: T[] = [];\n\n\tfor (const value of data) {\n\t\tconst val = compare(value, pivotValue);\n\t\tif (val < 0) {\n\t\t\tlower.push(value);\n\t\t} else if (val > 0) {\n\t\t\thigher.push(value);\n\t\t} else {\n\t\t\tpivots.push(value);\n\t\t}\n\t}\n\n\tif (nth < lower.length) {\n\t\treturn quickSelect(nth, lower, compare);\n\t} else if (nth < lower.length + pivots.length) {\n\t\treturn pivots[0];\n\t} else {\n\t\treturn quickSelect(nth - (lower.length + pivots.length), higher, compare);\n\t}\n}\n\nexport function groupBy<T>(data: ReadonlyArray<T>, compare: (a: T, b: T) => number): T[][] {\n\tconst result: T[][] = [];\n\tlet currentGroup: T[] | undefined = undefined;\n\tfor (const element of data.slice(0).sort(compare)) {\n\t\tif (!currentGroup || compare(currentGroup[0], element) !== 0) {\n\t\t\tcurrentGroup = [element];\n\t\t\tresult.push(currentGroup);\n\t\t} else {\n\t\t\tcurrentGroup.push(element);\n\t\t}\n\t}\n\treturn result;\n}\n\n/**\n * Splits the given items into a list of (non-empty) groups.\n * `shouldBeGrouped` is used to decide if two consecutive items should be in the same group.\n * The order of the items is preserved.\n */\nexport function* groupAdjacentBy<T>(items: Iterable<T>, shouldBeGrouped: (item1: T, item2: T) => boolean): Iterable<T[]> {\n\tlet currentGroup: T[] | undefined;\n\tlet last: T | undefined;\n\tfor (const item of items) {\n\t\tif (last !== undefined && shouldBeGrouped(last, item)) {\n\t\t\tcurrentGroup!.push(item);\n\t\t} else {\n\t\t\tif (currentGroup) {\n\t\t\t\tyield currentGroup;\n\t\t\t}\n\t\t\tcurrentGroup = [item];\n\t\t}\n\t\tlast = item;\n\t}\n\tif (currentGroup) {\n\t\tyield currentGroup;\n\t}\n}\n\nexport function forEachAdjacent<T>(arr: T[], f: (item1: T | undefined, item2: T | undefined) => void): void {\n\tfor (let i = 0; i <= arr.length; i++) {\n\t\tf(i === 0 ? undefined : arr[i - 1], i === arr.length ? undefined : arr[i]);\n\t}\n}\n\nexport function forEachWithNeighbors<T>(arr: T[], f: (before: T | undefined, element: T, after: T | undefined) => void): void {\n\tfor (let i = 0; i < arr.length; i++) {\n\t\tf(i === 0 ? undefined : arr[i - 1], arr[i], i + 1 === arr.length ? undefined : arr[i + 1]);\n\t}\n}\n\ninterface IMutableSplice<T> extends ISplice<T> {\n\treadonly toInsert: T[];\n\tdeleteCount: number;\n}\n\n/**\n * Diffs two *sorted* arrays and computes the splices which apply the diff.\n */\nexport function sortedDiff<T>(before: ReadonlyArray<T>, after: ReadonlyArray<T>, compare: (a: T, b: T) => number): ISplice<T>[] {\n\tconst result: IMutableSplice<T>[] = [];\n\n\tfunction pushSplice(start: number, deleteCount: number, toInsert: T[]): void {\n\t\tif (deleteCount === 0 && toInsert.length === 0) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst latest = result[result.length - 1];\n\n\t\tif (latest && latest.start + latest.deleteCount === start) {\n\t\t\tlatest.deleteCount += deleteCount;\n\t\t\tlatest.toInsert.push(...toInsert);\n\t\t} else {\n\t\t\tresult.push({ start, deleteCount, toInsert });\n\t\t}\n\t}\n\n\tlet beforeIdx = 0;\n\tlet afterIdx = 0;\n\n\twhile (true) {\n\t\tif (beforeIdx === before.length) {\n\t\t\tpushSplice(beforeIdx, 0, after.slice(afterIdx));\n\t\t\tbreak;\n\t\t}\n\t\tif (afterIdx === after.length) {\n\t\t\tpushSplice(beforeIdx, before.length - beforeIdx, []);\n\t\t\tbreak;\n\t\t}\n\n\t\tconst beforeElement = before[beforeIdx];\n\t\tconst afterElement = after[afterIdx];\n\t\tconst n = compare(beforeElement, afterElement);\n\t\tif (n === 0) {\n\t\t\t// equal\n\t\t\tbeforeIdx += 1;\n\t\t\tafterIdx += 1;\n\t\t} else if (n < 0) {\n\t\t\t// beforeElement is smaller -> before element removed\n\t\t\tpushSplice(beforeIdx, 1, []);\n\t\t\tbeforeIdx += 1;\n\t\t} else if (n > 0) {\n\t\t\t// beforeElement is greater -> after element added\n\t\t\tpushSplice(beforeIdx, 0, [afterElement]);\n\t\t\tafterIdx += 1;\n\t\t}\n\t}\n\n\treturn result;\n}\n\n/**\n * Takes two *sorted* arrays and computes their delta (removed, added elements).\n * Finishes in `Math.min(before.length, after.length)` steps.\n */\nexport function delta<T>(before: ReadonlyArray<T>, after: ReadonlyArray<T>, compare: (a: T, b: T) => number): { removed: T[]; added: T[] } {\n\tconst splices = sortedDiff(before, after, compare);\n\tconst removed: T[] = [];\n\tconst added: T[] = [];\n\n\tfor (const splice of splices) {\n\t\tremoved.push(...before.slice(splice.start, splice.start + splice.deleteCount));\n\t\tadded.push(...splice.toInsert);\n\t}\n\n\treturn { removed, added };\n}\n\n/**\n * Returns the top N elements from the array.\n *\n * Faster than sorting the entire array when the array is a lot larger than N.\n *\n * @param array The unsorted array.\n * @param compare A sort function for the elements.\n * @param n The number of elements to return.\n * @return The first n elements from array when sorted with compare.\n */\nexport function top<T>(array: ReadonlyArray<T>, compare: (a: T, b: T) => number, n: number): T[] {\n\tif (n === 0) {\n\t\treturn [];\n\t}\n\tconst result = array.slice(0, n).sort(compare);\n\ttopStep(array, compare, result, n, array.length);\n\treturn result;\n}\n\n/**\n * Asynchronous variant of `top()` allowing for splitting up work in batches between which the event loop can run.\n *\n * Returns the top N elements from the array.\n *\n * Faster than sorting the entire array when the array is a lot larger than N.\n *\n * @param array The unsorted array.\n * @param compare A sort function for the elements.\n * @param n The number of elements to return.\n * @param batch The number of elements to examine before yielding to the event loop.\n * @return The first n elements from array when sorted with compare.\n */\nexport function topAsync<T>(array: T[], compare: (a: T, b: T) => number, n: number, batch: number, token?: CancellationToken): Promise<T[]> {\n\tif (n === 0) {\n\t\treturn Promise.resolve([]);\n\t}\n\n\treturn new Promise((resolve, reject) => {\n\t\t(async () => {\n\t\t\tconst o = array.length;\n\t\t\tconst result = array.slice(0, n).sort(compare);\n\t\t\tfor (let i = n, m = Math.min(n + batch, o); i < o; i = m, m = Math.min(m + batch, o)) {\n\t\t\t\tif (i > n) {\n\t\t\t\t\tawait new Promise(resolve => setTimeout(resolve)); // any other delay function would starve I/O\n\t\t\t\t}\n\t\t\t\tif (token && token.isCancellationRequested) {\n\t\t\t\t\tthrow new CancellationError();\n\t\t\t\t}\n\t\t\t\ttopStep(array, compare, result, i, m);\n\t\t\t}\n\t\t\treturn result;\n\t\t})()\n\t\t\t.then(resolve, reject);\n\t});\n}\n\nfunction topStep<T>(array: ReadonlyArray<T>, compare: (a: T, b: T) => number, result: T[], i: number, m: number): void {\n\tfor (const n = result.length; i < m; i++) {\n\t\tconst element = array[i];\n\t\tif (compare(element, result[n - 1]) < 0) {\n\t\t\tresult.pop();\n\t\t\tconst j = findFirstIdxMonotonousOrArrLen(result, e => compare(element, e) < 0);\n\t\t\tresult.splice(j, 0, element);\n\t\t}\n\t}\n}\n\n/**\n * @returns New array with all falsy values removed. The original array IS NOT modified.\n */\nexport function coalesce<T>(array: ReadonlyArray<T | undefined | null>): T[] {\n\treturn array.filter((e): e is T => !!e);\n}\n\n/**\n * Remove all falsy values from `array`. The original array IS modified.\n */\nexport function coalesceInPlace<T>(array: Array<T | undefined | null>): asserts array is Array<T> {\n\tlet to = 0;\n\tfor (let i = 0; i < array.length; i++) {\n\t\tif (!!array[i]) {\n\t\t\tarray[to] = array[i];\n\t\t\tto += 1;\n\t\t}\n\t}\n\tarray.length = to;\n}\n\n/**\n * @deprecated Use `Array.copyWithin` instead\n */\nexport function move(array: any[], from: number, to: number): void {\n\tarray.splice(to, 0, array.splice(from, 1)[0]);\n}\n\n/**\n * @returns false if the provided object is an array and not empty.\n */\nexport function isFalsyOrEmpty(obj: any): boolean {\n\treturn !Array.isArray(obj) || obj.length === 0;\n}\n\n/**\n * @returns True if the provided object is an array and has at least one element.\n */\nexport function isNonEmptyArray<T>(obj: T[] | undefined | null): obj is T[];\nexport function isNonEmptyArray<T>(obj: readonly T[] | undefined | null): obj is readonly T[];\nexport function isNonEmptyArray<T>(obj: T[] | readonly T[] | undefined | null): obj is T[] | readonly T[] {\n\treturn Array.isArray(obj) && obj.length > 0;\n}\n\n/**\n * Removes duplicates from the given array. The optional keyFn allows to specify\n * how elements are checked for equality by returning an alternate value for each.\n */\nexport function distinct<T>(array: ReadonlyArray<T>, keyFn: (value: T) => any = value => value): T[] {\n\tconst seen = new Set<any>();\n\n\treturn array.filter(element => {\n\t\tconst key = keyFn!(element);\n\t\tif (seen.has(key)) {\n\t\t\treturn false;\n\t\t}\n\t\tseen.add(key);\n\t\treturn true;\n\t});\n}\n\nexport function uniqueFilter<T, R>(keyFn: (t: T) => R): (t: T) => boolean {\n\tconst seen = new Set<R>();\n\n\treturn element => {\n\t\tconst key = keyFn(element);\n\n\t\tif (seen.has(key)) {\n\t\t\treturn false;\n\t\t}\n\n\t\tseen.add(key);\n\t\treturn true;\n\t};\n}\n\nexport function firstOrDefault<T, NotFound = T>(array: ReadonlyArray<T>, notFoundValue: NotFound): T | NotFound;\nexport function firstOrDefault<T>(array: ReadonlyArray<T>): T | undefined;\nexport function firstOrDefault<T, NotFound = T>(array: ReadonlyArray<T>, notFoundValue?: NotFound): T | NotFound | undefined {\n\treturn array.length > 0 ? array[0] : notFoundValue;\n}\n\nexport function lastOrDefault<T, NotFound = T>(array: ReadonlyArray<T>, notFoundValue: NotFound): T | NotFound;\nexport function lastOrDefault<T>(array: ReadonlyArray<T>): T | undefined;\nexport function lastOrDefault<T, NotFound = T>(array: ReadonlyArray<T>, notFoundValue?: NotFound): T | NotFound | undefined {\n\treturn array.length > 0 ? array[array.length - 1] : notFoundValue;\n}\n\nexport function commonPrefixLength<T>(one: ReadonlyArray<T>, other: ReadonlyArray<T>, equals: (a: T, b: T) => boolean = (a, b) => a === b): number {\n\tlet result = 0;\n\n\tfor (let i = 0, len = Math.min(one.length, other.length); i < len && equals(one[i], other[i]); i++) {\n\t\tresult++;\n\t}\n\n\treturn result;\n}\n\nexport function range(to: number): number[];\nexport function range(from: number, to: number): number[];\nexport function range(arg: number, to?: number): number[] {\n\tlet from = typeof to === 'number' ? arg : 0;\n\n\tif (typeof to === 'number') {\n\t\tfrom = arg;\n\t} else {\n\t\tfrom = 0;\n\t\tto = arg;\n\t}\n\n\tconst result: number[] = [];\n\n\tif (from <= to) {\n\t\tfor (let i = from; i < to; i++) {\n\t\t\tresult.push(i);\n\t\t}\n\t} else {\n\t\tfor (let i = from; i > to; i--) {\n\t\t\tresult.push(i);\n\t\t}\n\t}\n\n\treturn result;\n}\n\nexport function index<T>(array: ReadonlyArray<T>, indexer: (t: T) => string): { [key: string]: T };\nexport function index<T, R>(array: ReadonlyArray<T>, indexer: (t: T) => string, mapper: (t: T) => R): { [key: string]: R };\nexport function index<T, R>(array: ReadonlyArray<T>, indexer: (t: T) => string, mapper?: (t: T) => R): { [key: string]: R } {\n\treturn array.reduce((r, t) => {\n\t\tr[indexer(t)] = mapper ? mapper(t) : t;\n\t\treturn r;\n\t}, Object.create(null));\n}\n\n/**\n * Inserts an element into an array. Returns a function which, when\n * called, will remove that element from the array.\n *\n * @deprecated In almost all cases, use a `Set<T>` instead.\n */\nexport function insert<T>(array: T[], element: T): () => void {\n\tarray.push(element);\n\n\treturn () => remove(array, element);\n}\n\n/**\n * Removes an element from an array if it can be found.\n *\n * @deprecated In almost all cases, use a `Set<T>` instead.\n */\nexport function remove<T>(array: T[], element: T): T | undefined {\n\tconst index = array.indexOf(element);\n\tif (index > -1) {\n\t\tarray.splice(index, 1);\n\n\t\treturn element;\n\t}\n\n\treturn undefined;\n}\n\n/**\n * Insert `insertArr` inside `target` at `insertIndex`.\n * Please don't touch unless you understand https://jsperf.com/inserting-an-array-within-an-array\n */\nexport function arrayInsert<T>(target: T[], insertIndex: number, insertArr: T[]): T[] {\n\tconst before = target.slice(0, insertIndex);\n\tconst after = target.slice(insertIndex);\n\treturn before.concat(insertArr, after);\n}\n\n/**\n * Uses Fisher-Yates shuffle to shuffle the given array\n */\nexport function shuffle<T>(array: T[], _seed?: number): void {\n\tlet rand: () => number;\n\n\tif (typeof _seed === 'number') {\n\t\tlet seed = _seed;\n\t\t// Seeded random number generator in JS. Modified from:\n\t\t// https://stackoverflow.com/questions/521295/seeding-the-random-number-generator-in-javascript\n\t\trand = () => {\n\t\t\tconst x = Math.sin(seed++) * 179426549; // throw away most significant digits and reduce any potential bias\n\t\t\treturn x - Math.floor(x);\n\t\t};\n\t} else {\n\t\trand = Math.random;\n\t}\n\n\tfor (let i = array.length - 1; i > 0; i -= 1) {\n\t\tconst j = Math.floor(rand() * (i + 1));\n\t\tconst temp = array[i];\n\t\tarray[i] = array[j];\n\t\tarray[j] = temp;\n\t}\n}\n\n/**\n * Pushes an element to the start of the array, if found.\n */\nexport function pushToStart<T>(arr: T[], value: T): void {\n\tconst index = arr.indexOf(value);\n\n\tif (index > -1) {\n\t\tarr.splice(index, 1);\n\t\tarr.unshift(value);\n\t}\n}\n\n/**\n * Pushes an element to the end of the array, if found.\n */\nexport function pushToEnd<T>(arr: T[], value: T): void {\n\tconst index = arr.indexOf(value);\n\n\tif (index > -1) {\n\t\tarr.splice(index, 1);\n\t\tarr.push(value);\n\t}\n}\n\nexport function pushMany<T>(arr: T[], items: ReadonlyArray<T>): void {\n\tfor (const item of items) {\n\t\tarr.push(item);\n\t}\n}\n\nexport function mapArrayOrNot<T, U>(items: T | T[], fn: (_: T) => U): U | U[] {\n\treturn Array.isArray(items) ?\n\t\titems.map(fn) :\n\t\tfn(items);\n}\n\nexport function asArray<T>(x: T | T[]): T[];\nexport function asArray<T>(x: T | readonly T[]): readonly T[];\nexport function asArray<T>(x: T | T[]): T[] {\n\treturn Array.isArray(x) ? x : [x];\n}\n\nexport function getRandomElement<T>(arr: T[]): T | undefined {\n\treturn arr[Math.floor(Math.random() * arr.length)];\n}\n\n/**\n * Insert the new items in the array.\n * @param array The original array.\n * @param start The zero-based location in the array from which to start inserting elements.\n * @param newItems The items to be inserted\n */\nexport function insertInto<T>(array: T[], start: number, newItems: T[]): void {\n\tconst startIdx = getActualStartIndex(array, start);\n\tconst originalLength = array.length;\n\tconst newItemsLength = newItems.length;\n\tarray.length = originalLength + newItemsLength;\n\t// Move the items after the start index, start from the end so that we don't overwrite any value.\n\tfor (let i = originalLength - 1; i >= startIdx; i--) {\n\t\tarray[i + newItemsLength] = array[i];\n\t}\n\n\tfor (let i = 0; i < newItemsLength; i++) {\n\t\tarray[i + startIdx] = newItems[i];\n\t}\n}\n\n/**\n * Removes elements from an array and inserts new elements in their place, returning the deleted elements. Alternative to the native Array.splice method, it\n * can only support limited number of items due to the maximum call stack size limit.\n * @param array The original array.\n * @param start The zero-based location in the array from which to start removing elements.\n * @param deleteCount The number of elements to remove.\n * @returns An array containing the elements that were deleted.\n */\nexport function splice<T>(array: T[], start: number, deleteCount: number, newItems: T[]): T[] {\n\tconst index = getActualStartIndex(array, start);\n\tlet result = array.splice(index, deleteCount);\n\tif (result === undefined) {\n\t\t// see https://bugs.webkit.org/show_bug.cgi?id=261140\n\t\tresult = [];\n\t}\n\tinsertInto(array, index, newItems);\n\treturn result;\n}\n\n/**\n * Determine the actual start index (same logic as the native splice() or slice())\n * If greater than the length of the array, start will be set to the length of the array. In this case, no element will be deleted but the method will behave as an adding function, adding as many element as item[n*] provided.\n * If negative, it will begin that many elements from the end of the array. (In this case, the origin -1, meaning -n is the index of the nth last element, and is therefore equivalent to the index of array.length - n.) If array.length + start is less than 0, it will begin from index 0.\n * @param array The target array.\n * @param start The operation index.\n */\nfunction getActualStartIndex<T>(array: T[], start: number): number {\n\treturn start < 0 ? Math.max(start + array.length, 0) : Math.min(start, array.length);\n}\n\n/**\n * When comparing two values,\n * a negative number indicates that the first value is less than the second,\n * a positive number indicates that the first value is greater than the second,\n * and zero indicates that neither is the case.\n*/\nexport type CompareResult = number;\n\nexport namespace CompareResult {\n\texport function isLessThan(result: CompareResult): boolean {\n\t\treturn result < 0;\n\t}\n\n\texport function isLessThanOrEqual(result: CompareResult): boolean {\n\t\treturn result <= 0;\n\t}\n\n\texport function isGreaterThan(result: CompareResult): boolean {\n\t\treturn result > 0;\n\t}\n\n\texport function isNeitherLessOrGreaterThan(result: CompareResult): boolean {\n\t\treturn result === 0;\n\t}\n\n\texport const greaterThan = 1;\n\texport const lessThan = -1;\n\texport const neitherLessOrGreaterThan = 0;\n}\n\n/**\n * A comparator `c` defines a total order `<=` on `T` as following:\n * `c(a, b) <= 0` iff `a` <= `b`.\n * We also have `c(a, b) == 0` iff `c(b, a) == 0`.\n*/\nexport type Comparator<T> = (a: T, b: T) => CompareResult;\n\nexport function compareBy<TItem, TCompareBy>(selector: (item: TItem) => TCompareBy, comparator: Comparator<TCompareBy>): Comparator<TItem> {\n\treturn (a, b) => comparator(selector(a), selector(b));\n}\n\nexport function tieBreakComparators<TItem>(...comparators: Comparator<TItem>[]): Comparator<TItem> {\n\treturn (item1, item2) => {\n\t\tfor (const comparator of comparators) {\n\t\t\tconst result = comparator(item1, item2);\n\t\t\tif (!CompareResult.isNeitherLessOrGreaterThan(result)) {\n\t\t\t\treturn result;\n\t\t\t}\n\t\t}\n\t\treturn CompareResult.neitherLessOrGreaterThan;\n\t};\n}\n\n/**\n * The natural order on numbers.\n*/\nexport const numberComparator: Comparator<number> = (a, b) => a - b;\n\nexport const booleanComparator: Comparator<boolean> = (a, b) => numberComparator(a ? 1 : 0, b ? 1 : 0);\n\nexport function reverseOrder<TItem>(comparator: Comparator<TItem>): Comparator<TItem> {\n\treturn (a, b) => -comparator(a, b);\n}\n\nexport class ArrayQueue<T> {\n\tprivate firstIdx = 0;\n\tprivate lastIdx = this.items.length - 1;\n\n\t/**\n\t * Constructs a queue that is backed by the given array. Runtime is O(1).\n\t*/\n\tconstructor(private readonly items: readonly T[]) { }\n\n\tget length(): number {\n\t\treturn this.lastIdx - this.firstIdx + 1;\n\t}\n\n\t/**\n\t * Consumes elements from the beginning of the queue as long as the predicate returns true.\n\t * If no elements were consumed, `null` is returned. Has a runtime of O(result.length).\n\t*/\n\ttakeWhile(predicate: (value: T) => boolean): T[] | null {\n\t\t// P(k) := k <= this.lastIdx && predicate(this.items[k])\n\t\t// Find s := min { k | k >= this.firstIdx && !P(k) } and return this.data[this.firstIdx...s)\n\n\t\tlet startIdx = this.firstIdx;\n\t\twhile (startIdx < this.items.length && predicate(this.items[startIdx])) {\n\t\t\tstartIdx++;\n\t\t}\n\t\tconst result = startIdx === this.firstIdx ? null : this.items.slice(this.firstIdx, startIdx);\n\t\tthis.firstIdx = startIdx;\n\t\treturn result;\n\t}\n\n\t/**\n\t * Consumes elements from the end of the queue as long as the predicate returns true.\n\t * If no elements were consumed, `null` is returned.\n\t * The result has the same order as the underlying array!\n\t*/\n\ttakeFromEndWhile(predicate: (value: T) => boolean): T[] | null {\n\t\t// P(k) := this.firstIdx >= k && predicate(this.items[k])\n\t\t// Find s := max { k | k <= this.lastIdx && !P(k) } and return this.data(s...this.lastIdx]\n\n\t\tlet endIdx = this.lastIdx;\n\t\twhile (endIdx >= 0 && predicate(this.items[endIdx])) {\n\t\t\tendIdx--;\n\t\t}\n\t\tconst result = endIdx === this.lastIdx ? null : this.items.slice(endIdx + 1, this.lastIdx + 1);\n\t\tthis.lastIdx = endIdx;\n\t\treturn result;\n\t}\n\n\tpeek(): T | undefined {\n\t\tif (this.length === 0) {\n\t\t\treturn undefined;\n\t\t}\n\t\treturn this.items[this.firstIdx];\n\t}\n\n\tpeekLast(): T | undefined {\n\t\tif (this.length === 0) {\n\t\t\treturn undefined;\n\t\t}\n\t\treturn this.items[this.lastIdx];\n\t}\n\n\tdequeue(): T | undefined {\n\t\tconst result = this.items[this.firstIdx];\n\t\tthis.firstIdx++;\n\t\treturn result;\n\t}\n\n\tremoveLast(): T | undefined {\n\t\tconst result = this.items[this.lastIdx];\n\t\tthis.lastIdx--;\n\t\treturn result;\n\t}\n\n\ttakeCount(count: number): T[] {\n\t\tconst result = this.items.slice(this.firstIdx, this.firstIdx + count);\n\t\tthis.firstIdx += count;\n\t\treturn result;\n\t}\n}\n\n/**\n * This class is faster than an iterator and array for lazy computed data.\n*/\nexport class CallbackIterable<T> {\n\tpublic static readonly empty = new CallbackIterable<never>(_callback => { });\n\n\tconstructor(\n\t\t/**\n\t\t * Calls the callback for every item.\n\t\t * Stops when the callback returns false.\n\t\t*/\n\t\tpublic readonly iterate: (callback: (item: T) => boolean) => void\n\t) {\n\t}\n\n\tforEach(handler: (item: T) => void) {\n\t\tthis.iterate(item => { handler(item); return true; });\n\t}\n\n\ttoArray(): T[] {\n\t\tconst result: T[] = [];\n\t\tthis.iterate(item => { result.push(item); return true; });\n\t\treturn result;\n\t}\n\n\tfilter(predicate: (item: T) => boolean): CallbackIterable<T> {\n\t\treturn new CallbackIterable(cb => this.iterate(item => predicate(item) ? cb(item) : true));\n\t}\n\n\tmap<TResult>(mapFn: (item: T) => TResult): CallbackIterable<TResult> {\n\t\treturn new CallbackIterable<TResult>(cb => this.iterate(item => cb(mapFn(item))));\n\t}\n\n\tsome(predicate: (item: T) => boolean): boolean {\n\t\tlet result = false;\n\t\tthis.iterate(item => { result = predicate(item); return !result; });\n\t\treturn result;\n\t}\n\n\tfindFirst(predicate: (item: T) => boolean): T | undefined {\n\t\tlet result: T | undefined;\n\t\tthis.iterate(item => {\n\t\t\tif (predicate(item)) {\n\t\t\t\tresult = item;\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\treturn true;\n\t\t});\n\t\treturn result;\n\t}\n\n\tfindLast(predicate: (item: T) => boolean): T | undefined {\n\t\tlet result: T | undefined;\n\t\tthis.iterate(item => {\n\t\t\tif (predicate(item)) {\n\t\t\t\tresult = item;\n\t\t\t}\n\t\t\treturn true;\n\t\t});\n\t\treturn result;\n\t}\n\n\tfindLastMaxBy(comparator: Comparator<T>): T | undefined {\n\t\tlet result: T | undefined;\n\t\tlet first = true;\n\t\tthis.iterate(item => {\n\t\t\tif (first || CompareResult.isGreaterThan(comparator(item, result!))) {\n\t\t\t\tfirst = false;\n\t\t\t\tresult = item;\n\t\t\t}\n\t\t\treturn true;\n\t\t});\n\t\treturn result;\n\t}\n}\n\n/**\n * Represents a re-arrangement of items in an array.\n */\nexport class Permutation {\n\tconstructor(private readonly _indexMap: readonly number[]) { }\n\n\t/**\n\t * Returns a permutation that sorts the given array according to the given compare function.\n\t */\n\tpublic static createSortPermutation<T>(arr: readonly T[], compareFn: (a: T, b: T) => number): Permutation {\n\t\tconst sortIndices = Array.from(arr.keys()).sort((index1, index2) => compareFn(arr[index1], arr[index2]));\n\t\treturn new Permutation(sortIndices);\n\t}\n\n\t/**\n\t * Returns a new array with the elements of the given array re-arranged according to this permutation.\n\t */\n\tapply<T>(arr: readonly T[]): T[] {\n\t\treturn arr.map((_, index) => arr[this._indexMap[index]]);\n\t}\n\n\t/**\n\t * Returns a new permutation that undoes the re-arrangement of this permutation.\n\t*/\n\tinverse(): Permutation {\n\t\tconst inverseIndexMap = this._indexMap.slice();\n\t\tfor (let i = 0; i < this._indexMap.length; i++) {\n\t\t\tinverseIndexMap[this._indexMap[i]] = i;\n\t\t}\n\t\treturn new Permutation(inverseIndexMap);\n\t}\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n/**\n * An interface for a JavaScript object that\n * acts a dictionary. The keys are strings.\n */\nexport type IStringDictionary<V> = Record<string, V>;\n\n/**\n * An interface for a JavaScript object that\n * acts a dictionary. The keys are numbers.\n */\nexport type INumberDictionary<V> = Record<number, V>;\n\n/**\n * Groups the collection into a dictionary based on the provided\n * group function.\n */\nexport function groupBy<K extends string | number | symbol, V>(data: V[], groupFn: (element: V) => K): Record<K, V[]> {\n\tconst result: Record<K, V[]> = Object.create(null);\n\tfor (const element of data) {\n\t\tconst key = groupFn(element);\n\t\tlet target = result[key];\n\t\tif (!target) {\n\t\t\ttarget = result[key] = [];\n\t\t}\n\t\ttarget.push(element);\n\t}\n\treturn result;\n}\n\nexport function diffSets<T>(before: Set<T>, after: Set<T>): { removed: T[]; added: T[] } {\n\tconst removed: T[] = [];\n\tconst added: T[] = [];\n\tfor (const element of before) {\n\t\tif (!after.has(element)) {\n\t\t\tremoved.push(element);\n\t\t}\n\t}\n\tfor (const element of after) {\n\t\tif (!before.has(element)) {\n\t\t\tadded.push(element);\n\t\t}\n\t}\n\treturn { removed, added };\n}\n\nexport function diffMaps<K, V>(before: Map<K, V>, after: Map<K, V>): { removed: V[]; added: V[] } {\n\tconst removed: V[] = [];\n\tconst added: V[] = [];\n\tfor (const [index, value] of before) {\n\t\tif (!after.has(index)) {\n\t\t\tremoved.push(value);\n\t\t}\n\t}\n\tfor (const [index, value] of after) {\n\t\tif (!before.has(index)) {\n\t\t\tadded.push(value);\n\t\t}\n\t}\n\treturn { removed, added };\n}\n\n/**\n * Computes the intersection of two sets.\n *\n * @param setA - The first set.\n * @param setB - The second iterable.\n * @returns A new set containing the elements that are in both `setA` and `setB`.\n */\nexport function intersection<T>(setA: Set<T>, setB: Iterable<T>): Set<T> {\n\tconst result = new Set<T>();\n\tfor (const elem of setB) {\n\t\tif (setA.has(elem)) {\n\t\t\tresult.add(elem);\n\t\t}\n\t}\n\treturn result;\n}\n\nexport class SetWithKey<T> implements Set<T> {\n\tprivate _map = new Map<any, T>();\n\n\tconstructor(values: T[], private toKey: (t: T) => any) {\n\t\tfor (const value of values) {\n\t\t\tthis.add(value);\n\t\t}\n\t}\n\n\tget size(): number {\n\t\treturn this._map.size;\n\t}\n\n\tadd(value: T): this {\n\t\tconst key = this.toKey(value);\n\t\tthis._map.set(key, value);\n\t\treturn this;\n\t}\n\n\tdelete(value: T): boolean {\n\t\treturn this._map.delete(this.toKey(value));\n\t}\n\n\thas(value: T): boolean {\n\t\treturn this._map.has(this.toKey(value));\n\t}\n\n\t*entries(): IterableIterator<[T, T]> {\n\t\tfor (const entry of this._map.values()) {\n\t\t\tyield [entry, entry];\n\t\t}\n\t}\n\n\tkeys(): IterableIterator<T> {\n\t\treturn this.values();\n\t}\n\n\t*values(): IterableIterator<T> {\n\t\tfor (const entry of this._map.values()) {\n\t\t\tyield entry;\n\t\t}\n\t}\n\n\tclear(): void {\n\t\tthis._map.clear();\n\t}\n\n\tforEach(callbackfn: (value: T, value2: T, set: Set<T>) => void, thisArg?: any): void {\n\t\tthis._map.forEach(entry => callbackfn.call(thisArg, entry, entry, this));\n\t}\n\n\t[Symbol.iterator](): IterableIterator<T> {\n\t\treturn this.values();\n\t}\n\n\t[Symbol.toStringTag]: string = 'SetWithKey';\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nexport function getOrSet<K, V>(map: Map<K, V>, key: K, value: V): V {\n\tlet result = map.get(key);\n\tif (result === undefined) {\n\t\tresult = value;\n\t\tmap.set(key, result);\n\t}\n\n\treturn result;\n}\n\nexport function mapToString<K, V>(map: Map<K, V>): string {\n\tconst entries: string[] = [];\n\tmap.forEach((value, key) => {\n\t\tentries.push(`${key} => ${value}`);\n\t});\n\n\treturn `Map(${map.size}) {${entries.join(', ')}}`;\n}\n\nexport function setToString<K>(set: Set<K>): string {\n\tconst entries: K[] = [];\n\tset.forEach(value => {\n\t\tentries.push(value);\n\t});\n\n\treturn `Set(${set.size}) {${entries.join(', ')}}`;\n}\n\nexport const enum Touch {\n\tNone = 0,\n\tAsOld = 1,\n\tAsNew = 2\n}\n\nexport class CounterSet<T> {\n\n\tprivate map = new Map<T, number>();\n\n\tadd(value: T): CounterSet<T> {\n\t\tthis.map.set(value, (this.map.get(value) || 0) + 1);\n\t\treturn this;\n\t}\n\n\tdelete(value: T): boolean {\n\t\tlet counter = this.map.get(value) || 0;\n\n\t\tif (counter === 0) {\n\t\t\treturn false;\n\t\t}\n\n\t\tcounter--;\n\n\t\tif (counter === 0) {\n\t\t\tthis.map.delete(value);\n\t\t} else {\n\t\t\tthis.map.set(value, counter);\n\t\t}\n\n\t\treturn true;\n\t}\n\n\thas(value: T): boolean {\n\t\treturn this.map.has(value);\n\t}\n}\n\n/**\n * A map that allows access both by keys and values.\n * **NOTE**: values need to be unique.\n */\nexport class BidirectionalMap<K, V> {\n\n\tprivate readonly _m1 = new Map<K, V>();\n\tprivate readonly _m2 = new Map<V, K>();\n\n\tconstructor(entries?: readonly (readonly [K, V])[]) {\n\t\tif (entries) {\n\t\t\tfor (const [key, value] of entries) {\n\t\t\t\tthis.set(key, value);\n\t\t\t}\n\t\t}\n\t}\n\n\tclear(): void {\n\t\tthis._m1.clear();\n\t\tthis._m2.clear();\n\t}\n\n\tset(key: K, value: V): void {\n\t\tthis._m1.set(key, value);\n\t\tthis._m2.set(value, key);\n\t}\n\n\tget(key: K): V | undefined {\n\t\treturn this._m1.get(key);\n\t}\n\n\tgetKey(value: V): K | undefined {\n\t\treturn this._m2.get(value);\n\t}\n\n\tdelete(key: K): boolean {\n\t\tconst value = this._m1.get(key);\n\t\tif (value === undefined) {\n\t\t\treturn false;\n\t\t}\n\t\tthis._m1.delete(key);\n\t\tthis._m2.delete(value);\n\t\treturn true;\n\t}\n\n\tforEach(callbackfn: (value: V, key: K, map: BidirectionalMap<K, V>) => void, thisArg?: any): void {\n\t\tthis._m1.forEach((value, key) => {\n\t\t\tcallbackfn.call(thisArg, value, key, this);\n\t\t});\n\t}\n\n\tkeys(): IterableIterator<K> {\n\t\treturn this._m1.keys();\n\t}\n\n\tvalues(): IterableIterator<V> {\n\t\treturn this._m1.values();\n\t}\n}\n\nexport class SetMap<K, V> {\n\n\tprivate map = new Map<K, Set<V>>();\n\n\tadd(key: K, value: V): void {\n\t\tlet values = this.map.get(key);\n\n\t\tif (!values) {\n\t\t\tvalues = new Set<V>();\n\t\t\tthis.map.set(key, values);\n\t\t}\n\n\t\tvalues.add(value);\n\t}\n\n\tdelete(key: K, value: V): void {\n\t\tconst values = this.map.get(key);\n\n\t\tif (!values) {\n\t\t\treturn;\n\t\t}\n\n\t\tvalues.delete(value);\n\n\t\tif (values.size === 0) {\n\t\t\tthis.map.delete(key);\n\t\t}\n\t}\n\n\tforEach(key: K, fn: (value: V) => void): void {\n\t\tconst values = this.map.get(key);\n\n\t\tif (!values) {\n\t\t\treturn;\n\t\t}\n\n\t\tvalues.forEach(fn);\n\t}\n\n\tget(key: K): ReadonlySet<V> {\n\t\tconst values = this.map.get(key);\n\t\tif (!values) {\n\t\t\treturn new Set<V>();\n\t\t}\n\t\treturn values;\n\t}\n}\n\nexport function mapsStrictEqualIgnoreOrder(a: Map<unknown, unknown>, b: Map<unknown, unknown>): boolean {\n\tif (a === b) {\n\t\treturn true;\n\t}\n\n\tif (a.size !== b.size) {\n\t\treturn false;\n\t}\n\n\tfor (const [key, value] of a) {\n\t\tif (!b.has(key) || b.get(key) !== value) {\n\t\t\treturn false;\n\t\t}\n\t}\n\n\tfor (const [key] of b) {\n\t\tif (!a.has(key)) {\n\t\t\treturn false;\n\t\t}\n\t}\n\n\treturn true;\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nexport namespace Iterable {\n\n\texport function is<T = any>(thing: any): thing is Iterable<T> {\n\t\treturn thing && typeof thing === 'object' && typeof thing[Symbol.iterator] === 'function';\n\t}\n\n\tconst _empty: Iterable<any> = Object.freeze([]);\n\texport function empty<T = any>(): Iterable<T> {\n\t\treturn _empty;\n\t}\n\n\texport function* single<T>(element: T): Iterable<T> {\n\t\tyield element;\n\t}\n\n\texport function wrap<T>(iterableOrElement: Iterable<T> | T): Iterable<T> {\n\t\tif (is(iterableOrElement)) {\n\t\t\treturn iterableOrElement;\n\t\t} else {\n\t\t\treturn single(iterableOrElement);\n\t\t}\n\t}\n\n\texport function from<T>(iterable: Iterable<T> | undefined | null): Iterable<T> {\n\t\treturn iterable || _empty;\n\t}\n\n\texport function* reverse<T>(array: Array<T>): Iterable<T> {\n\t\tfor (let i = array.length - 1; i >= 0; i--) {\n\t\t\tyield array[i];\n\t\t}\n\t}\n\n\texport function isEmpty<T>(iterable: Iterable<T> | undefined | null): boolean {\n\t\treturn !iterable || iterable[Symbol.iterator]().next().done === true;\n\t}\n\n\texport function first<T>(iterable: Iterable<T>): T | undefined {\n\t\treturn iterable[Symbol.iterator]().next().value;\n\t}\n\n\texport function some<T>(iterable: Iterable<T>, predicate: (t: T, i: number) => unknown): boolean {\n\t\tlet i = 0;\n\t\tfor (const element of iterable) {\n\t\t\tif (predicate(element, i++)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\t\treturn false;\n\t}\n\n\texport function find<T, R extends T>(iterable: Iterable<T>, predicate: (t: T) => t is R): R | undefined;\n\texport function find<T>(iterable: Iterable<T>, predicate: (t: T) => boolean): T | undefined;\n\texport function find<T>(iterable: Iterable<T>, predicate: (t: T) => boolean): T | undefined {\n\t\tfor (const element of iterable) {\n\t\t\tif (predicate(element)) {\n\t\t\t\treturn element;\n\t\t\t}\n\t\t}\n\n\t\treturn undefined;\n\t}\n\n\texport function filter<T, R extends T>(iterable: Iterable<T>, predicate: (t: T) => t is R): Iterable<R>;\n\texport function filter<T>(iterable: Iterable<T>, predicate: (t: T) => boolean): Iterable<T>;\n\texport function* filter<T>(iterable: Iterable<T>, predicate: (t: T) => boolean): Iterable<T> {\n\t\tfor (const element of iterable) {\n\t\t\tif (predicate(element)) {\n\t\t\t\tyield element;\n\t\t\t}\n\t\t}\n\t}\n\n\texport function* map<T, R>(iterable: Iterable<T>, fn: (t: T, index: number) => R): Iterable<R> {\n\t\tlet index = 0;\n\t\tfor (const element of iterable) {\n\t\t\tyield fn(element, index++);\n\t\t}\n\t}\n\n\texport function* flatMap<T, R>(iterable: Iterable<T>, fn: (t: T, index: number) => Iterable<R>): Iterable<R> {\n\t\tlet index = 0;\n\t\tfor (const element of iterable) {\n\t\t\tyield* fn(element, index++);\n\t\t}\n\t}\n\n\texport function* concat<T>(...iterables: Iterable<T>[]): Iterable<T> {\n\t\tfor (const iterable of iterables) {\n\t\t\tyield* iterable;\n\t\t}\n\t}\n\n\texport function reduce<T, R>(iterable: Iterable<T>, reducer: (previousValue: R, currentValue: T) => R, initialValue: R): R {\n\t\tlet value = initialValue;\n\t\tfor (const element of iterable) {\n\t\t\tvalue = reducer(value, element);\n\t\t}\n\t\treturn value;\n\t}\n\n\t/**\n\t * Returns an iterable slice of the array, with the same semantics as `array.slice()`.\n\t */\n\texport function* slice<T>(arr: ReadonlyArray<T>, from: number, to = arr.length): Iterable<T> {\n\t\tif (from < 0) {\n\t\t\tfrom += arr.length;\n\t\t}\n\n\t\tif (to < 0) {\n\t\t\tto += arr.length;\n\t\t} else if (to > arr.length) {\n\t\t\tto = arr.length;\n\t\t}\n\n\t\tfor (; from < to; from++) {\n\t\t\tyield arr[from];\n\t\t}\n\t}\n\n\t/**\n\t * Consumes `atMost` elements from iterable and returns the consumed elements,\n\t * and an iterable for the rest of the elements.\n\t */\n\texport function consume<T>(iterable: Iterable<T>, atMost: number = Number.POSITIVE_INFINITY): [T[], Iterable<T>] {\n\t\tconst consumed: T[] = [];\n\n\t\tif (atMost === 0) {\n\t\t\treturn [consumed, iterable];\n\t\t}\n\n\t\tconst iterator = iterable[Symbol.iterator]();\n\n\t\tfor (let i = 0; i < atMost; i++) {\n\t\t\tconst next = iterator.next();\n\n\t\t\tif (next.done) {\n\t\t\t\treturn [consumed, Iterable.empty()];\n\t\t\t}\n\n\t\t\tconsumed.push(next.value);\n\t\t}\n\n\t\treturn [consumed, { [Symbol.iterator]() { return iterator; } }];\n\t}\n\n\texport async function asyncToArray<T>(iterable: AsyncIterable<T>): Promise<T[]> {\n\t\tconst result: T[] = [];\n\t\tfor await (const item of iterable) {\n\t\t\tresult.push(item);\n\t\t}\n\t\treturn Promise.resolve(result);\n\t}\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { compareBy, numberComparator } from 'vs/base/common/arrays';\nimport { groupBy } from 'vs/base/common/collections';\nimport { SetMap } from './map';\nimport { createSingleCallFunction } from 'vs/base/common/functional';\nimport { Iterable } from 'vs/base/common/iterator';\n\n// #region Disposable Tracking\n\n/**\n * Enables logging of potentially leaked disposables.\n *\n * A disposable is considered leaked if it is not disposed or not registered as the child of\n * another disposable. This tracking is very simple an only works for classes that either\n * extend Disposable or use a DisposableStore. This means there are a lot of false positives.\n */\nconst TRACK_DISPOSABLES = false;\nlet disposableTracker: IDisposableTracker | null = null;\n\nexport interface IDisposableTracker {\n\t/**\n\t * Is called on construction of a disposable.\n\t*/\n\ttrackDisposable(disposable: IDisposable): void;\n\n\t/**\n\t * Is called when a disposable is registered as child of another disposable (e.g. {@link DisposableStore}).\n\t * If parent is `null`, the disposable is removed from its former parent.\n\t*/\n\tsetParent(child: IDisposable, parent: IDisposable | null): void;\n\n\t/**\n\t * Is called after a disposable is disposed.\n\t*/\n\tmarkAsDisposed(disposable: IDisposable): void;\n\n\t/**\n\t * Indicates that the given object is a singleton which does not need to be disposed.\n\t*/\n\tmarkAsSingleton(disposable: IDisposable): void;\n}\n\nexport interface DisposableInfo {\n\tvalue: IDisposable;\n\tsource: string | null;\n\tparent: IDisposable | null;\n\tisSingleton: boolean;\n\tidx: number;\n}\n\nexport class DisposableTracker implements IDisposableTracker {\n\tprivate static idx = 0;\n\n\tprivate readonly livingDisposables = new Map<IDisposable, DisposableInfo>();\n\n\tprivate getDisposableData(d: IDisposable): DisposableInfo {\n\t\tlet val = this.livingDisposables.get(d);\n\t\tif (!val) {\n\t\t\tval = { parent: null, source: null, isSingleton: false, value: d, idx: DisposableTracker.idx++ };\n\t\t\tthis.livingDisposables.set(d, val);\n\t\t}\n\t\treturn val;\n\t}\n\n\ttrackDisposable(d: IDisposable): void {\n\t\tconst data = this.getDisposableData(d);\n\t\tif (!data.source) {\n\t\t\tdata.source =\n\t\t\t\tnew Error().stack!;\n\t\t}\n\t}\n\n\tsetParent(child: IDisposable, parent: IDisposable | null): void {\n\t\tconst data = this.getDisposableData(child);\n\t\tdata.parent = parent;\n\t}\n\n\tmarkAsDisposed(x: IDisposable): void {\n\t\tthis.livingDisposables.delete(x);\n\t}\n\n\tmarkAsSingleton(disposable: IDisposable): void {\n\t\tthis.getDisposableData(disposable).isSingleton = true;\n\t}\n\n\tprivate getRootParent(data: DisposableInfo, cache: Map<DisposableInfo, DisposableInfo>): DisposableInfo {\n\t\tconst cacheValue = cache.get(data);\n\t\tif (cacheValue) {\n\t\t\treturn cacheValue;\n\t\t}\n\n\t\tconst result = data.parent ? this.getRootParent(this.getDisposableData(data.parent), cache) : data;\n\t\tcache.set(data, result);\n\t\treturn result;\n\t}\n\n\tgetTrackedDisposables(): IDisposable[] {\n\t\tconst rootParentCache = new Map<DisposableInfo, DisposableInfo>();\n\n\t\tconst leaking = [...this.livingDisposables.entries()]\n\t\t\t.filter(([, v]) => v.source !== null && !this.getRootParent(v, rootParentCache).isSingleton)\n\t\t\t.flatMap(([k]) => k);\n\n\t\treturn leaking;\n\t}\n\n\tcomputeLeakingDisposables(maxReported = 10, preComputedLeaks?: DisposableInfo[]): { leaks: DisposableInfo[]; details: string } | undefined {\n\t\tlet uncoveredLeakingObjs: DisposableInfo[] | undefined;\n\t\tif (preComputedLeaks) {\n\t\t\tuncoveredLeakingObjs = preComputedLeaks;\n\t\t} else {\n\t\t\tconst rootParentCache = new Map<DisposableInfo, DisposableInfo>();\n\n\t\t\tconst leakingObjects = [...this.livingDisposables.values()]\n\t\t\t\t.filter((info) => info.source !== null && !this.getRootParent(info, rootParentCache).isSingleton);\n\n\t\t\tif (leakingObjects.length === 0) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tconst leakingObjsSet = new Set(leakingObjects.map(o => o.value));\n\n\t\t\t// Remove all objects that are a child of other leaking objects. Assumes there are no cycles.\n\t\t\tuncoveredLeakingObjs = leakingObjects.filter(l => {\n\t\t\t\treturn !(l.parent && leakingObjsSet.has(l.parent));\n\t\t\t});\n\n\t\t\tif (uncoveredLeakingObjs.length === 0) {\n\t\t\t\tthrow new Error('There are cyclic diposable chains!');\n\t\t\t}\n\t\t}\n\n\t\tif (!uncoveredLeakingObjs) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tfunction getStackTracePath(leaking: DisposableInfo): string[] {\n\t\t\tfunction removePrefix(array: string[], linesToRemove: (string | RegExp)[]) {\n\t\t\t\twhile (array.length > 0 && linesToRemove.some(regexp => typeof regexp === 'string' ? regexp === array[0] : array[0].match(regexp))) {\n\t\t\t\t\tarray.shift();\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst lines = leaking.source!.split('\\n').map(p => p.trim().replace('at ', '')).filter(l => l !== '');\n\t\t\tremovePrefix(lines, ['Error', /^trackDisposable \\(.*\\)$/, /^DisposableTracker.trackDisposable \\(.*\\)$/]);\n\t\t\treturn lines.reverse();\n\t\t}\n\n\t\tconst stackTraceStarts = new SetMap<string, DisposableInfo>();\n\t\tfor (const leaking of uncoveredLeakingObjs) {\n\t\t\tconst stackTracePath = getStackTracePath(leaking);\n\t\t\tfor (let i = 0; i <= stackTracePath.length; i++) {\n\t\t\t\tstackTraceStarts.add(stackTracePath.slice(0, i).join('\\n'), leaking);\n\t\t\t}\n\t\t}\n\n\t\t// Put earlier leaks first\n\t\tuncoveredLeakingObjs.sort(compareBy(l => l.idx, numberComparator));\n\n\t\tlet message = '';\n\n\t\tlet i = 0;\n\t\tfor (const leaking of uncoveredLeakingObjs.slice(0, maxReported)) {\n\t\t\ti++;\n\t\t\tconst stackTracePath = getStackTracePath(leaking);\n\t\t\tconst stackTraceFormattedLines = [];\n\n\t\t\tfor (let i = 0; i < stackTracePath.length; i++) {\n\t\t\t\tlet line = stackTracePath[i];\n\t\t\t\tconst starts = stackTraceStarts.get(stackTracePath.slice(0, i + 1).join('\\n'));\n\t\t\t\tline = `(shared with ${starts.size}/${uncoveredLeakingObjs.length} leaks) at ${line}`;\n\n\t\t\t\tconst prevStarts = stackTraceStarts.get(stackTracePath.slice(0, i).join('\\n'));\n\t\t\t\tconst continuations = groupBy([...prevStarts].map(d => getStackTracePath(d)[i]), v => v);\n\t\t\t\tdelete continuations[stackTracePath[i]];\n\t\t\t\tfor (const [cont, set] of Object.entries(continuations)) {\n\t\t\t\t\tstackTraceFormattedLines.unshift(`    - stacktraces of ${set.length} other leaks continue with ${cont}`);\n\t\t\t\t}\n\n\t\t\t\tstackTraceFormattedLines.unshift(line);\n\t\t\t}\n\n\t\t\tmessage += `\\n\\n\\n==================== Leaking disposable ${i}/${uncoveredLeakingObjs.length}: ${leaking.value.constructor.name} ====================\\n${stackTraceFormattedLines.join('\\n')}\\n============================================================\\n\\n`;\n\t\t}\n\n\t\tif (uncoveredLeakingObjs.length > maxReported) {\n\t\t\tmessage += `\\n\\n\\n... and ${uncoveredLeakingObjs.length - maxReported} more leaking disposables\\n\\n`;\n\t\t}\n\n\t\treturn { leaks: uncoveredLeakingObjs, details: message };\n\t}\n}\n\nexport function setDisposableTracker(tracker: IDisposableTracker | null): void {\n\tdisposableTracker = tracker;\n}\n\nif (TRACK_DISPOSABLES) {\n\tconst __is_disposable_tracked__ = '__is_disposable_tracked__';\n\tsetDisposableTracker(new class implements IDisposableTracker {\n\t\ttrackDisposable(x: IDisposable): void {\n\t\t\tconst stack = new Error('Potentially leaked disposable').stack!;\n\t\t\tsetTimeout(() => {\n\t\t\t\tif (!(x as any)[__is_disposable_tracked__]) {\n\t\t\t\t\tconsole.log(stack);\n\t\t\t\t}\n\t\t\t}, 3000);\n\t\t}\n\n\t\tsetParent(child: IDisposable, parent: IDisposable | null): void {\n\t\t\tif (child && child !== Disposable.None) {\n\t\t\t\ttry {\n\t\t\t\t\t(child as any)[__is_disposable_tracked__] = true;\n\t\t\t\t} catch {\n\t\t\t\t\t// noop\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tmarkAsDisposed(disposable: IDisposable): void {\n\t\t\tif (disposable && disposable !== Disposable.None) {\n\t\t\t\ttry {\n\t\t\t\t\t(disposable as any)[__is_disposable_tracked__] = true;\n\t\t\t\t} catch {\n\t\t\t\t\t// noop\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tmarkAsSingleton(disposable: IDisposable): void { }\n\t});\n}\n\nexport function trackDisposable<T extends IDisposable>(x: T): T {\n\tdisposableTracker?.trackDisposable(x);\n\treturn x;\n}\n\nexport function markAsDisposed(disposable: IDisposable): void {\n\tdisposableTracker?.markAsDisposed(disposable);\n}\n\nfunction setParentOfDisposable(child: IDisposable, parent: IDisposable | null): void {\n\tdisposableTracker?.setParent(child, parent);\n}\n\nfunction setParentOfDisposables(children: IDisposable[], parent: IDisposable | null): void {\n\tif (!disposableTracker) {\n\t\treturn;\n\t}\n\tfor (const child of children) {\n\t\tdisposableTracker.setParent(child, parent);\n\t}\n}\n\n/**\n * Indicates that the given object is a singleton which does not need to be disposed.\n*/\nexport function markAsSingleton<T extends IDisposable>(singleton: T): T {\n\tdisposableTracker?.markAsSingleton(singleton);\n\treturn singleton;\n}\n\n// #endregion\n\n/**\n * An object that performs a cleanup operation when `.dispose()` is called.\n *\n * Some examples of how disposables are used:\n *\n * - An event listener that removes itself when `.dispose()` is called.\n * - A resource such as a file system watcher that cleans up the resource when `.dispose()` is called.\n * - The return value from registering a provider. When `.dispose()` is called, the provider is unregistered.\n */\nexport interface IDisposable {\n\tdispose(): void;\n}\n\n/**\n * Check if `thing` is {@link IDisposable disposable}.\n */\nexport function isDisposable<E extends any>(thing: E): thing is E & IDisposable {\n\treturn typeof thing === 'object' && thing !== null && typeof (<IDisposable><any>thing).dispose === 'function' && (<IDisposable><any>thing).dispose.length === 0;\n}\n\n/**\n * Disposes of the value(s) passed in.\n */\nexport function dispose<T extends IDisposable>(disposable: T): T;\nexport function dispose<T extends IDisposable>(disposable: T | undefined): T | undefined;\nexport function dispose<T extends IDisposable, A extends Iterable<T> = Iterable<T>>(disposables: A): A;\nexport function dispose<T extends IDisposable>(disposables: Array<T>): Array<T>;\nexport function dispose<T extends IDisposable>(disposables: ReadonlyArray<T>): ReadonlyArray<T>;\nexport function dispose<T extends IDisposable>(arg: T | Iterable<T> | undefined): any {\n\tif (Iterable.is(arg)) {\n\t\tconst errors: any[] = [];\n\n\t\tfor (const d of arg) {\n\t\t\tif (d) {\n\t\t\t\ttry {\n\t\t\t\t\td.dispose();\n\t\t\t\t} catch (e) {\n\t\t\t\t\terrors.push(e);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (errors.length === 1) {\n\t\t\tthrow errors[0];\n\t\t} else if (errors.length > 1) {\n\t\t\tthrow new AggregateError(errors, 'Encountered errors while disposing of store');\n\t\t}\n\n\t\treturn Array.isArray(arg) ? [] : arg;\n\t} else if (arg) {\n\t\targ.dispose();\n\t\treturn arg;\n\t}\n}\n\nexport function disposeIfDisposable<T extends IDisposable | object>(disposables: Array<T>): Array<T> {\n\tfor (const d of disposables) {\n\t\tif (isDisposable(d)) {\n\t\t\td.dispose();\n\t\t}\n\t}\n\treturn [];\n}\n\n/**\n * Combine multiple disposable values into a single {@link IDisposable}.\n */\nexport function combinedDisposable(...disposables: IDisposable[]): IDisposable {\n\tconst parent = toDisposable(() => dispose(disposables));\n\tsetParentOfDisposables(disposables, parent);\n\treturn parent;\n}\n\n/**\n * Turn a function that implements dispose into an {@link IDisposable}.\n *\n * @param fn Clean up function, guaranteed to be called only **once**.\n */\nexport function toDisposable(fn: () => void): IDisposable {\n\tconst self = trackDisposable({\n\t\tdispose: createSingleCallFunction(() => {\n\t\t\tmarkAsDisposed(self);\n\t\t\tfn();\n\t\t})\n\t});\n\treturn self;\n}\n\n/**\n * Manages a collection of disposable values.\n *\n * This is the preferred way to manage multiple disposables. A `DisposableStore` is safer to work with than an\n * `IDisposable[]` as it considers edge cases, such as registering the same value multiple times or adding an item to a\n * store that has already been disposed of.\n */\nexport class DisposableStore implements IDisposable {\n\n\tstatic DISABLE_DISPOSED_WARNING = false;\n\n\tprivate readonly _toDispose = new Set<IDisposable>();\n\tprivate _isDisposed = false;\n\n\tconstructor() {\n\t\ttrackDisposable(this);\n\t}\n\n\t/**\n\t * Dispose of all registered disposables and mark this object as disposed.\n\t *\n\t * Any future disposables added to this object will be disposed of on `add`.\n\t */\n\tpublic dispose(): void {\n\t\tif (this._isDisposed) {\n\t\t\treturn;\n\t\t}\n\n\t\tmarkAsDisposed(this);\n\t\tthis._isDisposed = true;\n\t\tthis.clear();\n\t}\n\n\t/**\n\t * @return `true` if this object has been disposed of.\n\t */\n\tpublic get isDisposed(): boolean {\n\t\treturn this._isDisposed;\n\t}\n\n\t/**\n\t * Dispose of all registered disposables but do not mark this object as disposed.\n\t */\n\tpublic clear(): void {\n\t\tif (this._toDispose.size === 0) {\n\t\t\treturn;\n\t\t}\n\n\t\ttry {\n\t\t\tdispose(this._toDispose);\n\t\t} finally {\n\t\t\tthis._toDispose.clear();\n\t\t}\n\t}\n\n\t/**\n\t * Add a new {@link IDisposable disposable} to the collection.\n\t */\n\tpublic add<T extends IDisposable>(o: T): T {\n\t\tif (!o) {\n\t\t\treturn o;\n\t\t}\n\t\tif ((o as unknown as DisposableStore) === this) {\n\t\t\tthrow new Error('Cannot register a disposable on itself!');\n\t\t}\n\n\t\tsetParentOfDisposable(o, this);\n\t\tif (this._isDisposed) {\n\t\t\tif (!DisposableStore.DISABLE_DISPOSED_WARNING) {\n\t\t\t\tconsole.warn(new Error('Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!').stack);\n\t\t\t}\n\t\t} else {\n\t\t\tthis._toDispose.add(o);\n\t\t}\n\n\t\treturn o;\n\t}\n\n\t/**\n\t * Deletes a disposable from store and disposes of it. This will not throw or warn and proceed to dispose the\n\t * disposable even when the disposable is not part in the store.\n\t */\n\tpublic delete<T extends IDisposable>(o: T): void {\n\t\tif (!o) {\n\t\t\treturn;\n\t\t}\n\t\tif ((o as unknown as DisposableStore) === this) {\n\t\t\tthrow new Error('Cannot dispose a disposable on itself!');\n\t\t}\n\t\tthis._toDispose.delete(o);\n\t\to.dispose();\n\t}\n\n\t/**\n\t * Deletes the value from the store, but does not dispose it.\n\t */\n\tpublic deleteAndLeak<T extends IDisposable>(o: T): void {\n\t\tif (!o) {\n\t\t\treturn;\n\t\t}\n\t\tif (this._toDispose.has(o)) {\n\t\t\tthis._toDispose.delete(o);\n\t\t\tsetParentOfDisposable(o, null);\n\t\t}\n\t}\n}\n\n/**\n * Abstract base class for a {@link IDisposable disposable} object.\n *\n * Subclasses can {@linkcode _register} disposables that will be automatically cleaned up when this object is disposed of.\n */\nexport abstract class Disposable implements IDisposable {\n\n\t/**\n\t * A disposable that does nothing when it is disposed of.\n\t *\n\t * TODO: This should not be a static property.\n\t */\n\tstatic readonly None = Object.freeze<IDisposable>({ dispose() { } });\n\n\tprotected readonly _store = new DisposableStore();\n\n\tconstructor() {\n\t\ttrackDisposable(this);\n\t\tsetParentOfDisposable(this._store, this);\n\t}\n\n\tpublic dispose(): void {\n\t\tmarkAsDisposed(this);\n\n\t\tthis._store.dispose();\n\t}\n\n\t/**\n\t * Adds `o` to the collection of disposables managed by this object.\n\t */\n\tprotected _register<T extends IDisposable>(o: T): T {\n\t\tif ((o as unknown as Disposable) === this) {\n\t\t\tthrow new Error('Cannot register a disposable on itself!');\n\t\t}\n\t\treturn this._store.add(o);\n\t}\n}\n\n/**\n * Manages the lifecycle of a disposable value that may be changed.\n *\n * This ensures that when the disposable value is changed, the previously held disposable is disposed of. You can\n * also register a `MutableDisposable` on a `Disposable` to ensure it is automatically cleaned up.\n */\nexport class MutableDisposable<T extends IDisposable> implements IDisposable {\n\tprivate _value?: T;\n\tprivate _isDisposed = false;\n\n\tconstructor() {\n\t\ttrackDisposable(this);\n\t}\n\n\tget value(): T | undefined {\n\t\treturn this._isDisposed ? undefined : this._value;\n\t}\n\n\tset value(value: T | undefined) {\n\t\tif (this._isDisposed || value === this._value) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._value?.dispose();\n\t\tif (value) {\n\t\t\tsetParentOfDisposable(value, this);\n\t\t}\n\t\tthis._value = value;\n\t}\n\n\t/**\n\t * Resets the stored value and disposed of the previously stored value.\n\t */\n\tclear(): void {\n\t\tthis.value = undefined;\n\t}\n\n\tdispose(): void {\n\t\tthis._isDisposed = true;\n\t\tmarkAsDisposed(this);\n\t\tthis._value?.dispose();\n\t\tthis._value = undefined;\n\t}\n\n\t/**\n\t * Clears the value, but does not dispose it.\n\t * The old value is returned.\n\t*/\n\tclearAndLeak(): T | undefined {\n\t\tconst oldValue = this._value;\n\t\tthis._value = undefined;\n\t\tif (oldValue) {\n\t\t\tsetParentOfDisposable(oldValue, null);\n\t\t}\n\t\treturn oldValue;\n\t}\n}\n\n/**\n * Manages the lifecycle of a disposable value that may be changed like {@link MutableDisposable}, but the value must\n * exist and cannot be undefined.\n */\nexport class MandatoryMutableDisposable<T extends IDisposable> implements IDisposable {\n\tprivate readonly _disposable = new MutableDisposable<T>();\n\tprivate _isDisposed = false;\n\n\tconstructor(initialValue: T) {\n\t\tthis._disposable.value = initialValue;\n\t}\n\n\tget value(): T {\n\t\treturn this._disposable.value!;\n\t}\n\n\tset value(value: T) {\n\t\tif (this._isDisposed || value === this._disposable.value) {\n\t\t\treturn;\n\t\t}\n\t\tthis._disposable.value = value;\n\t}\n\n\tdispose() {\n\t\tthis._isDisposed = true;\n\t\tthis._disposable.dispose();\n\t}\n}\n\nexport class RefCountedDisposable {\n\n\tprivate _counter: number = 1;\n\n\tconstructor(\n\t\tprivate readonly _disposable: IDisposable,\n\t) { }\n\n\tacquire() {\n\t\tthis._counter++;\n\t\treturn this;\n\t}\n\n\trelease() {\n\t\tif (--this._counter === 0) {\n\t\t\tthis._disposable.dispose();\n\t\t}\n\t\treturn this;\n\t}\n}\n\n/**\n * A safe disposable can be `unset` so that a leaked reference (listener)\n * can be cut-off.\n */\nexport class SafeDisposable implements IDisposable {\n\n\tdispose: () => void = () => { };\n\tunset: () => void = () => { };\n\tisset: () => boolean = () => false;\n\n\tconstructor() {\n\t\ttrackDisposable(this);\n\t}\n\n\tset(fn: Function) {\n\t\tlet callback: Function | undefined = fn;\n\t\tthis.unset = () => callback = undefined;\n\t\tthis.isset = () => callback !== undefined;\n\t\tthis.dispose = () => {\n\t\t\tif (callback) {\n\t\t\t\tcallback();\n\t\t\t\tcallback = undefined;\n\t\t\t\tmarkAsDisposed(this);\n\t\t\t}\n\t\t};\n\t\treturn this;\n\t}\n}\n\nexport interface IReference<T> extends IDisposable {\n\treadonly object: T;\n}\n\nexport abstract class ReferenceCollection<T> {\n\n\tprivate readonly references: Map<string, { readonly object: T; counter: number }> = new Map();\n\n\tacquire(key: string, ...args: any[]): IReference<T> {\n\t\tlet reference = this.references.get(key);\n\n\t\tif (!reference) {\n\t\t\treference = { counter: 0, object: this.createReferencedObject(key, ...args) };\n\t\t\tthis.references.set(key, reference);\n\t\t}\n\n\t\tconst { object } = reference;\n\t\tconst dispose = createSingleCallFunction(() => {\n\t\t\tif (--reference.counter === 0) {\n\t\t\t\tthis.destroyReferencedObject(key, reference.object);\n\t\t\t\tthis.references.delete(key);\n\t\t\t}\n\t\t});\n\n\t\treference.counter++;\n\n\t\treturn { object, dispose };\n\t}\n\n\tprotected abstract createReferencedObject(key: string, ...args: any[]): T;\n\tprotected abstract destroyReferencedObject(key: string, object: T): void;\n}\n\n/**\n * Unwraps a reference collection of promised values. Makes sure\n * references are disposed whenever promises get rejected.\n */\nexport class AsyncReferenceCollection<T> {\n\n\tconstructor(private referenceCollection: ReferenceCollection<Promise<T>>) { }\n\n\tasync acquire(key: string, ...args: any[]): Promise<IReference<T>> {\n\t\tconst ref = this.referenceCollection.acquire(key, ...args);\n\n\t\ttry {\n\t\t\tconst object = await ref.object;\n\n\t\t\treturn {\n\t\t\t\tobject,\n\t\t\t\tdispose: () => ref.dispose()\n\t\t\t};\n\t\t} catch (error) {\n\t\t\tref.dispose();\n\t\t\tthrow error;\n\t\t}\n\t}\n}\n\nexport class ImmortalReference<T> implements IReference<T> {\n\tconstructor(public object: T) { }\n\tdispose(): void { /* noop */ }\n}\n\nexport function disposeOnReturn(fn: (store: DisposableStore) => void): void {\n\tconst store = new DisposableStore();\n\ttry {\n\t\tfn(store);\n\t} finally {\n\t\tstore.dispose();\n\t}\n}\n\n/**\n * A map the manages the lifecycle of the values that it stores.\n */\nexport class DisposableMap<K, V extends IDisposable = IDisposable> implements IDisposable {\n\n\tprivate readonly _store = new Map<K, V>();\n\tprivate _isDisposed = false;\n\n\tconstructor() {\n\t\ttrackDisposable(this);\n\t}\n\n\t/**\n\t * Disposes of all stored values and mark this object as disposed.\n\t *\n\t * Trying to use this object after it has been disposed of is an error.\n\t */\n\tdispose(): void {\n\t\tmarkAsDisposed(this);\n\t\tthis._isDisposed = true;\n\t\tthis.clearAndDisposeAll();\n\t}\n\n\t/**\n\t * Disposes of all stored values and clear the map, but DO NOT mark this object as disposed.\n\t */\n\tclearAndDisposeAll(): void {\n\t\tif (!this._store.size) {\n\t\t\treturn;\n\t\t}\n\n\t\ttry {\n\t\t\tdispose(this._store.values());\n\t\t} finally {\n\t\t\tthis._store.clear();\n\t\t}\n\t}\n\n\thas(key: K): boolean {\n\t\treturn this._store.has(key);\n\t}\n\n\tget size(): number {\n\t\treturn this._store.size;\n\t}\n\n\tget(key: K): V | undefined {\n\t\treturn this._store.get(key);\n\t}\n\n\tset(key: K, value: V, skipDisposeOnOverwrite = false): void {\n\t\tif (this._isDisposed) {\n\t\t\tconsole.warn(new Error('Trying to add a disposable to a DisposableMap that has already been disposed of. The added object will be leaked!').stack);\n\t\t}\n\n\t\tif (!skipDisposeOnOverwrite) {\n\t\t\tthis._store.get(key)?.dispose();\n\t\t}\n\n\t\tthis._store.set(key, value);\n\t}\n\n\t/**\n\t * Delete the value stored for `key` from this map and also dispose of it.\n\t */\n\tdeleteAndDispose(key: K): void {\n\t\tthis._store.get(key)?.dispose();\n\t\tthis._store.delete(key);\n\t}\n\n\t/**\n\t * Delete the value stored for `key` from this map but return it. The caller is\n\t * responsible for disposing of the value.\n\t */\n\tdeleteAndLeak(key: K): V | undefined {\n\t\tconst value = this._store.get(key);\n\t\tthis._store.delete(key);\n\t\treturn value;\n\t}\n\n\tkeys(): IterableIterator<K> {\n\t\treturn this._store.keys();\n\t}\n\n\tvalues(): IterableIterator<V> {\n\t\treturn this._store.values();\n\t}\n\n\t[Symbol.iterator](): IterableIterator<[K, V]> {\n\t\treturn this._store[Symbol.iterator]();\n\t}\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nclass Node<E> {\n\n\tstatic readonly Undefined = new Node<any>(undefined);\n\n\telement: E;\n\tnext: Node<E>;\n\tprev: Node<E>;\n\n\tconstructor(element: E) {\n\t\tthis.element = element;\n\t\tthis.next = Node.Undefined;\n\t\tthis.prev = Node.Undefined;\n\t}\n}\n\nexport class LinkedList<E> {\n\n\tprivate _first: Node<E> = Node.Undefined;\n\tprivate _last: Node<E> = Node.Undefined;\n\tprivate _size: number = 0;\n\n\tget size(): number {\n\t\treturn this._size;\n\t}\n\n\tisEmpty(): boolean {\n\t\treturn this._first === Node.Undefined;\n\t}\n\n\tclear(): void {\n\t\tlet node = this._first;\n\t\twhile (node !== Node.Undefined) {\n\t\t\tconst next = node.next;\n\t\t\tnode.prev = Node.Undefined;\n\t\t\tnode.next = Node.Undefined;\n\t\t\tnode = next;\n\t\t}\n\n\t\tthis._first = Node.Undefined;\n\t\tthis._last = Node.Undefined;\n\t\tthis._size = 0;\n\t}\n\n\tunshift(element: E): () => void {\n\t\treturn this._insert(element, false);\n\t}\n\n\tpush(element: E): () => void {\n\t\treturn this._insert(element, true);\n\t}\n\n\tprivate _insert(element: E, atTheEnd: boolean): () => void {\n\t\tconst newNode = new Node(element);\n\t\tif (this._first === Node.Undefined) {\n\t\t\tthis._first = newNode;\n\t\t\tthis._last = newNode;\n\n\t\t} else if (atTheEnd) {\n\t\t\t// push\n\t\t\tconst oldLast = this._last;\n\t\t\tthis._last = newNode;\n\t\t\tnewNode.prev = oldLast;\n\t\t\toldLast.next = newNode;\n\n\t\t} else {\n\t\t\t// unshift\n\t\t\tconst oldFirst = this._first;\n\t\t\tthis._first = newNode;\n\t\t\tnewNode.next = oldFirst;\n\t\t\toldFirst.prev = newNode;\n\t\t}\n\t\tthis._size += 1;\n\n\t\tlet didRemove = false;\n\t\treturn () => {\n\t\t\tif (!didRemove) {\n\t\t\t\tdidRemove = true;\n\t\t\t\tthis._remove(newNode);\n\t\t\t}\n\t\t};\n\t}\n\n\tshift(): E | undefined {\n\t\tif (this._first === Node.Undefined) {\n\t\t\treturn undefined;\n\t\t} else {\n\t\t\tconst res = this._first.element;\n\t\t\tthis._remove(this._first);\n\t\t\treturn res;\n\t\t}\n\t}\n\n\tpop(): E | undefined {\n\t\tif (this._last === Node.Undefined) {\n\t\t\treturn undefined;\n\t\t} else {\n\t\t\tconst res = this._last.element;\n\t\t\tthis._remove(this._last);\n\t\t\treturn res;\n\t\t}\n\t}\n\n\tprivate _remove(node: Node<E>): void {\n\t\tif (node.prev !== Node.Undefined && node.next !== Node.Undefined) {\n\t\t\t// middle\n\t\t\tconst anchor = node.prev;\n\t\t\tanchor.next = node.next;\n\t\t\tnode.next.prev = anchor;\n\n\t\t} else if (node.prev === Node.Undefined && node.next === Node.Undefined) {\n\t\t\t// only node\n\t\t\tthis._first = Node.Undefined;\n\t\t\tthis._last = Node.Undefined;\n\n\t\t} else if (node.next === Node.Undefined) {\n\t\t\t// last\n\t\t\tthis._last = this._last.prev!;\n\t\t\tthis._last.next = Node.Undefined;\n\n\t\t} else if (node.prev === Node.Undefined) {\n\t\t\t// first\n\t\t\tthis._first = this._first.next!;\n\t\t\tthis._first.prev = Node.Undefined;\n\t\t}\n\n\t\t// done\n\t\tthis._size -= 1;\n\t}\n\n\t*[Symbol.iterator](): Iterator<E> {\n\t\tlet node = this._first;\n\t\twhile (node !== Node.Undefined) {\n\t\t\tyield node.element;\n\t\t\tnode = node.next;\n\t\t}\n\t}\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n// fake definition so that the valid layers check won't trip on this\ndeclare const globalThis: { performance?: { now(): number } };\n\nconst hasPerformanceNow = (globalThis.performance && typeof globalThis.performance.now === 'function');\n\nexport class StopWatch {\n\n\tprivate _startTime: number;\n\tprivate _stopTime: number;\n\n\tprivate readonly _now: () => number;\n\n\tpublic static create(highResolution?: boolean): StopWatch {\n\t\treturn new StopWatch(highResolution);\n\t}\n\n\tconstructor(highResolution?: boolean) {\n\t\tthis._now = hasPerformanceNow && highResolution === false ? Date.now : globalThis.performance!.now.bind(globalThis.performance);\n\t\tthis._startTime = this._now();\n\t\tthis._stopTime = -1;\n\t}\n\n\tpublic stop(): void {\n\t\tthis._stopTime = this._now();\n\t}\n\n\tpublic reset(): void {\n\t\tthis._startTime = this._now();\n\t\tthis._stopTime = -1;\n\t}\n\n\tpublic elapsed(): number {\n\t\tif (this._stopTime !== -1) {\n\t\t\treturn this._stopTime - this._startTime;\n\t\t}\n\t\treturn this._now() - this._startTime;\n\t}\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\nimport { CancellationToken } from 'vs/base/common/cancellation';\nimport { onUnexpectedError } from 'vs/base/common/errors';\nimport { createSingleCallFunction } from 'vs/base/common/functional';\nimport { combinedDisposable, Disposable, DisposableMap, DisposableStore, IDisposable, toDisposable } from 'vs/base/common/lifecycle';\nimport { LinkedList } from 'vs/base/common/linkedList';\nimport { IObservable, IObserver } from 'vs/base/common/observable';\nimport { StopWatch } from 'vs/base/common/stopwatch';\nimport { MicrotaskDelay } from 'vs/base/common/symbols';\n\n\n// -----------------------------------------------------------------------------------------------------------------------\n// Uncomment the next line to print warnings whenever a listener is GC'ed without having been disposed. This is a LEAK.\n// -----------------------------------------------------------------------------------------------------------------------\nconst _enableListenerGCedWarning = false\n\t// || Boolean(\"TRUE\") // causes a linter warning so that it cannot be pushed\n\t;\n\n// -----------------------------------------------------------------------------------------------------------------------\n// Uncomment the next line to print warnings whenever an emitter with listeners is disposed. That is a sign of code smell.\n// -----------------------------------------------------------------------------------------------------------------------\nconst _enableDisposeWithListenerWarning = false\n\t// || Boolean(\"TRUE\") // causes a linter warning so that it cannot be pushed\n\t;\n\n\n// -----------------------------------------------------------------------------------------------------------------------\n// Uncomment the next line to print warnings whenever a snapshotted event is used repeatedly without cleanup.\n// See https://github.com/microsoft/vscode/issues/142851\n// -----------------------------------------------------------------------------------------------------------------------\nconst _enableSnapshotPotentialLeakWarning = false\n\t// || Boolean(\"TRUE\") // causes a linter warning so that it cannot be pushed\n\t;\n\n/**\n * An event with zero or one parameters that can be subscribed to. The event is a function itself.\n */\nexport interface Event<T> {\n\t(listener: (e: T) => any, thisArgs?: any, disposables?: IDisposable[] | DisposableStore): IDisposable;\n}\n\nexport namespace Event {\n\texport const None: Event<any> = () => Disposable.None;\n\n\tfunction _addLeakageTraceLogic(options: EmitterOptions) {\n\t\tif (_enableSnapshotPotentialLeakWarning) {\n\t\t\tconst { onDidAddListener: origListenerDidAdd } = options;\n\t\t\tconst stack = Stacktrace.create();\n\t\t\tlet count = 0;\n\t\t\toptions.onDidAddListener = () => {\n\t\t\t\tif (++count === 2) {\n\t\t\t\t\tconsole.warn('snapshotted emitter LIKELY used public and SHOULD HAVE BEEN created with DisposableStore. snapshotted here');\n\t\t\t\t\tstack.print();\n\t\t\t\t}\n\t\t\t\torigListenerDidAdd?.();\n\t\t\t};\n\t\t}\n\t}\n\n\t/**\n\t * Given an event, returns another event which debounces calls and defers the listeners to a later task via a shared\n\t * `setTimeout`. The event is converted into a signal (`Event<void>`) to avoid additional object creation as a\n\t * result of merging events and to try prevent race conditions that could arise when using related deferred and\n\t * non-deferred events.\n\t *\n\t * This is useful for deferring non-critical work (eg. general UI updates) to ensure it does not block critical work\n\t * (eg. latency of keypress to text rendered).\n\t *\n\t * *NOTE* that this function returns an `Event` and it MUST be called with a `DisposableStore` whenever the returned\n\t * event is accessible to \"third parties\", e.g the event is a public property. Otherwise a leaked listener on the\n\t * returned event causes this utility to leak a listener on the original event.\n\t *\n\t * @param event The event source for the new event.\n\t * @param disposable A disposable store to add the new EventEmitter to.\n\t */\n\texport function defer(event: Event<unknown>, disposable?: DisposableStore): Event<void> {\n\t\treturn debounce<unknown, void>(event, () => void 0, 0, undefined, true, undefined, disposable);\n\t}\n\n\t/**\n\t * Given an event, returns another event which only fires once.\n\t *\n\t * @param event The event source for the new event.\n\t */\n\texport function once<T>(event: Event<T>): Event<T> {\n\t\treturn (listener, thisArgs = null, disposables?) => {\n\t\t\t// we need this, in case the event fires during the listener call\n\t\t\tlet didFire = false;\n\t\t\tlet result: IDisposable | undefined = undefined;\n\t\t\tresult = event(e => {\n\t\t\t\tif (didFire) {\n\t\t\t\t\treturn;\n\t\t\t\t} else if (result) {\n\t\t\t\t\tresult.dispose();\n\t\t\t\t} else {\n\t\t\t\t\tdidFire = true;\n\t\t\t\t}\n\n\t\t\t\treturn listener.call(thisArgs, e);\n\t\t\t}, null, disposables);\n\n\t\t\tif (didFire) {\n\t\t\t\tresult.dispose();\n\t\t\t}\n\n\t\t\treturn result;\n\t\t};\n\t}\n\n\t/**\n\t * Maps an event of one type into an event of another type using a mapping function, similar to how\n\t * `Array.prototype.map` works.\n\t *\n\t * *NOTE* that this function returns an `Event` and it MUST be called with a `DisposableStore` whenever the returned\n\t * event is accessible to \"third parties\", e.g the event is a public property. Otherwise a leaked listener on the\n\t * returned event causes this utility to leak a listener on the original event.\n\t *\n\t * @param event The event source for the new event.\n\t * @param map The mapping function.\n\t * @param disposable A disposable store to add the new EventEmitter to.\n\t */\n\texport function map<I, O>(event: Event<I>, map: (i: I) => O, disposable?: DisposableStore): Event<O> {\n\t\treturn snapshot((listener, thisArgs = null, disposables?) => event(i => listener.call(thisArgs, map(i)), null, disposables), disposable);\n\t}\n\n\t/**\n\t * Wraps an event in another event that performs some function on the event object before firing.\n\t *\n\t * *NOTE* that this function returns an `Event` and it MUST be called with a `DisposableStore` whenever the returned\n\t * event is accessible to \"third parties\", e.g the event is a public property. Otherwise a leaked listener on the\n\t * returned event causes this utility to leak a listener on the original event.\n\t *\n\t * @param event The event source for the new event.\n\t * @param each The function to perform on the event object.\n\t * @param disposable A disposable store to add the new EventEmitter to.\n\t */\n\texport function forEach<I>(event: Event<I>, each: (i: I) => void, disposable?: DisposableStore): Event<I> {\n\t\treturn snapshot((listener, thisArgs = null, disposables?) => event(i => { each(i); listener.call(thisArgs, i); }, null, disposables), disposable);\n\t}\n\n\t/**\n\t * Wraps an event in another event that fires only when some condition is met.\n\t *\n\t * *NOTE* that this function returns an `Event` and it MUST be called with a `DisposableStore` whenever the returned\n\t * event is accessible to \"third parties\", e.g the event is a public property. Otherwise a leaked listener on the\n\t * returned event causes this utility to leak a listener on the original event.\n\t *\n\t * @param event The event source for the new event.\n\t * @param filter The filter function that defines the condition. The event will fire for the object if this function\n\t * returns true.\n\t * @param disposable A disposable store to add the new EventEmitter to.\n\t */\n\texport function filter<T, U>(event: Event<T | U>, filter: (e: T | U) => e is T, disposable?: DisposableStore): Event<T>;\n\texport function filter<T>(event: Event<T>, filter: (e: T) => boolean, disposable?: DisposableStore): Event<T>;\n\texport function filter<T, R>(event: Event<T | R>, filter: (e: T | R) => e is R, disposable?: DisposableStore): Event<R>;\n\texport function filter<T>(event: Event<T>, filter: (e: T) => boolean, disposable?: DisposableStore): Event<T> {\n\t\treturn snapshot((listener, thisArgs = null, disposables?) => event(e => filter(e) && listener.call(thisArgs, e), null, disposables), disposable);\n\t}\n\n\t/**\n\t * Given an event, returns the same event but typed as `Event<void>`.\n\t */\n\texport function signal<T>(event: Event<T>): Event<void> {\n\t\treturn event as Event<any> as Event<void>;\n\t}\n\n\t/**\n\t * Given a collection of events, returns a single event which emits whenever any of the provided events emit.\n\t */\n\texport function any<T>(...events: Event<T>[]): Event<T>;\n\texport function any(...events: Event<any>[]): Event<void>;\n\texport function any<T>(...events: Event<T>[]): Event<T> {\n\t\treturn (listener, thisArgs = null, disposables?) => {\n\t\t\tconst disposable = combinedDisposable(...events.map(event => event(e => listener.call(thisArgs, e))));\n\t\t\treturn addAndReturnDisposable(disposable, disposables);\n\t\t};\n\t}\n\n\t/**\n\t * *NOTE* that this function returns an `Event` and it MUST be called with a `DisposableStore` whenever the returned\n\t * event is accessible to \"third parties\", e.g the event is a public property. Otherwise a leaked listener on the\n\t * returned event causes this utility to leak a listener on the original event.\n\t */\n\texport function reduce<I, O>(event: Event<I>, merge: (last: O | undefined, event: I) => O, initial?: O, disposable?: DisposableStore): Event<O> {\n\t\tlet output: O | undefined = initial;\n\n\t\treturn map<I, O>(event, e => {\n\t\t\toutput = merge(output, e);\n\t\t\treturn output;\n\t\t}, disposable);\n\t}\n\n\tfunction snapshot<T>(event: Event<T>, disposable: DisposableStore | undefined): Event<T> {\n\t\tlet listener: IDisposable | undefined;\n\n\t\tconst options: EmitterOptions | undefined = {\n\t\t\tonWillAddFirstListener() {\n\t\t\t\tlistener = event(emitter.fire, emitter);\n\t\t\t},\n\t\t\tonDidRemoveLastListener() {\n\t\t\t\tlistener?.dispose();\n\t\t\t}\n\t\t};\n\n\t\tif (!disposable) {\n\t\t\t_addLeakageTraceLogic(options);\n\t\t}\n\n\t\tconst emitter = new Emitter<T>(options);\n\n\t\tdisposable?.add(emitter);\n\n\t\treturn emitter.event;\n\t}\n\n\t/**\n\t * Adds the IDisposable to the store if it's set, and returns it. Useful to\n\t * Event function implementation.\n\t */\n\tfunction addAndReturnDisposable<T extends IDisposable>(d: T, store: DisposableStore | IDisposable[] | undefined): T {\n\t\tif (store instanceof Array) {\n\t\t\tstore.push(d);\n\t\t} else if (store) {\n\t\t\tstore.add(d);\n\t\t}\n\t\treturn d;\n\t}\n\n\t/**\n\t * Given an event, creates a new emitter that event that will debounce events based on {@link delay} and give an\n\t * array event object of all events that fired.\n\t *\n\t * *NOTE* that this function returns an `Event` and it MUST be called with a `DisposableStore` whenever the returned\n\t * event is accessible to \"third parties\", e.g the event is a public property. Otherwise a leaked listener on the\n\t * returned event causes this utility to leak a listener on the original event.\n\t *\n\t * @param event The original event to debounce.\n\t * @param merge A function that reduces all events into a single event.\n\t * @param delay The number of milliseconds to debounce.\n\t * @param leading Whether to fire a leading event without debouncing.\n\t * @param flushOnListenerRemove Whether to fire all debounced events when a listener is removed. If this is not\n\t * specified, some events could go missing. Use this if it's important that all events are processed, even if the\n\t * listener gets disposed before the debounced event fires.\n\t * @param leakWarningThreshold See {@link EmitterOptions.leakWarningThreshold}.\n\t * @param disposable A disposable store to register the debounce emitter to.\n\t */\n\texport function debounce<T>(event: Event<T>, merge: (last: T | undefined, event: T) => T, delay?: number | typeof MicrotaskDelay, leading?: boolean, flushOnListenerRemove?: boolean, leakWarningThreshold?: number, disposable?: DisposableStore): Event<T>;\n\texport function debounce<I, O>(event: Event<I>, merge: (last: O | undefined, event: I) => O, delay?: number | typeof MicrotaskDelay, leading?: boolean, flushOnListenerRemove?: boolean, leakWarningThreshold?: number, disposable?: DisposableStore): Event<O>;\n\texport function debounce<I, O>(event: Event<I>, merge: (last: O | undefined, event: I) => O, delay: number | typeof MicrotaskDelay = 100, leading = false, flushOnListenerRemove = false, leakWarningThreshold?: number, disposable?: DisposableStore): Event<O> {\n\t\tlet subscription: IDisposable;\n\t\tlet output: O | undefined = undefined;\n\t\tlet handle: any = undefined;\n\t\tlet numDebouncedCalls = 0;\n\t\tlet doFire: (() => void) | undefined;\n\n\t\tconst options: EmitterOptions | undefined = {\n\t\t\tleakWarningThreshold,\n\t\t\tonWillAddFirstListener() {\n\t\t\t\tsubscription = event(cur => {\n\t\t\t\t\tnumDebouncedCalls++;\n\t\t\t\t\toutput = merge(output, cur);\n\n\t\t\t\t\tif (leading && !handle) {\n\t\t\t\t\t\temitter.fire(output);\n\t\t\t\t\t\toutput = undefined;\n\t\t\t\t\t}\n\n\t\t\t\t\tdoFire = () => {\n\t\t\t\t\t\tconst _output = output;\n\t\t\t\t\t\toutput = undefined;\n\t\t\t\t\t\thandle = undefined;\n\t\t\t\t\t\tif (!leading || numDebouncedCalls > 1) {\n\t\t\t\t\t\t\temitter.fire(_output!);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tnumDebouncedCalls = 0;\n\t\t\t\t\t};\n\n\t\t\t\t\tif (typeof delay === 'number') {\n\t\t\t\t\t\tclearTimeout(handle);\n\t\t\t\t\t\thandle = setTimeout(doFire, delay);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (handle === undefined) {\n\t\t\t\t\t\t\thandle = 0;\n\t\t\t\t\t\t\tqueueMicrotask(doFire);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tonWillRemoveListener() {\n\t\t\t\tif (flushOnListenerRemove && numDebouncedCalls > 0) {\n\t\t\t\t\tdoFire?.();\n\t\t\t\t}\n\t\t\t},\n\t\t\tonDidRemoveLastListener() {\n\t\t\t\tdoFire = undefined;\n\t\t\t\tsubscription.dispose();\n\t\t\t}\n\t\t};\n\n\t\tif (!disposable) {\n\t\t\t_addLeakageTraceLogic(options);\n\t\t}\n\n\t\tconst emitter = new Emitter<O>(options);\n\n\t\tdisposable?.add(emitter);\n\n\t\treturn emitter.event;\n\t}\n\n\t/**\n\t * Debounces an event, firing after some delay (default=0) with an array of all event original objects.\n\t *\n\t * *NOTE* that this function returns an `Event` and it MUST be called with a `DisposableStore` whenever the returned\n\t * event is accessible to \"third parties\", e.g the event is a public property. Otherwise a leaked listener on the\n\t * returned event causes this utility to leak a listener on the original event.\n\t */\n\texport function accumulate<T>(event: Event<T>, delay: number = 0, disposable?: DisposableStore): Event<T[]> {\n\t\treturn Event.debounce<T, T[]>(event, (last, e) => {\n\t\t\tif (!last) {\n\t\t\t\treturn [e];\n\t\t\t}\n\t\t\tlast.push(e);\n\t\t\treturn last;\n\t\t}, delay, undefined, true, undefined, disposable);\n\t}\n\n\t/**\n\t * Filters an event such that some condition is _not_ met more than once in a row, effectively ensuring duplicate\n\t * event objects from different sources do not fire the same event object.\n\t *\n\t * *NOTE* that this function returns an `Event` and it MUST be called with a `DisposableStore` whenever the returned\n\t * event is accessible to \"third parties\", e.g the event is a public property. Otherwise a leaked listener on the\n\t * returned event causes this utility to leak a listener on the original event.\n\t *\n\t * @param event The event source for the new event.\n\t * @param equals The equality condition.\n\t * @param disposable A disposable store to add the new EventEmitter to.\n\t *\n\t * @example\n\t * ```\n\t * // Fire only one time when a single window is opened or focused\n\t * Event.latch(Event.any(onDidOpenWindow, onDidFocusWindow))\n\t * ```\n\t */\n\texport function latch<T>(event: Event<T>, equals: (a: T, b: T) => boolean = (a, b) => a === b, disposable?: DisposableStore): Event<T> {\n\t\tlet firstCall = true;\n\t\tlet cache: T;\n\n\t\treturn filter(event, value => {\n\t\t\tconst shouldEmit = firstCall || !equals(value, cache);\n\t\t\tfirstCall = false;\n\t\t\tcache = value;\n\t\t\treturn shouldEmit;\n\t\t}, disposable);\n\t}\n\n\t/**\n\t * Splits an event whose parameter is a union type into 2 separate events for each type in the union.\n\t *\n\t * *NOTE* that this function returns an `Event` and it MUST be called with a `DisposableStore` whenever the returned\n\t * event is accessible to \"third parties\", e.g the event is a public property. Otherwise a leaked listener on the\n\t * returned event causes this utility to leak a listener on the original event.\n\t *\n\t * @example\n\t * ```\n\t * const event = new EventEmitter<number | undefined>().event;\n\t * const [numberEvent, undefinedEvent] = Event.split(event, isUndefined);\n\t * ```\n\t *\n\t * @param event The event source for the new event.\n\t * @param isT A function that determines what event is of the first type.\n\t * @param disposable A disposable store to add the new EventEmitter to.\n\t */\n\texport function split<T, U>(event: Event<T | U>, isT: (e: T | U) => e is T, disposable?: DisposableStore): [Event<T>, Event<U>] {\n\t\treturn [\n\t\t\tEvent.filter(event, isT, disposable),\n\t\t\tEvent.filter(event, e => !isT(e), disposable) as Event<U>,\n\t\t];\n\t}\n\n\t/**\n\t * Buffers an event until it has a listener attached.\n\t *\n\t * *NOTE* that this function returns an `Event` and it MUST be called with a `DisposableStore` whenever the returned\n\t * event is accessible to \"third parties\", e.g the event is a public property. Otherwise a leaked listener on the\n\t * returned event causes this utility to leak a listener on the original event.\n\t *\n\t * @param event The event source for the new event.\n\t * @param flushAfterTimeout Determines whether to flush the buffer after a timeout immediately or after a\n\t * `setTimeout` when the first event listener is added.\n\t * @param _buffer Internal: A source event array used for tests.\n\t *\n\t * @example\n\t * ```\n\t * // Start accumulating events, when the first listener is attached, flush\n\t * // the event after a timeout such that multiple listeners attached before\n\t * // the timeout would receive the event\n\t * this.onInstallExtension = Event.buffer(service.onInstallExtension, true);\n\t * ```\n\t */\n\texport function buffer<T>(event: Event<T>, flushAfterTimeout = false, _buffer: T[] = [], disposable?: DisposableStore): Event<T> {\n\t\tlet buffer: T[] | null = _buffer.slice();\n\n\t\tlet listener: IDisposable | null = event(e => {\n\t\t\tif (buffer) {\n\t\t\t\tbuffer.push(e);\n\t\t\t} else {\n\t\t\t\temitter.fire(e);\n\t\t\t}\n\t\t});\n\n\t\tif (disposable) {\n\t\t\tdisposable.add(listener);\n\t\t}\n\n\t\tconst flush = () => {\n\t\t\tbuffer?.forEach(e => emitter.fire(e));\n\t\t\tbuffer = null;\n\t\t};\n\n\t\tconst emitter = new Emitter<T>({\n\t\t\tonWillAddFirstListener() {\n\t\t\t\tif (!listener) {\n\t\t\t\t\tlistener = event(e => emitter.fire(e));\n\t\t\t\t\tif (disposable) {\n\t\t\t\t\t\tdisposable.add(listener);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tonDidAddFirstListener() {\n\t\t\t\tif (buffer) {\n\t\t\t\t\tif (flushAfterTimeout) {\n\t\t\t\t\t\tsetTimeout(flush);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tflush();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tonDidRemoveLastListener() {\n\t\t\t\tif (listener) {\n\t\t\t\t\tlistener.dispose();\n\t\t\t\t}\n\t\t\t\tlistener = null;\n\t\t\t}\n\t\t});\n\n\t\tif (disposable) {\n\t\t\tdisposable.add(emitter);\n\t\t}\n\n\t\treturn emitter.event;\n\t}\n\t/**\n\t * Wraps the event in an {@link IChainableEvent}, allowing a more functional programming style.\n\t *\n\t * @example\n\t * ```\n\t * // Normal\n\t * const onEnterPressNormal = Event.filter(\n\t *   Event.map(onKeyPress.event, e => new StandardKeyboardEvent(e)),\n\t *   e.keyCode === KeyCode.Enter\n\t * ).event;\n\t *\n\t * // Using chain\n\t * const onEnterPressChain = Event.chain(onKeyPress.event, $ => $\n\t *   .map(e => new StandardKeyboardEvent(e))\n\t *   .filter(e => e.keyCode === KeyCode.Enter)\n\t * );\n\t * ```\n\t */\n\texport function chain<T, R>(event: Event<T>, sythensize: ($: IChainableSythensis<T>) => IChainableSythensis<R>): Event<R> {\n\t\tconst fn: Event<R> = (listener, thisArgs, disposables) => {\n\t\t\tconst cs = sythensize(new ChainableSynthesis()) as ChainableSynthesis;\n\t\t\treturn event(function (value) {\n\t\t\t\tconst result = cs.evaluate(value);\n\t\t\t\tif (result !== HaltChainable) {\n\t\t\t\t\tlistener.call(thisArgs, result);\n\t\t\t\t}\n\t\t\t}, undefined, disposables);\n\t\t};\n\n\t\treturn fn;\n\t}\n\n\tconst HaltChainable = Symbol('HaltChainable');\n\n\tclass ChainableSynthesis implements IChainableSythensis<any> {\n\t\tprivate readonly steps: ((input: any) => any)[] = [];\n\n\t\tmap<O>(fn: (i: any) => O): this {\n\t\t\tthis.steps.push(fn);\n\t\t\treturn this;\n\t\t}\n\n\t\tforEach(fn: (i: any) => void): this {\n\t\t\tthis.steps.push(v => {\n\t\t\t\tfn(v);\n\t\t\t\treturn v;\n\t\t\t});\n\t\t\treturn this;\n\t\t}\n\n\t\tfilter(fn: (e: any) => boolean): this {\n\t\t\tthis.steps.push(v => fn(v) ? v : HaltChainable);\n\t\t\treturn this;\n\t\t}\n\n\t\treduce<R>(merge: (last: R | undefined, event: any) => R, initial?: R | undefined): this {\n\t\t\tlet last = initial;\n\t\t\tthis.steps.push(v => {\n\t\t\t\tlast = merge(last, v);\n\t\t\t\treturn last;\n\t\t\t});\n\t\t\treturn this;\n\t\t}\n\n\t\tlatch(equals: (a: any, b: any) => boolean = (a, b) => a === b): ChainableSynthesis {\n\t\t\tlet firstCall = true;\n\t\t\tlet cache: any;\n\t\t\tthis.steps.push(value => {\n\t\t\t\tconst shouldEmit = firstCall || !equals(value, cache);\n\t\t\t\tfirstCall = false;\n\t\t\t\tcache = value;\n\t\t\t\treturn shouldEmit ? value : HaltChainable;\n\t\t\t});\n\n\t\t\treturn this;\n\t\t}\n\n\t\tpublic evaluate(value: any) {\n\t\t\tfor (const step of this.steps) {\n\t\t\t\tvalue = step(value);\n\t\t\t\tif (value === HaltChainable) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn value;\n\t\t}\n\t}\n\n\texport interface IChainableSythensis<T> {\n\t\tmap<O>(fn: (i: T) => O): IChainableSythensis<O>;\n\t\tforEach(fn: (i: T) => void): IChainableSythensis<T>;\n\t\tfilter<R extends T>(fn: (e: T) => e is R): IChainableSythensis<R>;\n\t\tfilter(fn: (e: T) => boolean): IChainableSythensis<T>;\n\t\treduce<R>(merge: (last: R, event: T) => R, initial: R): IChainableSythensis<R>;\n\t\treduce<R>(merge: (last: R | undefined, event: T) => R): IChainableSythensis<R>;\n\t\tlatch(equals?: (a: T, b: T) => boolean): IChainableSythensis<T>;\n\t}\n\n\texport interface NodeEventEmitter {\n\t\ton(event: string | symbol, listener: Function): unknown;\n\t\tremoveListener(event: string | symbol, listener: Function): unknown;\n\t}\n\n\t/**\n\t * Creates an {@link Event} from a node event emitter.\n\t */\n\texport function fromNodeEventEmitter<T>(emitter: NodeEventEmitter, eventName: string, map: (...args: any[]) => T = id => id): Event<T> {\n\t\tconst fn = (...args: any[]) => result.fire(map(...args));\n\t\tconst onFirstListenerAdd = () => emitter.on(eventName, fn);\n\t\tconst onLastListenerRemove = () => emitter.removeListener(eventName, fn);\n\t\tconst result = new Emitter<T>({ onWillAddFirstListener: onFirstListenerAdd, onDidRemoveLastListener: onLastListenerRemove });\n\n\t\treturn result.event;\n\t}\n\n\texport interface DOMEventEmitter {\n\t\taddEventListener(event: string | symbol, listener: Function): void;\n\t\tremoveEventListener(event: string | symbol, listener: Function): void;\n\t}\n\n\t/**\n\t * Creates an {@link Event} from a DOM event emitter.\n\t */\n\texport function fromDOMEventEmitter<T>(emitter: DOMEventEmitter, eventName: string, map: (...args: any[]) => T = id => id): Event<T> {\n\t\tconst fn = (...args: any[]) => result.fire(map(...args));\n\t\tconst onFirstListenerAdd = () => emitter.addEventListener(eventName, fn);\n\t\tconst onLastListenerRemove = () => emitter.removeEventListener(eventName, fn);\n\t\tconst result = new Emitter<T>({ onWillAddFirstListener: onFirstListenerAdd, onDidRemoveLastListener: onLastListenerRemove });\n\n\t\treturn result.event;\n\t}\n\n\t/**\n\t * Creates a promise out of an event, using the {@link Event.once} helper.\n\t */\n\texport function toPromise<T>(event: Event<T>): Promise<T> {\n\t\treturn new Promise(resolve => once(event)(resolve));\n\t}\n\n\t/**\n\t * Creates an event out of a promise that fires once when the promise is\n\t * resolved with the result of the promise or `undefined`.\n\t */\n\texport function fromPromise<T>(promise: Promise<T>): Event<T | undefined> {\n\t\tconst result = new Emitter<T | undefined>();\n\n\t\tpromise.then(res => {\n\t\t\tresult.fire(res);\n\t\t}, () => {\n\t\t\tresult.fire(undefined);\n\t\t}).finally(() => {\n\t\t\tresult.dispose();\n\t\t});\n\n\t\treturn result.event;\n\t}\n\n\t/**\n\t * A convenience function for forwarding an event to another emitter which\n\t * improves readability.allows Event.forward(event, emitter) instead of `event(e => emitter.fire(e))`.\n\t * @param from The event to forward.\n\t * @param to The emitter to forward the event to.\n\t * @example\n\t * Event.forward(event, emitter);\n\t * // equivalent to\n\t * event(e => emitter.fire(e));\n\t * // equivalent to\n\t * event(emitter.fire, emitter);\n\t */\n\texport function forward<T>(from: Event<T>, to: Emitter<T>): IDisposable {\n\t\treturn from(e => to.fire(e));\n\t}\n\n\t/**\n\t * Adds a listener to an event and calls the listener immediately with undefined as the event object.\n\t *\n\t * @example\n\t * ```\n\t * // Initialize the UI and update it when dataChangeEvent fires\n\t * runAndSubscribe(dataChangeEvent, () => this._updateUI());\n\t * ```\n\t */\n\texport function runAndSubscribe<T>(event: Event<T>, handler: (e: T) => any, initial: T): IDisposable;\n\texport function runAndSubscribe<T>(event: Event<T>, handler: (e: T | undefined) => any): IDisposable;\n\texport function runAndSubscribe<T>(event: Event<T>, handler: (e: T | undefined) => any, initial?: T): IDisposable {\n\t\thandler(initial);\n\t\treturn event(e => handler(e));\n\t}\n\n\tclass EmitterObserver<T> implements IObserver {\n\n\t\treadonly emitter: Emitter<T>;\n\n\t\tprivate _counter = 0;\n\t\tprivate _hasChanged = false;\n\n\t\tconstructor(readonly _observable: IObservable<T, any>, store: DisposableStore | undefined) {\n\t\t\tconst options: EmitterOptions = {\n\t\t\t\tonWillAddFirstListener: () => {\n\t\t\t\t\t_observable.addObserver(this);\n\t\t\t\t},\n\t\t\t\tonDidRemoveLastListener: () => {\n\t\t\t\t\t_observable.removeObserver(this);\n\t\t\t\t}\n\t\t\t};\n\t\t\tif (!store) {\n\t\t\t\t_addLeakageTraceLogic(options);\n\t\t\t}\n\t\t\tthis.emitter = new Emitter<T>(options);\n\t\t\tif (store) {\n\t\t\t\tstore.add(this.emitter);\n\t\t\t}\n\t\t}\n\n\t\tbeginUpdate<T>(_observable: IObservable<T, void>): void {\n\t\t\t// assert(_observable === this.obs);\n\t\t\tthis._counter++;\n\t\t}\n\n\t\thandlePossibleChange<T>(_observable: IObservable<T, unknown>): void {\n\t\t\t// assert(_observable === this.obs);\n\t\t}\n\n\t\thandleChange<T, TChange>(_observable: IObservable<T, TChange>, _change: TChange): void {\n\t\t\t// assert(_observable === this.obs);\n\t\t\tthis._hasChanged = true;\n\t\t}\n\n\t\tendUpdate<T>(_observable: IObservable<T, void>): void {\n\t\t\t// assert(_observable === this.obs);\n\t\t\tthis._counter--;\n\t\t\tif (this._counter === 0) {\n\t\t\t\tthis._observable.reportChanges();\n\t\t\t\tif (this._hasChanged) {\n\t\t\t\t\tthis._hasChanged = false;\n\t\t\t\t\tthis.emitter.fire(this._observable.get());\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Creates an event emitter that is fired when the observable changes.\n\t * Each listeners subscribes to the emitter.\n\t */\n\texport function fromObservable<T>(obs: IObservable<T, any>, store?: DisposableStore): Event<T> {\n\t\tconst observer = new EmitterObserver(obs, store);\n\t\treturn observer.emitter.event;\n\t}\n\n\t/**\n\t * Each listener is attached to the observable directly.\n\t */\n\texport function fromObservableLight(observable: IObservable<any>): Event<void> {\n\t\treturn (listener, thisArgs, disposables) => {\n\t\t\tlet count = 0;\n\t\t\tlet didChange = false;\n\t\t\tconst observer: IObserver = {\n\t\t\t\tbeginUpdate() {\n\t\t\t\t\tcount++;\n\t\t\t\t},\n\t\t\t\tendUpdate() {\n\t\t\t\t\tcount--;\n\t\t\t\t\tif (count === 0) {\n\t\t\t\t\t\tobservable.reportChanges();\n\t\t\t\t\t\tif (didChange) {\n\t\t\t\t\t\t\tdidChange = false;\n\t\t\t\t\t\t\tlistener.call(thisArgs);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\thandlePossibleChange() {\n\t\t\t\t\t// noop\n\t\t\t\t},\n\t\t\t\thandleChange() {\n\t\t\t\t\tdidChange = true;\n\t\t\t\t}\n\t\t\t};\n\t\t\tobservable.addObserver(observer);\n\t\t\tobservable.reportChanges();\n\t\t\tconst disposable = {\n\t\t\t\tdispose() {\n\t\t\t\t\tobservable.removeObserver(observer);\n\t\t\t\t}\n\t\t\t};\n\n\t\t\tif (disposables instanceof DisposableStore) {\n\t\t\t\tdisposables.add(disposable);\n\t\t\t} else if (Array.isArray(disposables)) {\n\t\t\t\tdisposables.push(disposable);\n\t\t\t}\n\n\t\t\treturn disposable;\n\t\t};\n\t}\n}\n\nexport interface EmitterOptions {\n\t/**\n\t * Optional function that's called *before* the very first listener is added\n\t */\n\tonWillAddFirstListener?: Function;\n\t/**\n\t * Optional function that's called *after* the very first listener is added\n\t */\n\tonDidAddFirstListener?: Function;\n\t/**\n\t * Optional function that's called after a listener is added\n\t */\n\tonDidAddListener?: Function;\n\t/**\n\t * Optional function that's called *after* remove the very last listener\n\t */\n\tonDidRemoveLastListener?: Function;\n\t/**\n\t * Optional function that's called *before* a listener is removed\n\t */\n\tonWillRemoveListener?: Function;\n\t/**\n\t * Optional function that's called when a listener throws an error. Defaults to\n\t * {@link onUnexpectedError}\n\t */\n\tonListenerError?: (e: any) => void;\n\t/**\n\t * Number of listeners that are allowed before assuming a leak. Default to\n\t * a globally configured value\n\t *\n\t * @see setGlobalLeakWarningThreshold\n\t */\n\tleakWarningThreshold?: number;\n\t/**\n\t * Pass in a delivery queue, which is useful for ensuring\n\t * in order event delivery across multiple emitters.\n\t */\n\tdeliveryQueue?: EventDeliveryQueue;\n\n\t/** ONLY enable this during development */\n\t_profName?: string;\n}\n\n\nexport class EventProfiling {\n\n\tstatic readonly all = new Set<EventProfiling>();\n\n\tprivate static _idPool = 0;\n\n\treadonly name: string;\n\tpublic listenerCount: number = 0;\n\tpublic invocationCount = 0;\n\tpublic elapsedOverall = 0;\n\tpublic durations: number[] = [];\n\n\tprivate _stopWatch?: StopWatch;\n\n\tconstructor(name: string) {\n\t\tthis.name = `${name}_${EventProfiling._idPool++}`;\n\t\tEventProfiling.all.add(this);\n\t}\n\n\tstart(listenerCount: number): void {\n\t\tthis._stopWatch = new StopWatch();\n\t\tthis.listenerCount = listenerCount;\n\t}\n\n\tstop(): void {\n\t\tif (this._stopWatch) {\n\t\t\tconst elapsed = this._stopWatch.elapsed();\n\t\t\tthis.durations.push(elapsed);\n\t\t\tthis.elapsedOverall += elapsed;\n\t\t\tthis.invocationCount += 1;\n\t\t\tthis._stopWatch = undefined;\n\t\t}\n\t}\n}\n\nlet _globalLeakWarningThreshold = -1;\nexport function setGlobalLeakWarningThreshold(n: number): IDisposable {\n\tconst oldValue = _globalLeakWarningThreshold;\n\t_globalLeakWarningThreshold = n;\n\treturn {\n\t\tdispose() {\n\t\t\t_globalLeakWarningThreshold = oldValue;\n\t\t}\n\t};\n}\n\nclass LeakageMonitor {\n\n\tprivate static _idPool = 1;\n\n\tprivate _stacks: Map<string, number> | undefined;\n\tprivate _warnCountdown: number = 0;\n\n\tconstructor(\n\t\tprivate readonly _errorHandler: (err: Error) => void,\n\t\treadonly threshold: number,\n\t\treadonly name: string = (LeakageMonitor._idPool++).toString(16).padStart(3, '0')\n\t) { }\n\n\tdispose(): void {\n\t\tthis._stacks?.clear();\n\t}\n\n\tcheck(stack: Stacktrace, listenerCount: number): undefined | (() => void) {\n\n\t\tconst threshold = this.threshold;\n\t\tif (threshold <= 0 || listenerCount < threshold) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tif (!this._stacks) {\n\t\t\tthis._stacks = new Map();\n\t\t}\n\t\tconst count = (this._stacks.get(stack.value) || 0);\n\t\tthis._stacks.set(stack.value, count + 1);\n\t\tthis._warnCountdown -= 1;\n\n\t\tif (this._warnCountdown <= 0) {\n\t\t\t// only warn on first exceed and then every time the limit\n\t\t\t// is exceeded by 50% again\n\t\t\tthis._warnCountdown = threshold * 0.5;\n\n\t\t\tconst [topStack, topCount] = this.getMostFrequentStack()!;\n\t\t\tconst message = `[${this.name}] potential listener LEAK detected, having ${listenerCount} listeners already. MOST frequent listener (${topCount}):`;\n\t\t\tconsole.warn(message);\n\t\t\tconsole.warn(topStack!);\n\n\t\t\tconst error = new ListenerLeakError(message, topStack);\n\t\t\tthis._errorHandler(error);\n\t\t}\n\n\t\treturn () => {\n\t\t\tconst count = (this._stacks!.get(stack.value) || 0);\n\t\t\tthis._stacks!.set(stack.value, count - 1);\n\t\t};\n\t}\n\n\tgetMostFrequentStack(): [string, number] | undefined {\n\t\tif (!this._stacks) {\n\t\t\treturn undefined;\n\t\t}\n\t\tlet topStack: [string, number] | undefined;\n\t\tlet topCount: number = 0;\n\t\tfor (const [stack, count] of this._stacks) {\n\t\t\tif (!topStack || topCount < count) {\n\t\t\t\ttopStack = [stack, count];\n\t\t\t\ttopCount = count;\n\t\t\t}\n\t\t}\n\t\treturn topStack;\n\t}\n}\n\nclass Stacktrace {\n\n\tstatic create() {\n\t\tconst err = new Error();\n\t\treturn new Stacktrace(err.stack ?? '');\n\t}\n\n\tprivate constructor(readonly value: string) { }\n\n\tprint() {\n\t\tconsole.warn(this.value.split('\\n').slice(2).join('\\n'));\n\t}\n}\n\n// error that is logged when going over the configured listener threshold\nexport class ListenerLeakError extends Error {\n\tconstructor(message: string, stack: string) {\n\t\tsuper(message);\n\t\tthis.name = 'ListenerLeakError';\n\t\tthis.stack = stack;\n\t}\n}\n\n// SEVERE error that is logged when having gone way over the configured listener\n// threshold so that the emitter refuses to accept more listeners\nexport class ListenerRefusalError extends Error {\n\tconstructor(message: string, stack: string) {\n\t\tsuper(message);\n\t\tthis.name = 'ListenerRefusalError';\n\t\tthis.stack = stack;\n\t}\n}\n\nlet id = 0;\nclass UniqueContainer<T> {\n\tstack?: Stacktrace;\n\tpublic id = id++;\n\tconstructor(public readonly value: T) { }\n}\nconst compactionThreshold = 2;\n\ntype ListenerContainer<T> = UniqueContainer<(data: T) => void>;\ntype ListenerOrListeners<T> = (ListenerContainer<T> | undefined)[] | ListenerContainer<T>;\n\nconst forEachListener = <T>(listeners: ListenerOrListeners<T>, fn: (c: ListenerContainer<T>) => void) => {\n\tif (listeners instanceof UniqueContainer) {\n\t\tfn(listeners);\n\t} else {\n\t\tfor (let i = 0; i < listeners.length; i++) {\n\t\t\tconst l = listeners[i];\n\t\t\tif (l) {\n\t\t\t\tfn(l);\n\t\t\t}\n\t\t}\n\t}\n};\n\n\nlet _listenerFinalizers: FinalizationRegistry<string> | undefined;\n\nif (_enableListenerGCedWarning) {\n\tconst leaks: string[] = [];\n\n\tsetInterval(() => {\n\t\tif (leaks.length === 0) {\n\t\t\treturn;\n\t\t}\n\t\tconsole.warn('[LEAKING LISTENERS] GC\\'ed these listeners that were NOT yet disposed:');\n\t\tconsole.warn(leaks.join('\\n'));\n\t\tleaks.length = 0;\n\t}, 3000);\n\n\t_listenerFinalizers = new FinalizationRegistry(heldValue => {\n\t\tif (typeof heldValue === 'string') {\n\t\t\tleaks.push(heldValue);\n\t\t}\n\t});\n}\n\n/**\n * The Emitter can be used to expose an Event to the public\n * to fire it from the insides.\n * Sample:\n\tclass Document {\n\n\t\tprivate readonly _onDidChange = new Emitter<(value:string)=>any>();\n\n\t\tpublic onDidChange = this._onDidChange.event;\n\n\t\t// getter-style\n\t\t// get onDidChange(): Event<(value:string)=>any> {\n\t\t// \treturn this._onDidChange.event;\n\t\t// }\n\n\t\tprivate _doIt() {\n\t\t\t//...\n\t\t\tthis._onDidChange.fire(value);\n\t\t}\n\t}\n */\nexport class Emitter<T> {\n\n\tprivate readonly _options?: EmitterOptions;\n\tprivate readonly _leakageMon?: LeakageMonitor;\n\tprivate readonly _perfMon?: EventProfiling;\n\tprivate _disposed?: true;\n\tprivate _event?: Event<T>;\n\n\t/**\n\t * A listener, or list of listeners. A single listener is the most common\n\t * for event emitters (#185789), so we optimize that special case to avoid\n\t * wrapping it in an array (just like Node.js itself.)\n\t *\n\t * A list of listeners never 'downgrades' back to a plain function if\n\t * listeners are removed, for two reasons:\n\t *\n\t *  1. That's complicated (especially with the deliveryQueue)\n\t *  2. A listener with >1 listener is likely to have >1 listener again at\n\t *     some point, and swapping between arrays and functions may[citation needed]\n\t *     introduce unnecessary work and garbage.\n\t *\n\t * The array listeners can be 'sparse', to avoid reallocating the array\n\t * whenever any listener is added or removed. If more than `1 / compactionThreshold`\n\t * of the array is empty, only then is it resized.\n\t */\n\tprotected _listeners?: ListenerOrListeners<T>;\n\n\t/**\n\t * Always to be defined if _listeners is an array. It's no longer a true\n\t * queue, but holds the dispatching 'state'. If `fire()` is called on an\n\t * emitter, any work left in the _deliveryQueue is finished first.\n\t */\n\tprivate _deliveryQueue?: EventDeliveryQueuePrivate;\n\tprotected _size = 0;\n\n\tconstructor(options?: EmitterOptions) {\n\t\tthis._options = options;\n\t\tthis._leakageMon = (_globalLeakWarningThreshold > 0 || this._options?.leakWarningThreshold)\n\t\t\t? new LeakageMonitor(options?.onListenerError ?? onUnexpectedError, this._options?.leakWarningThreshold ?? _globalLeakWarningThreshold) :\n\t\t\tundefined;\n\t\tthis._perfMon = this._options?._profName ? new EventProfiling(this._options._profName) : undefined;\n\t\tthis._deliveryQueue = this._options?.deliveryQueue as EventDeliveryQueuePrivate | undefined;\n\t}\n\n\tdispose() {\n\t\tif (!this._disposed) {\n\t\t\tthis._disposed = true;\n\n\t\t\t// It is bad to have listeners at the time of disposing an emitter, it is worst to have listeners keep the emitter\n\t\t\t// alive via the reference that's embedded in their disposables. Therefore we loop over all remaining listeners and\n\t\t\t// unset their subscriptions/disposables. Looping and blaming remaining listeners is done on next tick because the\n\t\t\t// the following programming pattern is very popular:\n\t\t\t//\n\t\t\t// const someModel = this._disposables.add(new ModelObject()); // (1) create and register model\n\t\t\t// this._disposables.add(someModel.onDidChange(() => { ... }); // (2) subscribe and register model-event listener\n\t\t\t// ...later...\n\t\t\t// this._disposables.dispose(); disposes (1) then (2): don't warn after (1) but after the \"overall dispose\" is done\n\n\t\t\tif (this._deliveryQueue?.current === this) {\n\t\t\t\tthis._deliveryQueue.reset();\n\t\t\t}\n\t\t\tif (this._listeners) {\n\t\t\t\tif (_enableDisposeWithListenerWarning) {\n\t\t\t\t\tconst listeners = this._listeners;\n\t\t\t\t\tqueueMicrotask(() => {\n\t\t\t\t\t\tforEachListener(listeners, l => l.stack?.print());\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\tthis._listeners = undefined;\n\t\t\t\tthis._size = 0;\n\t\t\t}\n\t\t\tthis._options?.onDidRemoveLastListener?.();\n\t\t\tthis._leakageMon?.dispose();\n\t\t}\n\t}\n\n\t/**\n\t * For the public to allow to subscribe\n\t * to events from this Emitter\n\t */\n\tget event(): Event<T> {\n\t\tthis._event ??= (callback: (e: T) => any, thisArgs?: any, disposables?: IDisposable[] | DisposableStore) => {\n\t\t\tif (this._leakageMon && this._size > this._leakageMon.threshold ** 2) {\n\t\t\t\tconst message = `[${this._leakageMon.name}] REFUSES to accept new listeners because it exceeded its threshold by far (${this._size} vs ${this._leakageMon.threshold})`;\n\t\t\t\tconsole.warn(message);\n\n\t\t\t\tconst tuple = this._leakageMon.getMostFrequentStack() ?? ['UNKNOWN stack', -1];\n\t\t\t\tconst error = new ListenerRefusalError(`${message}. HINT: Stack shows most frequent listener (${tuple[1]}-times)`, tuple[0]);\n\t\t\t\tconst errorHandler = this._options?.onListenerError || onUnexpectedError;\n\t\t\t\terrorHandler(error);\n\n\t\t\t\treturn Disposable.None;\n\t\t\t}\n\n\t\t\tif (this._disposed) {\n\t\t\t\t// todo: should we warn if a listener is added to a disposed emitter? This happens often\n\t\t\t\treturn Disposable.None;\n\t\t\t}\n\n\t\t\tif (thisArgs) {\n\t\t\t\tcallback = callback.bind(thisArgs);\n\t\t\t}\n\n\t\t\tconst contained = new UniqueContainer(callback);\n\n\t\t\tlet removeMonitor: Function | undefined;\n\t\t\tlet stack: Stacktrace | undefined;\n\t\t\tif (this._leakageMon && this._size >= Math.ceil(this._leakageMon.threshold * 0.2)) {\n\t\t\t\t// check and record this emitter for potential leakage\n\t\t\t\tcontained.stack = Stacktrace.create();\n\t\t\t\tremoveMonitor = this._leakageMon.check(contained.stack, this._size + 1);\n\t\t\t}\n\n\t\t\tif (_enableDisposeWithListenerWarning) {\n\t\t\t\tcontained.stack = stack ?? Stacktrace.create();\n\t\t\t}\n\n\t\t\tif (!this._listeners) {\n\t\t\t\tthis._options?.onWillAddFirstListener?.(this);\n\t\t\t\tthis._listeners = contained;\n\t\t\t\tthis._options?.onDidAddFirstListener?.(this);\n\t\t\t} else if (this._listeners instanceof UniqueContainer) {\n\t\t\t\tthis._deliveryQueue ??= new EventDeliveryQueuePrivate();\n\t\t\t\tthis._listeners = [this._listeners, contained];\n\t\t\t} else {\n\t\t\t\tthis._listeners.push(contained);\n\t\t\t}\n\n\t\t\tthis._size++;\n\n\n\t\t\tconst result = toDisposable(() => {\n\t\t\t\t_listenerFinalizers?.unregister(result);\n\t\t\t\tremoveMonitor?.();\n\t\t\t\tthis._removeListener(contained);\n\t\t\t});\n\t\t\tif (disposables instanceof DisposableStore) {\n\t\t\t\tdisposables.add(result);\n\t\t\t} else if (Array.isArray(disposables)) {\n\t\t\t\tdisposables.push(result);\n\t\t\t}\n\n\t\t\tif (_listenerFinalizers) {\n\t\t\t\tconst stack = new Error().stack!.split('\\n').slice(2, 3).join('\\n').trim();\n\t\t\t\tconst match = /(file:|vscode-file:\\/\\/vscode-app)?(\\/[^:]*:\\d+:\\d+)/.exec(stack);\n\t\t\t\t_listenerFinalizers.register(result, match?.[2] ?? stack, result);\n\t\t\t}\n\n\t\t\treturn result;\n\t\t};\n\n\t\treturn this._event;\n\t}\n\n\tprivate _removeListener(listener: ListenerContainer<T>) {\n\t\tthis._options?.onWillRemoveListener?.(this);\n\n\t\tif (!this._listeners) {\n\t\t\treturn; // expected if a listener gets disposed\n\t\t}\n\n\t\tif (this._size === 1) {\n\t\t\tthis._listeners = undefined;\n\t\t\tthis._options?.onDidRemoveLastListener?.(this);\n\t\t\tthis._size = 0;\n\t\t\treturn;\n\t\t}\n\n\t\t// size > 1 which requires that listeners be a list:\n\t\tconst listeners = this._listeners as (ListenerContainer<T> | undefined)[];\n\n\t\tconst index = listeners.indexOf(listener);\n\t\tif (index === -1) {\n\t\t\tconsole.log('disposed?', this._disposed);\n\t\t\tconsole.log('size?', this._size);\n\t\t\tconsole.log('arr?', JSON.stringify(this._listeners));\n\t\t\tthrow new Error('Attempted to dispose unknown listener');\n\t\t}\n\n\t\tthis._size--;\n\t\tlisteners[index] = undefined;\n\n\t\tconst adjustDeliveryQueue = this._deliveryQueue!.current === this;\n\t\tif (this._size * compactionThreshold <= listeners.length) {\n\t\t\tlet n = 0;\n\t\t\tfor (let i = 0; i < listeners.length; i++) {\n\t\t\t\tif (listeners[i]) {\n\t\t\t\t\tlisteners[n++] = listeners[i];\n\t\t\t\t} else if (adjustDeliveryQueue) {\n\t\t\t\t\tthis._deliveryQueue!.end--;\n\t\t\t\t\tif (n < this._deliveryQueue!.i) {\n\t\t\t\t\t\tthis._deliveryQueue!.i--;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tlisteners.length = n;\n\t\t}\n\t}\n\n\tprivate _deliver(listener: undefined | UniqueContainer<(value: T) => void>, value: T) {\n\t\tif (!listener) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst errorHandler = this._options?.onListenerError || onUnexpectedError;\n\t\tif (!errorHandler) {\n\t\t\tlistener.value(value);\n\t\t\treturn;\n\t\t}\n\n\t\ttry {\n\t\t\tlistener.value(value);\n\t\t} catch (e) {\n\t\t\terrorHandler(e);\n\t\t}\n\t}\n\n\t/** Delivers items in the queue. Assumes the queue is ready to go. */\n\tprivate _deliverQueue(dq: EventDeliveryQueuePrivate) {\n\t\tconst listeners = dq.current!._listeners! as (ListenerContainer<T> | undefined)[];\n\t\twhile (dq.i < dq.end) {\n\t\t\t// important: dq.i is incremented before calling deliver() because it might reenter deliverQueue()\n\t\t\tthis._deliver(listeners[dq.i++], dq.value as T);\n\t\t}\n\t\tdq.reset();\n\t}\n\n\t/**\n\t * To be kept private to fire an event to\n\t * subscribers\n\t */\n\tfire(event: T): void {\n\t\tif (this._deliveryQueue?.current) {\n\t\t\tthis._deliverQueue(this._deliveryQueue);\n\t\t\tthis._perfMon?.stop(); // last fire() will have starting perfmon, stop it before starting the next dispatch\n\t\t}\n\n\t\tthis._perfMon?.start(this._size);\n\n\t\tif (!this._listeners) {\n\t\t\t// no-op\n\t\t} else if (this._listeners instanceof UniqueContainer) {\n\t\t\tthis._deliver(this._listeners, event);\n\t\t} else {\n\t\t\tconst dq = this._deliveryQueue!;\n\t\t\tdq.enqueue(this, event, this._listeners.length);\n\t\t\tthis._deliverQueue(dq);\n\t\t}\n\n\t\tthis._perfMon?.stop();\n\t}\n\n\thasListeners(): boolean {\n\t\treturn this._size > 0;\n\t}\n}\n\nexport interface EventDeliveryQueue {\n\t_isEventDeliveryQueue: true;\n}\n\nexport const createEventDeliveryQueue = (): EventDeliveryQueue => new EventDeliveryQueuePrivate();\n\nclass EventDeliveryQueuePrivate implements EventDeliveryQueue {\n\tdeclare _isEventDeliveryQueue: true;\n\n\t/**\n\t * Index in current's listener list.\n\t */\n\tpublic i = -1;\n\n\t/**\n\t * The last index in the listener's list to deliver.\n\t */\n\tpublic end = 0;\n\n\t/**\n\t * Emitter currently being dispatched on. Emitter._listeners is always an array.\n\t */\n\tpublic current?: Emitter<any>;\n\t/**\n\t * Currently emitting value. Defined whenever `current` is.\n\t */\n\tpublic value?: unknown;\n\n\tpublic enqueue<T>(emitter: Emitter<T>, value: T, end: number) {\n\t\tthis.i = 0;\n\t\tthis.end = end;\n\t\tthis.current = emitter;\n\t\tthis.value = value;\n\t}\n\n\tpublic reset() {\n\t\tthis.i = this.end; // force any current emission loop to stop, mainly for during dispose\n\t\tthis.current = undefined;\n\t\tthis.value = undefined;\n\t}\n}\n\nexport interface IWaitUntil {\n\ttoken: CancellationToken;\n\twaitUntil(thenable: Promise<unknown>): void;\n}\n\nexport type IWaitUntilData<T> = Omit<Omit<T, 'waitUntil'>, 'token'>;\n\nexport class AsyncEmitter<T extends IWaitUntil> extends Emitter<T> {\n\n\tprivate _asyncDeliveryQueue?: LinkedList<[(ev: T) => void, IWaitUntilData<T>]>;\n\n\tasync fireAsync(data: IWaitUntilData<T>, token: CancellationToken, promiseJoin?: (p: Promise<unknown>, listener: Function) => Promise<unknown>): Promise<void> {\n\t\tif (!this._listeners) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (!this._asyncDeliveryQueue) {\n\t\t\tthis._asyncDeliveryQueue = new LinkedList();\n\t\t}\n\n\t\tforEachListener(this._listeners, listener => this._asyncDeliveryQueue!.push([listener.value, data]));\n\n\t\twhile (this._asyncDeliveryQueue.size > 0 && !token.isCancellationRequested) {\n\n\t\t\tconst [listener, data] = this._asyncDeliveryQueue.shift()!;\n\t\t\tconst thenables: Promise<unknown>[] = [];\n\n\t\t\tconst event = <T>{\n\t\t\t\t...data,\n\t\t\t\ttoken,\n\t\t\t\twaitUntil: (p: Promise<unknown>): void => {\n\t\t\t\t\tif (Object.isFrozen(thenables)) {\n\t\t\t\t\t\tthrow new Error('waitUntil can NOT be called asynchronous');\n\t\t\t\t\t}\n\t\t\t\t\tif (promiseJoin) {\n\t\t\t\t\t\tp = promiseJoin(p, listener);\n\t\t\t\t\t}\n\t\t\t\t\tthenables.push(p);\n\t\t\t\t}\n\t\t\t};\n\n\t\t\ttry {\n\t\t\t\tlistener(event);\n\t\t\t} catch (e) {\n\t\t\t\tonUnexpectedError(e);\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\t// freeze thenables-collection to enforce sync-calls to\n\t\t\t// wait until and then wait for all thenables to resolve\n\t\t\tObject.freeze(thenables);\n\n\t\t\tawait Promise.allSettled(thenables).then(values => {\n\t\t\t\tfor (const value of values) {\n\t\t\t\t\tif (value.status === 'rejected') {\n\t\t\t\t\t\tonUnexpectedError(value.reason);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t}\n}\n\n\nexport class PauseableEmitter<T> extends Emitter<T> {\n\n\tprivate _isPaused = 0;\n\tprotected _eventQueue = new LinkedList<T>();\n\tprivate _mergeFn?: (input: T[]) => T;\n\n\tpublic get isPaused(): boolean {\n\t\treturn this._isPaused !== 0;\n\t}\n\n\tconstructor(options?: EmitterOptions & { merge?: (input: T[]) => T }) {\n\t\tsuper(options);\n\t\tthis._mergeFn = options?.merge;\n\t}\n\n\tpause(): void {\n\t\tthis._isPaused++;\n\t}\n\n\tresume(): void {\n\t\tif (this._isPaused !== 0 && --this._isPaused === 0) {\n\t\t\tif (this._mergeFn) {\n\t\t\t\t// use the merge function to create a single composite\n\t\t\t\t// event. make a copy in case firing pauses this emitter\n\t\t\t\tif (this._eventQueue.size > 0) {\n\t\t\t\t\tconst events = Array.from(this._eventQueue);\n\t\t\t\t\tthis._eventQueue.clear();\n\t\t\t\t\tsuper.fire(this._mergeFn(events));\n\t\t\t\t}\n\n\t\t\t} else {\n\t\t\t\t// no merging, fire each event individually and test\n\t\t\t\t// that this emitter isn't paused halfway through\n\t\t\t\twhile (!this._isPaused && this._eventQueue.size !== 0) {\n\t\t\t\t\tsuper.fire(this._eventQueue.shift()!);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\toverride fire(event: T): void {\n\t\tif (this._size) {\n\t\t\tif (this._isPaused !== 0) {\n\t\t\t\tthis._eventQueue.push(event);\n\t\t\t} else {\n\t\t\t\tsuper.fire(event);\n\t\t\t}\n\t\t}\n\t}\n}\n\nexport class DebounceEmitter<T> extends PauseableEmitter<T> {\n\n\tprivate readonly _delay: number;\n\tprivate _handle: any | undefined;\n\n\tconstructor(options: EmitterOptions & { merge: (input: T[]) => T; delay?: number }) {\n\t\tsuper(options);\n\t\tthis._delay = options.delay ?? 100;\n\t}\n\n\toverride fire(event: T): void {\n\t\tif (!this._handle) {\n\t\t\tthis.pause();\n\t\t\tthis._handle = setTimeout(() => {\n\t\t\t\tthis._handle = undefined;\n\t\t\t\tthis.resume();\n\t\t\t}, this._delay);\n\t\t}\n\t\tsuper.fire(event);\n\t}\n}\n\n/**\n * An emitter which queue all events and then process them at the\n * end of the event loop.\n */\nexport class MicrotaskEmitter<T> extends Emitter<T> {\n\tprivate _queuedEvents: T[] = [];\n\tprivate _mergeFn?: (input: T[]) => T;\n\n\tconstructor(options?: EmitterOptions & { merge?: (input: T[]) => T }) {\n\t\tsuper(options);\n\t\tthis._mergeFn = options?.merge;\n\t}\n\toverride fire(event: T): void {\n\n\t\tif (!this.hasListeners()) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._queuedEvents.push(event);\n\t\tif (this._queuedEvents.length === 1) {\n\t\t\tqueueMicrotask(() => {\n\t\t\t\tif (this._mergeFn) {\n\t\t\t\t\tsuper.fire(this._mergeFn(this._queuedEvents));\n\t\t\t\t} else {\n\t\t\t\t\tthis._queuedEvents.forEach(e => super.fire(e));\n\t\t\t\t}\n\t\t\t\tthis._queuedEvents = [];\n\t\t\t});\n\t\t}\n\t}\n}\n\n/**\n * An event emitter that multiplexes many events into a single event.\n *\n * @example Listen to the `onData` event of all `Thing`s, dynamically adding and removing `Thing`s\n * to the multiplexer as needed.\n *\n * ```typescript\n * const anythingDataMultiplexer = new EventMultiplexer<{ data: string }>();\n *\n * const thingListeners = DisposableMap<Thing, IDisposable>();\n *\n * thingService.onDidAddThing(thing => {\n *   thingListeners.set(thing, anythingDataMultiplexer.add(thing.onData);\n * });\n * thingService.onDidRemoveThing(thing => {\n *   thingListeners.deleteAndDispose(thing);\n * });\n *\n * anythingDataMultiplexer.event(e => {\n *   console.log('Something fired data ' + e.data)\n * });\n * ```\n */\nexport class EventMultiplexer<T> implements IDisposable {\n\n\tprivate readonly emitter: Emitter<T>;\n\tprivate hasListeners = false;\n\tprivate events: { event: Event<T>; listener: IDisposable | null }[] = [];\n\n\tconstructor() {\n\t\tthis.emitter = new Emitter<T>({\n\t\t\tonWillAddFirstListener: () => this.onFirstListenerAdd(),\n\t\t\tonDidRemoveLastListener: () => this.onLastListenerRemove()\n\t\t});\n\t}\n\n\tget event(): Event<T> {\n\t\treturn this.emitter.event;\n\t}\n\n\tadd(event: Event<T>): IDisposable {\n\t\tconst e = { event: event, listener: null };\n\t\tthis.events.push(e);\n\n\t\tif (this.hasListeners) {\n\t\t\tthis.hook(e);\n\t\t}\n\n\t\tconst dispose = () => {\n\t\t\tif (this.hasListeners) {\n\t\t\t\tthis.unhook(e);\n\t\t\t}\n\n\t\t\tconst idx = this.events.indexOf(e);\n\t\t\tthis.events.splice(idx, 1);\n\t\t};\n\n\t\treturn toDisposable(createSingleCallFunction(dispose));\n\t}\n\n\tprivate onFirstListenerAdd(): void {\n\t\tthis.hasListeners = true;\n\t\tthis.events.forEach(e => this.hook(e));\n\t}\n\n\tprivate onLastListenerRemove(): void {\n\t\tthis.hasListeners = false;\n\t\tthis.events.forEach(e => this.unhook(e));\n\t}\n\n\tprivate hook(e: { event: Event<T>; listener: IDisposable | null }): void {\n\t\te.listener = e.event(r => this.emitter.fire(r));\n\t}\n\n\tprivate unhook(e: { event: Event<T>; listener: IDisposable | null }): void {\n\t\te.listener?.dispose();\n\t\te.listener = null;\n\t}\n\n\tdispose(): void {\n\t\tthis.emitter.dispose();\n\n\t\tfor (const e of this.events) {\n\t\t\te.listener?.dispose();\n\t\t}\n\t\tthis.events = [];\n\t}\n}\n\nexport interface IDynamicListEventMultiplexer<TEventType> extends IDisposable {\n\treadonly event: Event<TEventType>;\n}\nexport class DynamicListEventMultiplexer<TItem, TEventType> implements IDynamicListEventMultiplexer<TEventType> {\n\tprivate readonly _store = new DisposableStore();\n\n\treadonly event: Event<TEventType>;\n\n\tconstructor(\n\t\titems: TItem[],\n\t\tonAddItem: Event<TItem>,\n\t\tonRemoveItem: Event<TItem>,\n\t\tgetEvent: (item: TItem) => Event<TEventType>\n\t) {\n\t\tconst multiplexer = this._store.add(new EventMultiplexer<TEventType>());\n\t\tconst itemListeners = this._store.add(new DisposableMap<TItem, IDisposable>());\n\n\t\tfunction addItem(instance: TItem) {\n\t\t\titemListeners.set(instance, multiplexer.add(getEvent(instance)));\n\t\t}\n\n\t\t// Existing items\n\t\tfor (const instance of items) {\n\t\t\taddItem(instance);\n\t\t}\n\n\t\t// Added items\n\t\tthis._store.add(onAddItem(instance => {\n\t\t\taddItem(instance);\n\t\t}));\n\n\t\t// Removed items\n\t\tthis._store.add(onRemoveItem(instance => {\n\t\t\titemListeners.deleteAndDispose(instance);\n\t\t}));\n\n\t\tthis.event = multiplexer.event;\n\t}\n\n\tdispose() {\n\t\tthis._store.dispose();\n\t}\n}\n\n/**\n * The EventBufferer is useful in situations in which you want\n * to delay firing your events during some code.\n * You can wrap that code and be sure that the event will not\n * be fired during that wrap.\n *\n * ```\n * const emitter: Emitter;\n * const delayer = new EventDelayer();\n * const delayedEvent = delayer.wrapEvent(emitter.event);\n *\n * delayedEvent(console.log);\n *\n * delayer.bufferEvents(() => {\n *   emitter.fire(); // event will not be fired yet\n * });\n *\n * // event will only be fired at this point\n * ```\n */\nexport class EventBufferer {\n\n\tprivate data: { buffers: Function[] }[] = [];\n\n\twrapEvent<T>(event: Event<T>): Event<T>;\n\twrapEvent<T>(event: Event<T>, reduce: (last: T | undefined, event: T) => T): Event<T>;\n\twrapEvent<T, O>(event: Event<T>, reduce: (last: O | undefined, event: T) => O, initial: O): Event<O>;\n\twrapEvent<T, O>(event: Event<T>, reduce?: (last: T | O | undefined, event: T) => T | O, initial?: O): Event<O | T> {\n\t\treturn (listener, thisArgs?, disposables?) => {\n\t\t\treturn event(i => {\n\t\t\t\tconst data = this.data[this.data.length - 1];\n\n\t\t\t\t// Non-reduce scenario\n\t\t\t\tif (!reduce) {\n\t\t\t\t\t// Buffering case\n\t\t\t\t\tif (data) {\n\t\t\t\t\t\tdata.buffers.push(() => listener.call(thisArgs, i));\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// Not buffering case\n\t\t\t\t\t\tlistener.call(thisArgs, i);\n\t\t\t\t\t}\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// Reduce scenario\n\t\t\t\tconst reduceData = data as typeof data & {\n\t\t\t\t\t/**\n\t\t\t\t\t * The accumulated items that will be reduced.\n\t\t\t\t\t */\n\t\t\t\t\titems?: T[];\n\t\t\t\t\t/**\n\t\t\t\t\t * The reduced result cached to be shared with other listeners.\n\t\t\t\t\t */\n\t\t\t\t\treducedResult?: T | O;\n\t\t\t\t};\n\n\t\t\t\t// Not buffering case\n\t\t\t\tif (!reduceData) {\n\t\t\t\t\t// TODO: Is there a way to cache this reduce call for all listeners?\n\t\t\t\t\tlistener.call(thisArgs, reduce(initial, i));\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// Buffering case\n\t\t\t\treduceData.items ??= [];\n\t\t\t\treduceData.items.push(i);\n\t\t\t\tif (reduceData.buffers.length === 0) {\n\t\t\t\t\t// Include a single buffered function that will reduce all events when we're done buffering events\n\t\t\t\t\tdata.buffers.push(() => {\n\t\t\t\t\t\t// cache the reduced result so that the value can be shared across all listeners\n\t\t\t\t\t\treduceData.reducedResult ??= initial\n\t\t\t\t\t\t\t? reduceData.items!.reduce(reduce as (last: O | undefined, event: T) => O, initial)\n\t\t\t\t\t\t\t: reduceData.items!.reduce(reduce as (last: T | undefined, event: T) => T);\n\t\t\t\t\t\tlistener.call(thisArgs, reduceData.reducedResult);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}, undefined, disposables);\n\t\t};\n\t}\n\n\tbufferEvents<R = void>(fn: () => R): R {\n\t\tconst data = { buffers: new Array<Function>() };\n\t\tthis.data.push(data);\n\t\tconst r = fn();\n\t\tthis.data.pop();\n\t\tdata.buffers.forEach(flush => flush());\n\t\treturn r;\n\t}\n}\n\n/**\n * A Relay is an event forwarder which functions as a replugabble event pipe.\n * Once created, you can connect an input event to it and it will simply forward\n * events from that input event through its own `event` property. The `input`\n * can be changed at any point in time.\n */\nexport class Relay<T> implements IDisposable {\n\n\tprivate listening = false;\n\tprivate inputEvent: Event<T> = Event.None;\n\tprivate inputEventListener: IDisposable = Disposable.None;\n\n\tprivate readonly emitter = new Emitter<T>({\n\t\tonDidAddFirstListener: () => {\n\t\t\tthis.listening = true;\n\t\t\tthis.inputEventListener = this.inputEvent(this.emitter.fire, this.emitter);\n\t\t},\n\t\tonDidRemoveLastListener: () => {\n\t\t\tthis.listening = false;\n\t\t\tthis.inputEventListener.dispose();\n\t\t}\n\t});\n\n\treadonly event: Event<T> = this.emitter.event;\n\n\tset input(event: Event<T>) {\n\t\tthis.inputEvent = event;\n\n\t\tif (this.listening) {\n\t\t\tthis.inputEventListener.dispose();\n\t\t\tthis.inputEventListener = event(this.emitter.fire, this.emitter);\n\t\t}\n\t}\n\n\tdispose() {\n\t\tthis.inputEventListener.dispose();\n\t\tthis.emitter.dispose();\n\t}\n}\n\nexport interface IValueWithChangeEvent<T> {\n\treadonly onDidChange: Event<void>;\n\tget value(): T;\n}\n\nexport class ValueWithChangeEvent<T> implements IValueWithChangeEvent<T> {\n\tpublic static const<T>(value: T): IValueWithChangeEvent<T> {\n\t\treturn new ConstValueWithChangeEvent(value);\n\t}\n\n\tprivate readonly _onDidChange = new Emitter<void>();\n\treadonly onDidChange: Event<void> = this._onDidChange.event;\n\n\tconstructor(private _value: T) { }\n\n\tget value(): T {\n\t\treturn this._value;\n\t}\n\n\tset value(value: T) {\n\t\tif (value !== this._value) {\n\t\t\tthis._value = value;\n\t\t\tthis._onDidChange.fire(undefined);\n\t\t}\n\t}\n}\n\nclass ConstValueWithChangeEvent<T> implements IValueWithChangeEvent<T> {\n\tpublic readonly onDidChange: Event<void> = Event.None;\n\n\tconstructor(readonly value: T) { }\n}\n", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport type { Terminal, IDisposable, ITerminalAddon, IDecoration } from '@xterm/xterm';\nimport type { SearchAddon as ISearchApi } from '@xterm/addon-search';\nimport { Emitter } from 'vs/base/common/event';\nimport { combinedDisposable, Disposable, dispose, MutableDisposable, toDisposable } from 'vs/base/common/lifecycle';\n\nexport interface ISearchOptions {\n  regex?: boolean;\n  wholeWord?: boolean;\n  caseSensitive?: boolean;\n  incremental?: boolean;\n  decorations?: ISearchDecorationOptions;\n  noScroll?: boolean;\n}\n\ninterface ISearchDecorationOptions {\n  matchBackground?: string;\n  matchBorder?: string;\n  matchOverviewRuler: string;\n  activeMatchBackground?: string;\n  activeMatchBorder?: string;\n  activeMatchColorOverviewRuler: string;\n}\n\nexport interface ISearchPosition {\n  startCol: number;\n  startRow: number;\n}\n\nexport interface ISearchAddonOptions {\n  highlightLimit: number;\n}\n\nexport interface ISearchResult {\n  term: string;\n  col: number;\n  row: number;\n  size: number;\n}\n\ntype LineCacheEntry = [\n  /**\n   * The string representation of a line (as opposed to the buffer cell representation).\n   */\n  lineAsString: string,\n  /**\n   * The offsets where each line starts when the entry describes a wrapped line.\n   */\n  lineOffsets: number[]\n];\n\ninterface IHighlight extends IDisposable {\n  decoration: IDecoration;\n  match: ISearchResult;\n}\n\ninterface IMultiHighlight extends IDisposable {\n  decorations: IDecoration[];\n  match: ISearchResult;\n}\n\nconst NON_WORD_CHARACTERS = ' ~!@#$%^&*()+`-=[]{}|\\\\;:\"\\',./<>?';\nconst LINES_CACHE_TIME_TO_LIVE = 15 * 1000; // 15 secs\nconst DEFAULT_HIGHLIGHT_LIMIT = 1000;\n\nexport class SearchAddon extends Disposable implements ITerminalAddon , ISearchApi {\n  private _terminal: Terminal | undefined;\n  private _cachedSearchTerm: string | undefined;\n  private _highlightedLines: Set<number> = new Set();\n  private _highlightDecorations: IHighlight[] = [];\n  private _selectedDecoration: MutableDisposable<IMultiHighlight> = this._register(new MutableDisposable());\n  private _highlightLimit: number;\n  private _lastSearchOptions: ISearchOptions | undefined;\n  private _highlightTimeout: number | undefined;\n  /**\n   * translateBufferLineToStringWithWrap is a fairly expensive call.\n   * We memoize the calls into an array that has a time based ttl.\n   * _linesCache is also invalidated when the terminal cursor moves.\n   */\n  private _linesCache: LineCacheEntry[] | undefined;\n  private _linesCacheTimeoutId = 0;\n  private _linesCacheDisposables = new MutableDisposable();\n\n  private readonly _onDidChangeResults = this._register(new Emitter<{ resultIndex: number, resultCount: number }>());\n  public readonly onDidChangeResults = this._onDidChangeResults.event;\n\n  constructor(options?: Partial<ISearchAddonOptions>) {\n    super();\n\n    this._highlightLimit = options?.highlightLimit ?? DEFAULT_HIGHLIGHT_LIMIT;\n  }\n\n  public activate(terminal: Terminal): void {\n    this._terminal = terminal;\n    this._register(this._terminal.onWriteParsed(() => this._updateMatches()));\n    this._register(this._terminal.onResize(() => this._updateMatches()));\n    this._register(toDisposable(() => this.clearDecorations()));\n  }\n\n  private _updateMatches(): void {\n    if (this._highlightTimeout) {\n      window.clearTimeout(this._highlightTimeout);\n    }\n    if (this._cachedSearchTerm && this._lastSearchOptions?.decorations) {\n      this._highlightTimeout = setTimeout(() => {\n        const term = this._cachedSearchTerm;\n        this._cachedSearchTerm = undefined;\n        this.findPrevious(term!, { ...this._lastSearchOptions, incremental: true, noScroll: true });\n      }, 200);\n    }\n  }\n\n  public clearDecorations(retainCachedSearchTerm?: boolean): void {\n    this._selectedDecoration.clear();\n    dispose(this._highlightDecorations);\n    this._highlightDecorations = [];\n    this._highlightedLines.clear();\n    if (!retainCachedSearchTerm) {\n      this._cachedSearchTerm = undefined;\n    }\n  }\n\n  public clearActiveDecoration(): void {\n    this._selectedDecoration.clear();\n  }\n\n  /**\n   * Find the next instance of the term, then scroll to and select it. If it\n   * doesn't exist, do nothing.\n   * @param term The search term.\n   * @param searchOptions Search options.\n   * @returns Whether a result was found.\n   */\n  public findNext(term: string, searchOptions?: ISearchOptions): boolean {\n    if (!this._terminal) {\n      throw new Error('Cannot use addon until it has been loaded');\n    }\n    const didOptionsChanged = this._lastSearchOptions ? this._didOptionsChange(this._lastSearchOptions, searchOptions) : true;\n    this._lastSearchOptions = searchOptions;\n    if (searchOptions?.decorations) {\n      if (this._cachedSearchTerm === undefined || term !== this._cachedSearchTerm || didOptionsChanged) {\n        this._highlightAllMatches(term, searchOptions);\n      }\n    }\n\n    const found = this._findNextAndSelect(term, searchOptions);\n    this._fireResults(searchOptions);\n    this._cachedSearchTerm = term;\n\n    return found;\n  }\n\n  private _highlightAllMatches(term: string, searchOptions: ISearchOptions): void {\n    if (!this._terminal) {\n      throw new Error('Cannot use addon until it has been loaded');\n    }\n    if (!term || term.length === 0) {\n      this.clearDecorations();\n      return;\n    }\n    searchOptions = searchOptions || {};\n\n    // new search, clear out the old decorations\n    this.clearDecorations(true);\n\n    const searchResultsWithHighlight: ISearchResult[] = [];\n    let prevResult: ISearchResult | undefined = undefined;\n    let result = this._find(term, 0, 0, searchOptions);\n    while (result && (prevResult?.row !== result.row || prevResult?.col !== result.col)) {\n      if (searchResultsWithHighlight.length >= this._highlightLimit) {\n        break;\n      }\n      prevResult = result;\n      searchResultsWithHighlight.push(prevResult);\n      result = this._find(\n        term,\n        prevResult.col + prevResult.term.length >= this._terminal.cols ? prevResult.row + 1 : prevResult.row,\n        prevResult.col + prevResult.term.length >= this._terminal.cols ? 0 : prevResult.col + 1,\n        searchOptions\n      );\n    }\n    for (const match of searchResultsWithHighlight) {\n      const decorations = this._createResultDecorations(match, searchOptions.decorations!, false);\n      if (decorations) {\n        for (const decoration of decorations) {\n          this._storeDecoration(decoration, match);\n        }\n      }\n    }\n  }\n\n  private _storeDecoration(decoration: IDecoration, match: ISearchResult): void {\n    this._highlightedLines.add(decoration.marker.line);\n    this._highlightDecorations.push({ decoration, match, dispose() { decoration.dispose(); } });\n  }\n\n  private _find(term: string, startRow: number, startCol: number, searchOptions?: ISearchOptions): ISearchResult | undefined {\n    if (!this._terminal || !term || term.length === 0) {\n      this._terminal?.clearSelection();\n      this.clearDecorations();\n      return undefined;\n    }\n    if (startCol > this._terminal.cols) {\n      throw new Error(`Invalid col: ${startCol} to search in terminal of ${this._terminal.cols} cols`);\n    }\n\n    let result: ISearchResult | undefined = undefined;\n\n    this._initLinesCache();\n\n    const searchPosition: ISearchPosition = {\n      startRow,\n      startCol\n    };\n\n    // Search startRow\n    result = this._findInLine(term, searchPosition, searchOptions);\n    // Search from startRow + 1 to end\n    if (!result) {\n\n      for (let y = startRow + 1; y < this._terminal.buffer.active.baseY + this._terminal.rows; y++) {\n        searchPosition.startRow = y;\n        searchPosition.startCol = 0;\n        // If the current line is wrapped line, increase index of column to ignore the previous scan\n        // Otherwise, reset beginning column index to zero with set new unwrapped line index\n        result = this._findInLine(term, searchPosition, searchOptions);\n        if (result) {\n          break;\n        }\n      }\n    }\n    return result;\n  }\n\n  private _findNextAndSelect(term: string, searchOptions?: ISearchOptions): boolean {\n    if (!this._terminal || !term || term.length === 0) {\n      this._terminal?.clearSelection();\n      this.clearDecorations();\n      return false;\n    }\n\n    const prevSelectedPos = this._terminal.getSelectionPosition();\n    this._terminal.clearSelection();\n\n    let startCol = 0;\n    let startRow = 0;\n    if (prevSelectedPos) {\n      if (this._cachedSearchTerm === term) {\n        startCol = prevSelectedPos.end.x;\n        startRow = prevSelectedPos.end.y;\n      } else {\n        startCol = prevSelectedPos.start.x;\n        startRow = prevSelectedPos.start.y;\n      }\n    }\n\n    this._initLinesCache();\n\n    const searchPosition: ISearchPosition = {\n      startRow,\n      startCol\n    };\n\n    // Search startRow\n    let result = this._findInLine(term, searchPosition, searchOptions);\n    // Search from startRow + 1 to end\n    if (!result) {\n\n      for (let y = startRow + 1; y < this._terminal.buffer.active.baseY + this._terminal.rows; y++) {\n        searchPosition.startRow = y;\n        searchPosition.startCol = 0;\n        // If the current line is wrapped line, increase index of column to ignore the previous scan\n        // Otherwise, reset beginning column index to zero with set new unwrapped line index\n        result = this._findInLine(term, searchPosition, searchOptions);\n        if (result) {\n          break;\n        }\n      }\n    }\n    // If we hit the bottom and didn't search from the very top wrap back up\n    if (!result && startRow !== 0) {\n      for (let y = 0; y < startRow; y++) {\n        searchPosition.startRow = y;\n        searchPosition.startCol = 0;\n        result = this._findInLine(term, searchPosition, searchOptions);\n        if (result) {\n          break;\n        }\n      }\n    }\n\n    // If there is only one result, wrap back and return selection if it exists.\n    if (!result && prevSelectedPos) {\n      searchPosition.startRow = prevSelectedPos.start.y;\n      searchPosition.startCol = 0;\n      result = this._findInLine(term, searchPosition, searchOptions);\n    }\n\n    // Set selection and scroll if a result was found\n    return this._selectResult(result, searchOptions?.decorations, searchOptions?.noScroll);\n  }\n  /**\n   * Find the previous instance of the term, then scroll to and select it. If it\n   * doesn't exist, do nothing.\n   * @param term The search term.\n   * @param searchOptions Search options.\n   * @returns Whether a result was found.\n   */\n  public findPrevious(term: string, searchOptions?: ISearchOptions): boolean {\n    if (!this._terminal) {\n      throw new Error('Cannot use addon until it has been loaded');\n    }\n    const didOptionsChanged = this._lastSearchOptions ? this._didOptionsChange(this._lastSearchOptions, searchOptions) : true;\n    this._lastSearchOptions = searchOptions;\n    if (searchOptions?.decorations) {\n      if (this._cachedSearchTerm === undefined || term !== this._cachedSearchTerm || didOptionsChanged) {\n        this._highlightAllMatches(term, searchOptions);\n      }\n    }\n\n    const found = this._findPreviousAndSelect(term, searchOptions);\n    this._fireResults(searchOptions);\n    this._cachedSearchTerm = term;\n\n    return found;\n  }\n\n  private _didOptionsChange(lastSearchOptions: ISearchOptions, searchOptions?: ISearchOptions): boolean {\n    if (!searchOptions) {\n      return false;\n    }\n    if (lastSearchOptions.caseSensitive !== searchOptions.caseSensitive) {\n      return true;\n    }\n    if (lastSearchOptions.regex !== searchOptions.regex) {\n      return true;\n    }\n    if (lastSearchOptions.wholeWord !== searchOptions.wholeWord) {\n      return true;\n    }\n    return false;\n  }\n\n  private _fireResults(searchOptions?: ISearchOptions): void {\n    if (searchOptions?.decorations) {\n      let resultIndex = -1;\n      if (this._selectedDecoration.value) {\n        const selectedMatch = this._selectedDecoration.value.match;\n        for (let i = 0; i < this._highlightDecorations.length; i++) {\n          const match = this._highlightDecorations[i].match;\n          if (match.row === selectedMatch.row && match.col === selectedMatch.col && match.size === selectedMatch.size) {\n            resultIndex = i;\n            break;\n          }\n        }\n      }\n      this._onDidChangeResults.fire({ resultIndex, resultCount: this._highlightDecorations.length });\n    }\n  }\n\n  private _findPreviousAndSelect(term: string, searchOptions?: ISearchOptions): boolean {\n    if (!this._terminal) {\n      throw new Error('Cannot use addon until it has been loaded');\n    }\n    if (!this._terminal || !term || term.length === 0) {\n      this._terminal?.clearSelection();\n      this.clearDecorations();\n      return false;\n    }\n\n    const prevSelectedPos = this._terminal.getSelectionPosition();\n    this._terminal.clearSelection();\n\n    let startRow = this._terminal.buffer.active.baseY + this._terminal.rows - 1;\n    let startCol = this._terminal.cols;\n    const isReverseSearch = true;\n\n    this._initLinesCache();\n    const searchPosition: ISearchPosition = {\n      startRow,\n      startCol\n    };\n\n    let result: ISearchResult | undefined;\n    if (prevSelectedPos) {\n      searchPosition.startRow = startRow = prevSelectedPos.start.y;\n      searchPosition.startCol = startCol = prevSelectedPos.start.x;\n      if (this._cachedSearchTerm !== term) {\n        // Try to expand selection to right first.\n        result = this._findInLine(term, searchPosition, searchOptions, false);\n        if (!result) {\n          // If selection was not able to be expanded to the right, then try reverse search\n          searchPosition.startRow = startRow = prevSelectedPos.end.y;\n          searchPosition.startCol = startCol = prevSelectedPos.end.x;\n        }\n      }\n    }\n\n    if (!result) {\n      result = this._findInLine(term, searchPosition, searchOptions, isReverseSearch);\n    }\n\n    // Search from startRow - 1 to top\n    if (!result) {\n      searchPosition.startCol = Math.max(searchPosition.startCol, this._terminal.cols);\n      for (let y = startRow - 1; y >= 0; y--) {\n        searchPosition.startRow = y;\n        result = this._findInLine(term, searchPosition, searchOptions, isReverseSearch);\n        if (result) {\n          break;\n        }\n      }\n    }\n    // If we hit the top and didn't search from the very bottom wrap back down\n    if (!result && startRow !== (this._terminal.buffer.active.baseY + this._terminal.rows - 1)) {\n      for (let y = (this._terminal.buffer.active.baseY + this._terminal.rows - 1); y >= startRow; y--) {\n        searchPosition.startRow = y;\n        result = this._findInLine(term, searchPosition, searchOptions, isReverseSearch);\n        if (result) {\n          break;\n        }\n      }\n    }\n\n    // Set selection and scroll if a result was found\n    return this._selectResult(result, searchOptions?.decorations, searchOptions?.noScroll);\n  }\n\n  /**\n   * Sets up a line cache with a ttl\n   */\n  private _initLinesCache(): void {\n    const terminal = this._terminal!;\n    if (!this._linesCache) {\n      this._linesCache = new Array(terminal.buffer.active.length);\n      this._linesCacheDisposables.value = combinedDisposable(\n        terminal.onLineFeed(() => this._destroyLinesCache()),\n        terminal.onCursorMove(() => this._destroyLinesCache()),\n        terminal.onResize(() => this._destroyLinesCache())\n      );\n    }\n\n    window.clearTimeout(this._linesCacheTimeoutId);\n    this._linesCacheTimeoutId = window.setTimeout(() => this._destroyLinesCache(), LINES_CACHE_TIME_TO_LIVE);\n  }\n\n  private _destroyLinesCache(): void {\n    this._linesCache = undefined;\n    this._linesCacheDisposables.clear();\n    if (this._linesCacheTimeoutId) {\n      window.clearTimeout(this._linesCacheTimeoutId);\n      this._linesCacheTimeoutId = 0;\n    }\n  }\n\n  /**\n   * A found substring is a whole word if it doesn't have an alphanumeric character directly\n   * adjacent to it.\n   * @param searchIndex starting indext of the potential whole word substring\n   * @param line entire string in which the potential whole word was found\n   * @param term the substring that starts at searchIndex\n   */\n  private _isWholeWord(searchIndex: number, line: string, term: string): boolean {\n    return ((searchIndex === 0) || (NON_WORD_CHARACTERS.includes(line[searchIndex - 1]))) &&\n      (((searchIndex + term.length) === line.length) || (NON_WORD_CHARACTERS.includes(line[searchIndex + term.length])));\n  }\n\n  /**\n   * Searches a line for a search term. Takes the provided terminal line and searches the text line,\n   * which may contain subsequent terminal lines if the text is wrapped. If the provided line number\n   * is part of a wrapped text line that started on an earlier line then it is skipped since it will\n   * be properly searched when the terminal line that the text starts on is searched.\n   * @param term The search term.\n   * @param searchPosition The position to start the search.\n   * @param searchOptions Search options.\n   * @param isReverseSearch Whether the search should start from the right side of the terminal and\n   * search to the left.\n   * @returns The search result if it was found.\n   */\n  protected _findInLine(term: string, searchPosition: ISearchPosition, searchOptions: ISearchOptions = {}, isReverseSearch: boolean = false): ISearchResult | undefined {\n    const terminal = this._terminal!;\n    const row = searchPosition.startRow;\n    const col = searchPosition.startCol;\n\n    // Ignore wrapped lines, only consider on unwrapped line (first row of command string).\n    const firstLine = terminal.buffer.active.getLine(row);\n    if (firstLine?.isWrapped) {\n      if (isReverseSearch) {\n        searchPosition.startCol += terminal.cols;\n        return;\n      }\n\n      // This will iterate until we find the line start.\n      // When we find it, we will search using the calculated start column.\n      searchPosition.startRow--;\n      searchPosition.startCol += terminal.cols;\n      return this._findInLine(term, searchPosition, searchOptions);\n    }\n    let cache = this._linesCache?.[row];\n    if (!cache) {\n      cache = this._translateBufferLineToStringWithWrap(row, true);\n      if (this._linesCache) {\n        this._linesCache[row] = cache;\n      }\n    }\n    const [stringLine, offsets] = cache;\n\n    const offset = this._bufferColsToStringOffset(row, col);\n    let searchTerm = term;\n    let searchStringLine = stringLine;\n    if (!searchOptions.regex) {\n      searchTerm = searchOptions.caseSensitive ? term : term.toLowerCase();\n      searchStringLine = searchOptions.caseSensitive ? stringLine : stringLine.toLowerCase();\n    }\n\n    let resultIndex = -1;\n    if (searchOptions.regex) {\n      const searchRegex = RegExp(searchTerm, searchOptions.caseSensitive ? 'g' : 'gi');\n      let foundTerm: RegExpExecArray | null;\n      if (isReverseSearch) {\n        // This loop will get the resultIndex of the _last_ regex match in the range 0..offset\n        while (foundTerm = searchRegex.exec(searchStringLine.slice(0, offset))) {\n          resultIndex = searchRegex.lastIndex - foundTerm[0].length;\n          term = foundTerm[0];\n          searchRegex.lastIndex -= (term.length - 1);\n        }\n      } else {\n        foundTerm = searchRegex.exec(searchStringLine.slice(offset));\n        if (foundTerm && foundTerm[0].length > 0) {\n          resultIndex = offset + (searchRegex.lastIndex - foundTerm[0].length);\n          term = foundTerm[0];\n        }\n      }\n    } else {\n      if (isReverseSearch) {\n        if (offset - searchTerm.length >= 0) {\n          resultIndex = searchStringLine.lastIndexOf(searchTerm, offset - searchTerm.length);\n        }\n      } else {\n        resultIndex = searchStringLine.indexOf(searchTerm, offset);\n      }\n    }\n\n    if (resultIndex >= 0) {\n      if (searchOptions.wholeWord && !this._isWholeWord(resultIndex, searchStringLine, term)) {\n        return;\n      }\n\n      // Adjust the row number and search index if needed since a \"line\" of text can span multiple\n      // rows\n      let startRowOffset = 0;\n      while (startRowOffset < offsets.length - 1 && resultIndex >= offsets[startRowOffset + 1]) {\n        startRowOffset++;\n      }\n      let endRowOffset = startRowOffset;\n      while (endRowOffset < offsets.length - 1 && resultIndex + term.length >= offsets[endRowOffset + 1]) {\n        endRowOffset++;\n      }\n      const startColOffset = resultIndex - offsets[startRowOffset];\n      const endColOffset = resultIndex + term.length - offsets[endRowOffset];\n      const startColIndex = this._stringLengthToBufferSize(row + startRowOffset, startColOffset);\n      const endColIndex = this._stringLengthToBufferSize(row + endRowOffset, endColOffset);\n      const size = endColIndex - startColIndex + terminal.cols * (endRowOffset - startRowOffset);\n\n      return {\n        term,\n        col: startColIndex,\n        row: row + startRowOffset,\n        size\n      };\n    }\n  }\n\n  private _stringLengthToBufferSize(row: number, offset: number): number {\n    const line = this._terminal!.buffer.active.getLine(row);\n    if (!line) {\n      return 0;\n    }\n    for (let i = 0; i < offset; i++) {\n      const cell = line.getCell(i);\n      if (!cell) {\n        break;\n      }\n      // Adjust the searchIndex to normalize emoji into single chars\n      const char = cell.getChars();\n      if (char.length > 1) {\n        offset -= char.length - 1;\n      }\n      // Adjust the searchIndex for empty characters following wide unicode\n      // chars (eg. CJK)\n      const nextCell = line.getCell(i + 1);\n      if (nextCell && nextCell.getWidth() === 0) {\n        offset++;\n      }\n    }\n    return offset;\n  }\n\n  private _bufferColsToStringOffset(startRow: number, cols: number): number {\n    const terminal = this._terminal!;\n    let lineIndex = startRow;\n    let offset = 0;\n    let line = terminal.buffer.active.getLine(lineIndex);\n    while (cols > 0 && line) {\n      for (let i = 0; i < cols && i < terminal.cols; i++) {\n        const cell = line.getCell(i);\n        if (!cell) {\n          break;\n        }\n        if (cell.getWidth()) {\n          // Treat null characters as whitespace to align with the translateToString API\n          offset += cell.getCode() === 0 ? 1 : cell.getChars().length;\n        }\n      }\n      lineIndex++;\n      line = terminal.buffer.active.getLine(lineIndex);\n      if (line && !line.isWrapped) {\n        break;\n      }\n      cols -= terminal.cols;\n    }\n    return offset;\n  }\n\n  /**\n   * Translates a buffer line to a string, including subsequent lines if they are wraps.\n   * Wide characters will count as two columns in the resulting string. This\n   * function is useful for getting the actual text underneath the raw selection\n   * position.\n   * @param lineIndex The index of the line being translated.\n   * @param trimRight Whether to trim whitespace to the right.\n   */\n  private _translateBufferLineToStringWithWrap(lineIndex: number, trimRight: boolean): LineCacheEntry {\n    const terminal = this._terminal!;\n    const strings = [];\n    const lineOffsets = [0];\n    let line = terminal.buffer.active.getLine(lineIndex);\n    while (line) {\n      const nextLine = terminal.buffer.active.getLine(lineIndex + 1);\n      const lineWrapsToNext = nextLine ? nextLine.isWrapped : false;\n      let string = line.translateToString(!lineWrapsToNext && trimRight);\n      if (lineWrapsToNext && nextLine) {\n        const lastCell = line.getCell(line.length - 1);\n        const lastCellIsNull = lastCell && lastCell.getCode() === 0 && lastCell.getWidth() === 1;\n        // a wide character wrapped to the next line\n        if (lastCellIsNull && nextLine.getCell(0)?.getWidth() === 2) {\n          string = string.slice(0, -1);\n        }\n      }\n      strings.push(string);\n      if (lineWrapsToNext) {\n        lineOffsets.push(lineOffsets[lineOffsets.length - 1] + string.length);\n      } else {\n        break;\n      }\n      lineIndex++;\n      line = nextLine;\n    }\n    return [strings.join(''), lineOffsets];\n  }\n\n  /**\n   * Selects and scrolls to a result.\n   * @param result The result to select.\n   * @returns Whether a result was selected.\n   */\n  private _selectResult(result: ISearchResult | undefined, options?: ISearchDecorationOptions, noScroll?: boolean): boolean {\n    const terminal = this._terminal!;\n    this._selectedDecoration.clear();\n    if (!result) {\n      terminal.clearSelection();\n      return false;\n    }\n    terminal.select(result.col, result.row, result.size);\n    if (options) {\n      const decorations = this._createResultDecorations(result, options, true);\n      if (decorations) {\n        this._selectedDecoration.value = { decorations, match: result, dispose() { dispose(decorations); } };\n      }\n    }\n\n    if (!noScroll) {\n      // If it is not in the viewport then we scroll else it just gets selected\n      if (result.row >= (terminal.buffer.active.viewportY + terminal.rows) || result.row < terminal.buffer.active.viewportY) {\n        let scroll = result.row - terminal.buffer.active.viewportY;\n        scroll -= Math.floor(terminal.rows / 2);\n        terminal.scrollLines(scroll);\n      }\n    }\n    return true;\n  }\n\n  /**\n   * Applies styles to the decoration when it is rendered.\n   * @param element The decoration's element.\n   * @param borderColor The border color to apply.\n   * @param isActiveResult Whether the element is part of the active search result.\n   * @returns\n   */\n  private _applyStyles(element: HTMLElement, borderColor: string | undefined, isActiveResult: boolean): void {\n    if (!element.classList.contains('xterm-find-result-decoration')) {\n      element.classList.add('xterm-find-result-decoration');\n      if (borderColor) {\n        element.style.outline = `1px solid ${borderColor}`;\n      }\n    }\n    if (isActiveResult) {\n      element.classList.add('xterm-find-active-result-decoration');\n    }\n  }\n\n  /**\n   * Creates a decoration for the result and applies styles\n   * @param result the search result for which to create the decoration\n   * @param options the options for the decoration\n   * @returns the {@link IDecoration} or undefined if the marker has already been disposed of\n   */\n  private _createResultDecorations(result: ISearchResult, options: ISearchDecorationOptions, isActiveResult: boolean): IDecoration[] | undefined {\n    const terminal = this._terminal!;\n\n    // Gather decoration ranges for this match as it could wrap\n    const decorationRanges: [number, number, number][] = [];\n    let currentCol = result.col;\n    let remainingSize = result.size;\n    let markerOffset = -terminal.buffer.active.baseY - terminal.buffer.active.cursorY + result.row;\n    while (remainingSize > 0) {\n      const amountThisRow = Math.min(terminal.cols - currentCol, remainingSize);\n      decorationRanges.push([markerOffset, currentCol, amountThisRow]);\n      currentCol = 0;\n      remainingSize -= amountThisRow;\n      markerOffset++;\n    }\n\n    // Create the decorations\n    const decorations: IDecoration[] = [];\n    for (const range of decorationRanges) {\n      const marker = terminal.registerMarker(range[0]);\n      const decoration = terminal.registerDecoration({\n        marker,\n        x: range[1],\n        width: range[2],\n        backgroundColor: isActiveResult ? options.activeMatchBackground : options.matchBackground,\n        overviewRulerOptions: this._highlightedLines.has(marker.line) ? undefined : {\n          color: isActiveResult ? options.activeMatchColorOverviewRuler : options.matchOverviewRuler,\n          position: 'center'\n        }\n      });\n      if (decoration) {\n        const disposables: IDisposable[] = [];\n        disposables.push(marker);\n        disposables.push(decoration.onRender((e) => this._applyStyles(e, isActiveResult ? options.activeMatchBorder : options.matchBorder, false)));\n        disposables.push(decoration.onDispose(() => dispose(disposables)));\n        decorations.push(decoration);\n      }\n    }\n\n    return decorations.length === 0 ? undefined : decorations;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAcO,IAAMA,GAAN,KAAmB,CAIzB,aAAc,CAEb,KAAK,UAAY,CAAC,EAElB,KAAK,uBAAyB,SAAU,EAAQ,CAC/C,WAAW,IAAM,CAChB,MAAI,EAAE,MACDC,EAAiB,mBAAmB,CAAC,EAClC,IAAIA,EAAiB,EAAE,QAAU;AAAA;AAAA,EAAS,EAAE,KAAK,EAGlD,IAAI,MAAM,EAAE,QAAU;AAAA;AAAA,EAAS,EAAE,KAAK,EAGvC,CACP,EAAG,CAAC,CACL,CACD,CAEA,YAAYC,EAAsD,CACjE,YAAK,UAAU,KAAKA,CAAQ,EAErB,IAAM,CACZ,KAAK,gBAAgBA,CAAQ,CAC9B,CACD,CAEQ,KAAK,EAAc,CAC1B,KAAK,UAAU,QAASA,GAAa,CACpCA,EAAS,CAAC,CACX,CAAC,CACF,CAEQ,gBAAgBA,EAAuC,CAC9D,KAAK,UAAU,OAAO,KAAK,UAAU,QAAQA,CAAQ,EAAG,CAAC,CAC1D,CAEA,0BAA0BC,EAAmD,CAC5E,KAAK,uBAAyBA,CAC/B,CAEA,2BAA8C,CAC7C,OAAO,KAAK,sBACb,CAEA,kBAAkB,EAAc,CAC/B,KAAK,uBAAuB,CAAC,EAC7B,KAAK,KAAK,CAAC,CACZ,CAGA,0BAA0B,EAAc,CACvC,KAAK,uBAAuB,CAAC,CAC9B,CACD,EAEaC,GAAe,IAAIJ,GAsBzB,SAASK,GAAkBC,EAAmB,CAE/CC,GAAoBD,CAAC,GACzBE,GAAa,kBAAkBF,CAAC,CAGlC,CAoEA,IAAMG,GAAe,WAKd,SAASC,GAAoBC,EAAqB,CACxD,OAAIA,aAAiBC,EACb,GAEDD,aAAiB,OAASA,EAAM,OAASF,IAAgBE,EAAM,UAAYF,EACnF,CAIO,IAAMG,EAAN,cAAgC,KAAM,CAC5C,aAAc,CACb,MAAMH,EAAY,EAClB,KAAK,KAAO,KAAK,OAClB,CACD,EA0EO,IAAMI,EAAN,MAAMC,UAAyB,KAAM,CAG3C,YAAYC,EAAc,CACzB,MAAMA,CAAG,EACT,KAAK,KAAO,mBACb,CAEA,OAAc,UAAUC,EAA8B,CACrD,GAAIA,aAAeF,EAClB,OAAOE,EAGR,IAAMC,EAAS,IAAIH,EACnB,OAAAG,EAAO,QAAUD,EAAI,QACrBC,EAAO,MAAQD,EAAI,MACZC,CACR,CAEA,OAAc,mBAAmBD,EAAqC,CACrE,OAAOA,EAAI,OAAS,mBACrB,CACD,ECrRO,SAASE,GAA4DC,EAAOC,EAAkC,CACpH,IAAMC,EAAQ,KACVC,EAAU,GACVC,EAEJ,OAAO,UAAY,CAClB,GAAID,EACH,OAAOC,EAIR,GADAD,EAAU,GACNF,EACH,GAAI,CACHG,EAASJ,EAAG,MAAME,EAAO,SAAS,CACnC,QAAE,CACDD,EAAiB,CAClB,MAEAG,EAASJ,EAAG,MAAME,EAAO,SAAS,EAGnC,OAAOE,CACR,CACD,CCaO,SAASC,GAAyBC,EAAqBC,EAAiCC,EAAW,EAAGC,EAAWH,EAAM,OAAgB,CAC7I,IAAI,EAAIE,EACJE,EAAID,EACR,KAAO,EAAIC,GAAG,CACb,IAAMC,EAAI,KAAK,OAAO,EAAID,GAAK,CAAC,EAC5BH,EAAUD,EAAMK,CAAC,CAAC,EACrB,EAAIA,EAAI,EAERD,EAAIC,CAEN,CACA,OAAO,EAAI,CACZ,CA4CO,IAAMC,GAAN,MAAMA,EAAmB,CAM/B,YAA6BC,EAAsB,CAAtB,YAAAA,EAH7B,KAAQ,2BAA6B,CAIrC,CAMA,mBAAmBC,EAAgD,CAClE,GAAIF,GAAgB,iBAAkB,CACrC,GAAI,KAAK,wBACR,QAAWG,KAAQ,KAAK,OACvB,GAAI,KAAK,uBAAuBA,CAAI,GAAK,CAACD,EAAUC,CAAI,EACvD,MAAM,IAAI,MAAM,8FAA8F,EAIjH,KAAK,uBAAyBD,CAC/B,CAEA,IAAME,EAAMC,GAAsB,KAAK,OAAQH,EAAW,KAAK,0BAA0B,EACzF,YAAK,2BAA6BE,EAAM,EACjCA,IAAQ,GAAK,OAAY,KAAK,OAAOA,CAAG,CAChD,CACD,EA7BaJ,GACE,iBAAmB,GAD3B,IAAMM,GAANN,GC8hBA,IAAUO,OAAV,CACC,SAASC,EAAWC,EAAgC,CAC1D,OAAOA,EAAS,CACjB,CAFOF,EAAS,WAAAC,EAIT,SAASE,EAAkBD,EAAgC,CACjE,OAAOA,GAAU,CAClB,CAFOF,EAAS,kBAAAG,EAIT,SAASC,EAAcF,EAAgC,CAC7D,OAAOA,EAAS,CACjB,CAFOF,EAAS,cAAAI,EAIT,SAASC,EAA2BH,EAAgC,CAC1E,OAAOA,IAAW,CACnB,CAFOF,EAAS,2BAAAK,EAIHL,EAAA,YAAc,EACdA,EAAA,SAAW,GACXA,EAAA,yBAA2B,IAnBxBA,KAAA,IA6BV,SAASM,GAA6BC,EAAuCC,EAAuD,CAC1I,MAAO,CAACC,EAAGC,IAAMF,EAAWD,EAASE,CAAC,EAAGF,EAASG,CAAC,CAAC,CACrD,CAiBO,IAAMC,GAAuC,CAACC,EAAGC,IAAMD,EAAIC,EA4F3D,IAAMC,EAAN,MAAMA,CAAoB,CAGhC,YAKiBC,EACf,CADe,aAAAA,CAEjB,CAEA,QAAQC,EAA4B,CACnC,KAAK,QAAQC,IAAUD,EAAQC,CAAI,EAAU,GAAO,CACrD,CAEA,SAAe,CACd,IAAMC,EAAc,CAAC,EACrB,YAAK,QAAQD,IAAUC,EAAO,KAAKD,CAAI,EAAU,GAAO,EACjDC,CACR,CAEA,OAAOC,EAAsD,CAC5D,OAAO,IAAIL,EAAiBM,GAAM,KAAK,QAAQH,GAAQE,EAAUF,CAAI,EAAIG,EAAGH,CAAI,EAAI,EAAI,CAAC,CAC1F,CAEA,IAAaI,EAAwD,CACpE,OAAO,IAAIP,EAA0BM,GAAM,KAAK,QAAQH,GAAQG,EAAGC,EAAMJ,CAAI,CAAC,CAAC,CAAC,CACjF,CAEA,KAAKE,EAA0C,CAC9C,IAAID,EAAS,GACb,YAAK,QAAQD,IAAUC,EAASC,EAAUF,CAAI,EAAU,CAACC,EAAS,EAC3DA,CACR,CAEA,UAAUC,EAAgD,CACzD,IAAID,EACJ,YAAK,QAAQD,GACRE,EAAUF,CAAI,GACjBC,EAASD,EACF,IAED,EACP,EACMC,CACR,CAEA,SAASC,EAAgD,CACxD,IAAID,EACJ,YAAK,QAAQD,IACRE,EAAUF,CAAI,IACjBC,EAASD,GAEH,GACP,EACMC,CACR,CAEA,cAAcI,EAA0C,CACvD,IAAIJ,EACAK,EAAQ,GACZ,YAAK,QAAQN,KACRM,GAASC,GAAc,cAAcF,EAAWL,EAAMC,CAAO,CAAC,KACjEK,EAAQ,GACRL,EAASD,GAEH,GACP,EACMC,CACR,CACD,EAvEaJ,EACW,MAAQ,IAAIA,EAAwBW,GAAa,CAAE,CAAC,EADrE,IAAMC,GAANZ,ECzvBA,SAASa,GAA+CC,EAAWC,EAA4C,CACrH,IAAMC,EAAyB,OAAO,OAAO,IAAI,EACjD,QAAWC,KAAWH,EAAM,CAC3B,IAAMI,EAAMH,EAAQE,CAAO,EACvBE,EAASH,EAAOE,CAAG,EAClBC,IACJA,EAASH,EAAOE,CAAG,EAAI,CAAC,GAEzBC,EAAO,KAAKF,CAAO,CACpB,CACA,OAAOD,CACR,CAhCA,IAAAI,GAAAC,GAmFaC,GAAN,KAAsC,CAG5C,YAAYC,EAAqBC,EAAsB,CAAtB,WAAAA,EAFjC,KAAQ,KAAO,IAAI,IAsDnB,KAACJ,IAA8B,aAnD9B,QAAWK,KAASF,EACnB,KAAK,IAAIE,CAAK,CAEhB,CAEA,IAAI,MAAe,CAClB,OAAO,KAAK,KAAK,IAClB,CAEA,IAAIA,EAAgB,CACnB,IAAMC,EAAM,KAAK,MAAMD,CAAK,EAC5B,YAAK,KAAK,IAAIC,EAAKD,CAAK,EACjB,IACR,CAEA,OAAOA,EAAmB,CACzB,OAAO,KAAK,KAAK,OAAO,KAAK,MAAMA,CAAK,CAAC,CAC1C,CAEA,IAAIA,EAAmB,CACtB,OAAO,KAAK,KAAK,IAAI,KAAK,MAAMA,CAAK,CAAC,CACvC,CAEA,CAAC,SAAoC,CACpC,QAAWE,KAAS,KAAK,KAAK,OAAO,EACpC,KAAM,CAACA,EAAOA,CAAK,CAErB,CAEA,MAA4B,CAC3B,OAAO,KAAK,OAAO,CACpB,CAEA,CAAC,QAA8B,CAC9B,QAAWA,KAAS,KAAK,KAAK,OAAO,EACpC,MAAMA,CAER,CAEA,OAAc,CACb,KAAK,KAAK,MAAM,CACjB,CAEA,QAAQC,EAAwDC,EAAqB,CACpF,KAAK,KAAK,QAAQF,GAASC,EAAW,KAAKC,EAASF,EAAOA,EAAO,IAAI,CAAC,CACxE,CAEA,EAACN,GAAA,OAAO,SAIPD,GAAA,OAAO,YAJPC,GAAe,GAAyB,CACxC,OAAO,KAAK,OAAO,CACpB,CAGD,ECRO,IAAMS,GAAN,KAAmB,CAAnB,cAEN,KAAQ,IAAM,IAAI,IAElB,IAAIC,EAAQC,EAAgB,CAC3B,IAAIC,EAAS,KAAK,IAAI,IAAIF,CAAG,EAExBE,IACJA,EAAS,IAAI,IACb,KAAK,IAAI,IAAIF,EAAKE,CAAM,GAGzBA,EAAO,IAAID,CAAK,CACjB,CAEA,OAAOD,EAAQC,EAAgB,CAC9B,IAAMC,EAAS,KAAK,IAAI,IAAIF,CAAG,EAE1BE,IAILA,EAAO,OAAOD,CAAK,EAEfC,EAAO,OAAS,GACnB,KAAK,IAAI,OAAOF,CAAG,EAErB,CAEA,QAAQA,EAAQG,EAA8B,CAC7C,IAAMD,EAAS,KAAK,IAAI,IAAIF,CAAG,EAE1BE,GAILA,EAAO,QAAQC,CAAE,CAClB,CAEA,IAAIH,EAAwB,CAC3B,IAAME,EAAS,KAAK,IAAI,IAAIF,CAAG,EAC/B,OAAKE,GACG,IAAI,GAGb,CACD,EC5KO,IAAUE,OAAV,CAEC,SAASC,EAAYC,EAAkC,CAC7D,OAAOA,GAAS,OAAOA,GAAU,UAAY,OAAOA,EAAM,OAAO,QAAQ,GAAM,UAChF,CAFOF,EAAS,GAAAC,EAIhB,IAAME,EAAwB,OAAO,OAAO,CAAC,CAAC,EACvC,SAASC,GAA8B,CAC7C,OAAOD,CACR,CAFOH,EAAS,MAAAI,EAIT,SAAUC,EAAUC,EAAyB,CACnD,MAAMA,CACP,CAFON,EAAU,OAAAK,EAIV,SAASE,EAAQC,EAAiD,CACxE,OAAIP,EAAGO,CAAiB,EAChBA,EAEAH,EAAOG,CAAiB,CAEjC,CANOR,EAAS,KAAAO,EAQT,SAASE,EAAQC,EAAuD,CAC9E,OAAOA,GAAYP,CACpB,CAFOH,EAAS,KAAAS,EAIT,SAAUE,EAAWC,EAA8B,CACzD,QAASC,EAAID,EAAM,OAAS,EAAGC,GAAK,EAAGA,IACtC,MAAMD,EAAMC,CAAC,CAEf,CAJOb,EAAU,QAAAW,EAMV,SAASG,EAAWJ,EAAmD,CAC7E,MAAO,CAACA,GAAYA,EAAS,OAAO,QAAQ,EAAE,EAAE,KAAK,EAAE,OAAS,EACjE,CAFOV,EAAS,QAAAc,EAIT,SAASC,EAASL,EAAsC,CAC9D,OAAOA,EAAS,OAAO,QAAQ,EAAE,EAAE,KAAK,EAAE,KAC3C,CAFOV,EAAS,MAAAe,EAIT,SAASC,EAAQN,EAAuBO,EAAkD,CAChG,IAAIJ,EAAI,EACR,QAAWP,KAAWI,EACrB,GAAIO,EAAUX,EAASO,GAAG,EACzB,MAAO,GAGT,MAAO,EACR,CAROb,EAAS,KAAAgB,EAYT,SAASE,EAAQR,EAAuBO,EAA6C,CAC3F,QAAWX,KAAWI,EACrB,GAAIO,EAAUX,CAAO,EACpB,OAAOA,CAKV,CARON,EAAS,KAAAkB,EAYT,SAAUC,EAAUT,EAAuBO,EAA2C,CAC5F,QAAWX,KAAWI,EACjBO,EAAUX,CAAO,IACpB,MAAMA,EAGT,CANON,EAAU,OAAAmB,EAQV,SAAUC,EAAUV,EAAuBW,EAA6C,CAC9F,IAAIC,EAAQ,EACZ,QAAWhB,KAAWI,EACrB,MAAMW,EAAGf,EAASgB,GAAO,CAE3B,CALOtB,EAAU,IAAAoB,EAOV,SAAUG,EAAcb,EAAuBW,EAAuD,CAC5G,IAAIC,EAAQ,EACZ,QAAWhB,KAAWI,EACrB,MAAOW,EAAGf,EAASgB,GAAO,CAE5B,CALOtB,EAAU,QAAAuB,EAOV,SAAUC,KAAaC,EAAuC,CACpE,QAAWf,KAAYe,EACtB,MAAOf,CAET,CAJOV,EAAU,OAAAwB,EAMV,SAASE,EAAahB,EAAuBiB,EAAmDC,EAAoB,CAC1H,IAAIC,EAAQD,EACZ,QAAWtB,KAAWI,EACrBmB,EAAQF,EAAQE,EAAOvB,CAAO,EAE/B,OAAOuB,CACR,CANO7B,EAAS,OAAA0B,EAWT,SAAUI,EAASC,EAAuBtB,EAAcuB,EAAKD,EAAI,OAAqB,CAW5F,IAVItB,EAAO,IACVA,GAAQsB,EAAI,QAGTC,EAAK,EACRA,GAAMD,EAAI,OACAC,EAAKD,EAAI,SACnBC,EAAKD,EAAI,QAGHtB,EAAOuB,EAAIvB,IACjB,MAAMsB,EAAItB,CAAI,CAEhB,CAdOT,EAAU,MAAA8B,EAoBV,SAASG,EAAWvB,EAAuBwB,EAAiB,OAAO,kBAAuC,CAChH,IAAMC,EAAgB,CAAC,EAEvB,GAAID,IAAW,EACd,MAAO,CAACC,EAAUzB,CAAQ,EAG3B,IAAM0B,EAAW1B,EAAS,OAAO,QAAQ,EAAE,EAE3C,QAASG,EAAI,EAAGA,EAAIqB,EAAQrB,IAAK,CAChC,IAAMwB,GAAOD,EAAS,KAAK,EAE3B,GAAIC,GAAK,KACR,MAAO,CAACF,EAAUnC,EAAS,MAAM,CAAC,EAGnCmC,EAAS,KAAKE,GAAK,KAAK,CACzB,CAEA,MAAO,CAACF,EAAU,CAAE,CAAC,OAAO,QAAQ,GAAI,CAAE,OAAOC,CAAU,CAAE,CAAC,CAC/D,CApBOpC,EAAS,QAAAiC,EAsBhB,eAAsBK,EAAgB5B,EAA0C,CAC/E,IAAM6B,EAAc,CAAC,EACrB,cAAiBC,KAAQ9B,EACxB6B,EAAO,KAAKC,CAAI,EAEjB,OAAO,QAAQ,QAAQD,CAAM,CAC9B,CANAvC,EAAsB,aAAAsC,IAlJNtC,KAAA,ICejB,IAAMyC,GAAoB,GACtBC,EAA+C,KAiCtCC,GAAN,MAAMA,EAAgD,CAAtD,cAGN,KAAiB,kBAAoB,IAAI,IAEjC,kBAAkBC,EAAgC,CACzD,IAAIC,EAAM,KAAK,kBAAkB,IAAID,CAAC,EACtC,OAAKC,IACJA,EAAM,CAAE,OAAQ,KAAM,OAAQ,KAAM,YAAa,GAAO,MAAOD,EAAG,IAAKD,GAAkB,KAAM,EAC/F,KAAK,kBAAkB,IAAIC,EAAGC,CAAG,GAE3BA,CACR,CAEA,gBAAgBD,EAAsB,CACrC,IAAME,EAAO,KAAK,kBAAkBF,CAAC,EAChCE,EAAK,SACTA,EAAK,OACJ,IAAI,MAAM,EAAE,MAEf,CAEA,UAAUC,EAAoBC,EAAkC,CAC/D,IAAMF,EAAO,KAAK,kBAAkBC,CAAK,EACzCD,EAAK,OAASE,CACf,CAEA,eAAeC,EAAsB,CACpC,KAAK,kBAAkB,OAAOA,CAAC,CAChC,CAEA,gBAAgBC,EAA+B,CAC9C,KAAK,kBAAkBA,CAAU,EAAE,YAAc,EAClD,CAEQ,cAAcJ,EAAsBK,EAA4D,CACvG,IAAMC,EAAaD,EAAM,IAAIL,CAAI,EACjC,GAAIM,EACH,OAAOA,EAGR,IAAMC,EAASP,EAAK,OAAS,KAAK,cAAc,KAAK,kBAAkBA,EAAK,MAAM,EAAGK,CAAK,EAAIL,EAC9F,OAAAK,EAAM,IAAIL,EAAMO,CAAM,EACfA,CACR,CAEA,uBAAuC,CACtC,IAAMC,EAAkB,IAAI,IAM5B,MAJgB,CAAC,GAAG,KAAK,kBAAkB,QAAQ,CAAC,EAClD,OAAO,CAAC,CAAC,CAAEC,CAAC,IAAMA,EAAE,SAAW,MAAQ,CAAC,KAAK,cAAcA,EAAGD,CAAe,EAAE,WAAW,EAC1F,QAAQ,CAAC,CAACE,CAAC,IAAMA,CAAC,CAGrB,CAEA,0BAA0BC,EAAc,GAAIC,EAA+F,CAC1I,IAAIC,EACJ,GAAID,EACHC,EAAuBD,MACjB,CACN,IAAMJ,EAAkB,IAAI,IAEtBM,EAAiB,CAAC,GAAG,KAAK,kBAAkB,OAAO,CAAC,EACxD,OAAQC,GAASA,EAAK,SAAW,MAAQ,CAAC,KAAK,cAAcA,EAAMP,CAAe,EAAE,WAAW,EAEjG,GAAIM,EAAe,SAAW,EAC7B,OAED,IAAME,EAAiB,IAAI,IAAIF,EAAe,IAAIG,GAAKA,EAAE,KAAK,CAAC,EAO/D,GAJAJ,EAAuBC,EAAe,OAAOI,GACrC,EAAEA,EAAE,QAAUF,EAAe,IAAIE,EAAE,MAAM,EAChD,EAEGL,EAAqB,SAAW,EACnC,MAAM,IAAI,MAAM,oCAAoC,CAEtD,CAEA,GAAI,CAACA,EACJ,OAGD,SAASM,EAAkBC,EAAmC,CAC7D,SAASC,EAAaC,EAAiBC,EAAoC,CAC1E,KAAOD,EAAM,OAAS,GAAKC,EAAc,KAAKC,GAAU,OAAOA,GAAW,SAAWA,IAAWF,EAAM,CAAC,EAAIA,EAAM,CAAC,EAAE,MAAME,CAAM,CAAC,GAChIF,EAAM,MAAM,CAEd,CAEA,IAAMG,EAAQL,EAAQ,OAAQ,MAAM;AAAA,CAAI,EAAE,IAAIM,GAAKA,EAAE,KAAK,EAAE,QAAQ,MAAO,EAAE,CAAC,EAAE,OAAOR,GAAKA,IAAM,EAAE,EACpG,OAAAG,EAAaI,EAAO,CAAC,QAAS,2BAA4B,4CAA4C,CAAC,EAChGA,EAAM,QAAQ,CACtB,CAEA,IAAME,EAAmB,IAAIC,GAC7B,QAAWR,KAAWP,EAAsB,CAC3C,IAAMgB,EAAiBV,EAAkBC,CAAO,EAChD,QAASU,EAAI,EAAGA,GAAKD,EAAe,OAAQC,IAC3CH,EAAiB,IAAIE,EAAe,MAAM,EAAGC,CAAC,EAAE,KAAK;AAAA,CAAI,EAAGV,CAAO,CAErE,CAGAP,EAAqB,KAAKkB,GAAUb,GAAKA,EAAE,IAAKc,EAAgB,CAAC,EAEjE,IAAIC,EAAU,GAEVH,EAAI,EACR,QAAWV,KAAWP,EAAqB,MAAM,EAAGF,CAAW,EAAG,CACjEmB,IACA,IAAMD,EAAiBV,EAAkBC,CAAO,EAC1Cc,EAA2B,CAAC,EAElC,QAASJ,EAAI,EAAGA,EAAID,EAAe,OAAQC,IAAK,CAC/C,IAAIK,EAAON,EAAeC,CAAC,EAE3BK,EAAO,gBADQR,EAAiB,IAAIE,EAAe,MAAM,EAAGC,EAAI,CAAC,EAAE,KAAK;AAAA,CAAI,CAAC,EAC/C,IAAI,IAAIjB,EAAqB,MAAM,cAAcsB,CAAI,GAEnF,IAAMC,EAAaT,EAAiB,IAAIE,EAAe,MAAM,EAAGC,CAAC,EAAE,KAAK;AAAA,CAAI,CAAC,EACvEO,EAAgBC,GAAQ,CAAC,GAAGF,CAAU,EAAE,IAAItC,GAAKqB,EAAkBrB,CAAC,EAAEgC,CAAC,CAAC,EAAGrB,GAAKA,CAAC,EACvF,OAAO4B,EAAcR,EAAeC,CAAC,CAAC,EACtC,OAAW,CAACS,EAAMC,CAAG,IAAK,OAAO,QAAQH,CAAa,EACrDH,EAAyB,QAAQ,wBAAwBM,EAAI,MAAM,8BAA8BD,CAAI,EAAE,EAGxGL,EAAyB,QAAQC,CAAI,CACtC,CAEAF,GAAW;AAAA;AAAA;AAAA,0CAAiDH,CAAC,IAAIjB,EAAqB,MAAM,KAAKO,EAAQ,MAAM,YAAY,IAAI;AAAA,EAA0Bc,EAAyB,KAAK;AAAA,CAAI,CAAC;AAAA;AAAA;AAAA,CAC7L,CAEA,OAAIrB,EAAqB,OAASF,IACjCsB,GAAW;AAAA;AAAA;AAAA,UAAiBpB,EAAqB,OAASF,CAAW;AAAA;AAAA,GAG/D,CAAE,MAAOE,EAAsB,QAASoB,CAAQ,CACxD,CACD,EA5IapC,GACG,IAAM,EADf,IAAM4C,GAAN5C,GA8IA,SAAS6C,GAAqBC,EAA0C,CAC9E/C,EAAoB+C,CACrB,CAEA,GAAIhD,GAAmB,CACtB,IAAMiD,EAA4B,4BAClCF,GAAqB,IAAI,KAAoC,CAC5D,gBAAgBvC,EAAsB,CACrC,IAAM0C,EAAQ,IAAI,MAAM,+BAA+B,EAAE,MACzD,WAAW,IAAM,CACV1C,EAAUyC,CAAyB,GACxC,QAAQ,IAAIC,CAAK,CAEnB,EAAG,GAAI,CACR,CAEA,UAAU5C,EAAoBC,EAAkC,CAC/D,GAAID,GAASA,IAAU6C,EAAW,KACjC,GAAI,CACF7C,EAAc2C,CAAyB,EAAI,EAC7C,MAAQ,CAER,CAEF,CAEA,eAAexC,EAA+B,CAC7C,GAAIA,GAAcA,IAAe0C,EAAW,KAC3C,GAAI,CACF1C,EAAmBwC,CAAyB,EAAI,EAClD,MAAQ,CAER,CAEF,CACA,gBAAgBxC,EAA+B,CAAE,CAClD,CAAC,CACF,CAEO,SAAS2C,GAAuC5C,EAAS,CAC/D,OAAAP,GAAmB,gBAAgBO,CAAC,EAC7BA,CACR,CAEO,SAAS6C,GAAe5C,EAA+B,CAC7DR,GAAmB,eAAeQ,CAAU,CAC7C,CAEA,SAAS6C,EAAsBhD,EAAoBC,EAAkC,CACpFN,GAAmB,UAAUK,EAAOC,CAAM,CAC3C,CAEA,SAASgD,GAAuBC,EAAyBjD,EAAkC,CAC1F,GAAKN,EAGL,QAAWK,KAASkD,EACnBvD,EAAkB,UAAUK,EAAOC,CAAM,CAE3C,CAwCO,SAASkD,EAA+BC,EAAuC,CACrF,GAAIC,GAAS,GAAGD,CAAG,EAAG,CACrB,IAAME,EAAgB,CAAC,EAEvB,QAAWC,KAAKH,EACf,GAAIG,EACH,GAAI,CACHA,EAAE,QAAQ,CACX,OAASC,EAAG,CACXF,EAAO,KAAKE,CAAC,CACd,CAIF,GAAIF,EAAO,SAAW,EACrB,MAAMA,EAAO,CAAC,EACR,GAAIA,EAAO,OAAS,EAC1B,MAAM,IAAI,eAAeA,EAAQ,6CAA6C,EAG/E,OAAO,MAAM,QAAQF,CAAG,EAAI,CAAC,EAAIA,CAClC,SAAWA,EACV,OAAAA,EAAI,QAAQ,EACLA,CAET,CAcO,SAASK,MAAsBC,EAAyC,CAC9E,IAAMC,EAASC,EAAa,IAAMC,EAAQH,CAAW,CAAC,EACtD,OAAAI,GAAuBJ,EAAaC,CAAM,EACnCA,CACR,CAOO,SAASC,EAAaG,EAA6B,CACzD,IAAMC,EAAOC,GAAgB,CAC5B,QAASC,GAAyB,IAAM,CACvCC,GAAeH,CAAI,EACnBD,EAAG,CACJ,CAAC,CACF,CAAC,EACD,OAAOC,CACR,CASO,IAAMI,GAAN,MAAMA,EAAuC,CAOnD,aAAc,CAHd,KAAiB,WAAa,IAAI,IAClC,KAAQ,YAAc,GAGrBH,GAAgB,IAAI,CACrB,CAOO,SAAgB,CAClB,KAAK,cAITE,GAAe,IAAI,EACnB,KAAK,YAAc,GACnB,KAAK,MAAM,EACZ,CAKA,IAAW,YAAsB,CAChC,OAAO,KAAK,WACb,CAKO,OAAc,CACpB,GAAI,KAAK,WAAW,OAAS,EAI7B,GAAI,CACHN,EAAQ,KAAK,UAAU,CACxB,QAAE,CACD,KAAK,WAAW,MAAM,CACvB,CACD,CAKO,IAA2BQ,EAAS,CAC1C,GAAI,CAACA,EACJ,OAAOA,EAER,GAAKA,IAAqC,KACzC,MAAM,IAAI,MAAM,yCAAyC,EAG1D,OAAAC,EAAsBD,EAAG,IAAI,EACzB,KAAK,YACHD,GAAgB,0BACpB,QAAQ,KAAK,IAAI,MAAM,qHAAqH,EAAE,KAAK,EAGpJ,KAAK,WAAW,IAAIC,CAAC,EAGfA,CACR,CAMO,OAA8BA,EAAY,CAChD,GAAKA,EAGL,IAAKA,IAAqC,KACzC,MAAM,IAAI,MAAM,wCAAwC,EAEzD,KAAK,WAAW,OAAOA,CAAC,EACxBA,EAAE,QAAQ,EACX,CAKO,cAAqCA,EAAY,CAClDA,GAGD,KAAK,WAAW,IAAIA,CAAC,IACxB,KAAK,WAAW,OAAOA,CAAC,EACxBC,EAAsBD,EAAG,IAAI,EAE/B,CACD,EAlGaD,GAEL,yBAA2B,GAF5B,IAAMG,EAANH,GAyGeI,EAAf,KAAiD,CAWvD,aAAc,CAFd,KAAmB,OAAS,IAAID,EAG/BN,GAAgB,IAAI,EACpBK,EAAsB,KAAK,OAAQ,IAAI,CACxC,CAEO,SAAgB,CACtBH,GAAe,IAAI,EAEnB,KAAK,OAAO,QAAQ,CACrB,CAKU,UAAiCE,EAAS,CACnD,GAAKA,IAAgC,KACpC,MAAM,IAAI,MAAM,yCAAyC,EAE1D,OAAO,KAAK,OAAO,IAAIA,CAAC,CACzB,CACD,EA/BsBG,EAOL,KAAO,OAAO,OAAoB,CAAE,SAAU,CAAE,CAAE,CAAC,EAgC7D,IAAMC,EAAN,KAAsE,CAI5E,aAAc,CAFd,KAAQ,YAAc,GAGrBR,GAAgB,IAAI,CACrB,CAEA,IAAI,OAAuB,CAC1B,OAAO,KAAK,YAAc,OAAY,KAAK,MAC5C,CAEA,IAAI,MAAMS,EAAsB,CAC3B,KAAK,aAAeA,IAAU,KAAK,SAIvC,KAAK,QAAQ,QAAQ,EACjBA,GACHJ,EAAsBI,EAAO,IAAI,EAElC,KAAK,OAASA,EACf,CAKA,OAAc,CACb,KAAK,MAAQ,MACd,CAEA,SAAgB,CACf,KAAK,YAAc,GACnBP,GAAe,IAAI,EACnB,KAAK,QAAQ,QAAQ,EACrB,KAAK,OAAS,MACf,CAMA,cAA8B,CAC7B,IAAMQ,EAAW,KAAK,OACtB,YAAK,OAAS,OACVA,GACHL,EAAsBK,EAAU,IAAI,EAE9BA,CACR,CACD,ECviBA,IAAMC,EAAN,MAAMA,CAAQ,CAQb,YAAYC,EAAY,CACvB,KAAK,QAAUA,EACf,KAAK,KAAOD,EAAK,UACjB,KAAK,KAAOA,EAAK,SAClB,CACD,EAbMA,EAEW,UAAY,IAAIA,EAAU,MAAS,EAFpD,IAAME,GAANF,ECGA,IAAMG,GAAqB,WAAW,aAAe,OAAO,WAAW,YAAY,KAAQ,WAE9EC,GAAN,MAAMC,CAAU,CAOtB,OAAc,OAAOC,EAAqC,CACzD,OAAO,IAAID,EAAUC,CAAc,CACpC,CAEA,YAAYA,EAA0B,CACrC,KAAK,KAAOH,IAAqBG,IAAmB,GAAQ,KAAK,IAAM,WAAW,YAAa,IAAI,KAAK,WAAW,WAAW,EAC9H,KAAK,WAAa,KAAK,KAAK,EAC5B,KAAK,UAAY,EAClB,CAEO,MAAa,CACnB,KAAK,UAAY,KAAK,KAAK,CAC5B,CAEO,OAAc,CACpB,KAAK,WAAa,KAAK,KAAK,EAC5B,KAAK,UAAY,EAClB,CAEO,SAAkB,CACxB,OAAI,KAAK,YAAc,GACf,KAAK,UAAY,KAAK,WAEvB,KAAK,KAAK,EAAI,KAAK,UAC3B,CACD,ECxBA,IAAMC,GAA6B,GAO7BC,GAAoC,GASpCC,GAAsC,GAW3BC,OAAV,CACOA,EAAA,KAAmB,IAAMC,EAAW,KAEjD,SAASC,EAAsBC,EAAyB,CACvD,GAAIJ,GAAqC,CACxC,GAAM,CAAE,iBAAkBK,CAAmB,EAAID,EAC3CE,EAAQC,EAAW,OAAO,EAC5BC,EAAQ,EACZJ,EAAQ,iBAAmB,IAAM,CAC5B,EAAEI,IAAU,IACf,QAAQ,KAAK,4GAA4G,EACzHF,EAAM,MAAM,GAEbD,IAAqB,CACtB,CACD,CACD,CAkBO,SAASI,EAAMC,EAAuBC,EAA2C,CACvF,OAAOC,EAAwBF,EAAO,IAAG,GAAW,EAAG,OAAW,GAAM,OAAWC,CAAU,CAC9F,CAFOV,EAAS,MAAAQ,EAST,SAASI,EAAQH,EAA2B,CAClD,MAAO,CAACI,EAAUC,EAAW,KAAMC,IAAiB,CAEnD,IAAIC,EAAU,GACVC,EACJ,OAAAA,EAASR,EAAMS,GAAK,CACnB,GAAI,CAAAF,EAEG,OAAIC,EACVA,EAAO,QAAQ,EAEfD,EAAU,GAGJH,EAAS,KAAKC,EAAUI,CAAC,CACjC,EAAG,KAAMH,CAAW,EAEhBC,GACHC,EAAO,QAAQ,EAGTA,CACR,CACD,CAvBOjB,EAAS,KAAAY,EAqCT,SAASO,EAAUV,EAAiBU,EAAkBT,EAAwC,CACpG,OAAOU,EAAS,CAACP,EAAUC,EAAW,KAAMC,IAAiBN,EAAMY,GAAKR,EAAS,KAAKC,EAAUK,EAAIE,CAAC,CAAC,EAAG,KAAMN,CAAW,EAAGL,CAAU,CACxI,CAFOV,EAAS,IAAAmB,EAeT,SAASG,EAAWb,EAAiBc,EAAsBb,EAAwC,CACzG,OAAOU,EAAS,CAACP,EAAUC,EAAW,KAAMC,IAAiBN,EAAMY,GAAK,CAAEE,EAAKF,CAAC,EAAGR,EAAS,KAAKC,EAAUO,CAAC,CAAG,EAAG,KAAMN,CAAW,EAAGL,CAAU,CACjJ,CAFOV,EAAS,QAAAsB,EAmBT,SAASE,EAAUf,EAAiBe,EAA2Bd,EAAwC,CAC7G,OAAOU,EAAS,CAACP,EAAUC,EAAW,KAAMC,IAAiBN,EAAMS,GAAKM,EAAON,CAAC,GAAKL,EAAS,KAAKC,EAAUI,CAAC,EAAG,KAAMH,CAAW,EAAGL,CAAU,CAChJ,CAFOV,EAAS,OAAAwB,EAOT,SAASC,EAAUhB,EAA8B,CACvD,OAAOA,CACR,CAFOT,EAAS,OAAAyB,EAST,SAASC,KAAUC,EAA8B,CACvD,MAAO,CAACd,EAAUC,EAAW,KAAMC,IAAiB,CACnD,IAAML,EAAakB,GAAmB,GAAGD,EAAO,IAAIlB,GAASA,EAAMS,GAAKL,EAAS,KAAKC,EAAUI,CAAC,CAAC,CAAC,CAAC,EACpG,OAAOW,EAAuBnB,EAAYK,CAAW,CACtD,CACD,CALOf,EAAS,IAAA0B,EAYT,SAASI,EAAarB,EAAiBsB,EAA6CC,EAAatB,EAAwC,CAC/I,IAAIuB,EAAwBD,EAE5B,OAAOb,EAAUV,EAAOS,IACvBe,EAASF,EAAME,EAAQf,CAAC,EACjBe,GACLvB,CAAU,CACd,CAPOV,EAAS,OAAA8B,EAShB,SAASV,EAAYX,EAAiBC,EAAmD,CACxF,IAAIG,EAEEV,EAAsC,CAC3C,wBAAyB,CACxBU,EAAWJ,EAAMyB,EAAQ,KAAMA,CAAO,CACvC,EACA,yBAA0B,CACzBrB,GAAU,QAAQ,CACnB,CACD,EAEKH,GACJR,EAAsBC,CAAO,EAG9B,IAAM+B,EAAU,IAAIC,EAAWhC,CAAO,EAEtC,OAAAO,GAAY,IAAIwB,CAAO,EAEhBA,EAAQ,KAChB,CAMA,SAASL,EAA8CO,EAAMC,EAAuD,CACnH,OAAIA,aAAiB,MACpBA,EAAM,KAAKD,CAAC,EACFC,GACVA,EAAM,IAAID,CAAC,EAELA,CACR,CAsBO,SAASzB,EAAeF,EAAiBsB,EAA6CO,EAAwC,IAAKC,EAAU,GAAOC,EAAwB,GAAOC,EAA+B/B,EAAwC,CAChQ,IAAIgC,EACAT,EACAU,EACAC,EAAoB,EACpBC,EAEE1C,GAAsC,CAC3C,qBAAAsC,EACA,wBAAyB,CACxBC,EAAejC,EAAMqC,IAAO,CAC3BF,IACAX,EAASF,EAAME,EAAQa,EAAG,EAEtBP,GAAW,CAACI,IACfT,EAAQ,KAAKD,CAAM,EACnBA,EAAS,QAGVY,EAAS,IAAM,CACd,IAAME,GAAUd,EAChBA,EAAS,OACTU,EAAS,QACL,CAACJ,GAAWK,EAAoB,IACnCV,EAAQ,KAAKa,EAAQ,EAEtBH,EAAoB,CACrB,EAEI,OAAON,GAAU,UACpB,aAAaK,CAAM,EACnBA,EAAS,WAAWE,EAAQP,CAAK,GAE7BK,IAAW,SACdA,EAAS,EACT,eAAeE,CAAM,EAGxB,CAAC,CACF,EACA,sBAAuB,CAClBL,GAAyBI,EAAoB,GAChDC,IAAS,CAEX,EACA,yBAA0B,CACzBA,EAAS,OACTH,EAAa,QAAQ,CACtB,CACD,EAEKhC,GACJR,EAAsBC,EAAO,EAG9B,IAAM+B,EAAU,IAAIC,EAAWhC,EAAO,EAEtC,OAAAO,GAAY,IAAIwB,CAAO,EAEhBA,EAAQ,KAChB,CA5DOlC,EAAS,SAAAW,EAqET,SAASqC,EAAcvC,EAAiB6B,EAAgB,EAAG5B,EAA0C,CAC3G,OAAOV,EAAM,SAAiBS,EAAO,CAACwC,EAAM/B,IACtC+B,GAGLA,EAAK,KAAK/B,CAAC,EACJ+B,GAHC,CAAC/B,CAAC,EAIRoB,EAAO,OAAW,GAAM,OAAW5B,CAAU,CACjD,CAROV,EAAS,WAAAgD,EA4BT,SAASE,EAASzC,EAAiB0C,EAAkC,CAACC,EAAGC,IAAMD,IAAMC,EAAG3C,EAAwC,CACtI,IAAI4C,EAAY,GACZC,EAEJ,OAAO/B,EAAOf,EAAO+C,GAAS,CAC7B,IAAMC,EAAaH,GAAa,CAACH,EAAOK,EAAOD,CAAK,EACpD,OAAAD,EAAY,GACZC,EAAQC,EACDC,CACR,EAAG/C,CAAU,CACd,CAVOV,EAAS,MAAAkD,EA6BT,SAASQ,EAAYjD,EAAqBkD,EAA2BjD,EAAoD,CAC/H,MAAO,CACNV,EAAM,OAAOS,EAAOkD,EAAKjD,CAAU,EACnCV,EAAM,OAAOS,EAAOS,GAAK,CAACyC,EAAIzC,CAAC,EAAGR,CAAU,CAC7C,CACD,CALOV,EAAS,MAAA0D,EA2BT,SAASE,EAAUnD,EAAiBoD,EAAoB,GAAOC,EAAe,CAAC,EAAGpD,EAAwC,CAChI,IAAIkD,EAAqBE,EAAQ,MAAM,EAEnCjD,EAA+BJ,EAAMS,GAAK,CACzC0C,EACHA,EAAO,KAAK1C,CAAC,EAEbgB,EAAQ,KAAKhB,CAAC,CAEhB,CAAC,EAEGR,GACHA,EAAW,IAAIG,CAAQ,EAGxB,IAAMkD,EAAQ,IAAM,CACnBH,GAAQ,QAAQ1C,GAAKgB,EAAQ,KAAKhB,CAAC,CAAC,EACpC0C,EAAS,IACV,EAEM1B,EAAU,IAAIC,EAAW,CAC9B,wBAAyB,CACnBtB,IACJA,EAAWJ,EAAMS,GAAKgB,EAAQ,KAAKhB,CAAC,CAAC,EACjCR,GACHA,EAAW,IAAIG,CAAQ,EAG1B,EAEA,uBAAwB,CACnB+C,IACCC,EACH,WAAWE,CAAK,EAEhBA,EAAM,EAGT,EAEA,yBAA0B,CACrBlD,GACHA,EAAS,QAAQ,EAElBA,EAAW,IACZ,CACD,CAAC,EAED,OAAIH,GACHA,EAAW,IAAIwB,CAAO,EAGhBA,EAAQ,KAChB,CArDOlC,EAAS,OAAA4D,EAwET,SAASI,EAAYvD,EAAiBwD,EAA6E,CAWzH,MAVqB,CAACpD,EAAUC,EAAUC,IAAgB,CACzD,IAAMmD,EAAKD,EAAW,IAAIE,CAAoB,EAC9C,OAAO1D,EAAM,SAAU+C,EAAO,CAC7B,IAAMvC,EAASiD,EAAG,SAASV,CAAK,EAC5BvC,IAAWmD,GACdvD,EAAS,KAAKC,EAAUG,CAAM,CAEhC,EAAG,OAAWF,CAAW,CAC1B,CAGD,CAZOf,EAAS,MAAAgE,EAchB,IAAMI,EAAgB,OAAO,eAAe,EAE5C,MAAMD,CAAuD,CAA7D,cACC,KAAiB,MAAiC,CAAC,EAEnD,IAAOE,EAAyB,CAC/B,YAAK,MAAM,KAAKA,CAAE,EACX,IACR,CAEA,QAAQA,EAA4B,CACnC,YAAK,MAAM,KAAKC,IACfD,EAAGC,CAAC,EACGA,EACP,EACM,IACR,CAEA,OAAOD,EAA+B,CACrC,YAAK,MAAM,KAAKC,GAAKD,EAAGC,CAAC,EAAIA,EAAIF,CAAa,EACvC,IACR,CAEA,OAAUrC,EAA+CC,EAA+B,CACvF,IAAIiB,EAAOjB,EACX,YAAK,MAAM,KAAKsC,IACfrB,EAAOlB,EAAMkB,EAAMqB,CAAC,EACbrB,EACP,EACM,IACR,CAEA,MAAME,EAAsC,CAACC,EAAGC,IAAMD,IAAMC,EAAuB,CAClF,IAAIC,EAAY,GACZC,EACJ,YAAK,MAAM,KAAKC,GAAS,CACxB,IAAMC,EAAaH,GAAa,CAACH,EAAOK,EAAOD,CAAK,EACpD,OAAAD,EAAY,GACZC,EAAQC,EACDC,EAAaD,EAAQY,CAC7B,CAAC,EAEM,IACR,CAEO,SAASZ,EAAY,CAC3B,QAAWe,KAAQ,KAAK,MAEvB,GADAf,EAAQe,EAAKf,CAAK,EACdA,IAAUY,EACb,MAIF,OAAOZ,CACR,CACD,CAoBO,SAASgB,EAAwBtC,EAA2BuC,EAAmBtD,EAA6BuD,GAAMA,EAAc,CACtI,IAAML,EAAK,IAAIM,IAAgB1D,EAAO,KAAKE,EAAI,GAAGwD,CAAI,CAAC,EACjDC,EAAqB,IAAM1C,EAAQ,GAAGuC,EAAWJ,CAAE,EACnDQ,EAAuB,IAAM3C,EAAQ,eAAeuC,EAAWJ,CAAE,EACjEpD,EAAS,IAAIkB,EAAW,CAAE,uBAAwByC,EAAoB,wBAAyBC,CAAqB,CAAC,EAE3H,OAAO5D,EAAO,KACf,CAPOjB,EAAS,qBAAAwE,EAiBT,SAASM,EAAuB5C,EAA0BuC,EAAmBtD,EAA6BuD,GAAMA,EAAc,CACpI,IAAML,EAAK,IAAIM,IAAgB1D,EAAO,KAAKE,EAAI,GAAGwD,CAAI,CAAC,EACjDC,EAAqB,IAAM1C,EAAQ,iBAAiBuC,EAAWJ,CAAE,EACjEQ,EAAuB,IAAM3C,EAAQ,oBAAoBuC,EAAWJ,CAAE,EACtEpD,EAAS,IAAIkB,EAAW,CAAE,uBAAwByC,EAAoB,wBAAyBC,CAAqB,CAAC,EAE3H,OAAO5D,EAAO,KACf,CAPOjB,EAAS,oBAAA8E,EAYT,SAASC,EAAatE,EAA6B,CACzD,OAAO,IAAI,QAAQuE,GAAWpE,EAAKH,CAAK,EAAEuE,CAAO,CAAC,CACnD,CAFOhF,EAAS,UAAA+E,EAQT,SAASE,EAAeC,EAA2C,CACzE,IAAMjE,EAAS,IAAIkB,EAEnB,OAAA+C,EAAQ,KAAKC,GAAO,CACnBlE,EAAO,KAAKkE,CAAG,CAChB,EAAG,IAAM,CACRlE,EAAO,KAAK,MAAS,CACtB,CAAC,EAAE,QAAQ,IAAM,CAChBA,EAAO,QAAQ,CAChB,CAAC,EAEMA,EAAO,KACf,CAZOjB,EAAS,YAAAiF,EA0BT,SAASG,EAAWC,EAAgBC,EAA6B,CACvE,OAAOD,EAAKnE,GAAKoE,EAAG,KAAKpE,CAAC,CAAC,CAC5B,CAFOlB,EAAS,QAAAoF,EAeT,SAASG,GAAmB9E,EAAiB+E,EAAoCxD,EAA0B,CACjH,OAAAwD,EAAQxD,CAAO,EACRvB,EAAMS,GAAKsE,EAAQtE,CAAC,CAAC,CAC7B,CAHOlB,EAAS,gBAAAuF,GAKhB,MAAME,EAAwC,CAO7C,YAAqBC,EAAkCrD,EAAoC,CAAtE,iBAAAqD,EAHrB,KAAQ,SAAW,EACnB,KAAQ,YAAc,GAGrB,IAAMvF,EAA0B,CAC/B,uBAAwB,IAAM,CAC7BuF,EAAY,YAAY,IAAI,CAC7B,EACA,wBAAyB,IAAM,CAC9BA,EAAY,eAAe,IAAI,CAChC,CACD,EACKrD,GACJnC,EAAsBC,CAAO,EAE9B,KAAK,QAAU,IAAIgC,EAAWhC,CAAO,EACjCkC,GACHA,EAAM,IAAI,KAAK,OAAO,CAExB,CAEA,YAAeqD,EAAyC,CAEvD,KAAK,UACN,CAEA,qBAAwBA,EAA4C,CAEpE,CAEA,aAAyBA,EAAsCC,EAAwB,CAEtF,KAAK,YAAc,EACpB,CAEA,UAAaD,EAAyC,CAErD,KAAK,WACD,KAAK,WAAa,IACrB,KAAK,YAAY,cAAc,EAC3B,KAAK,cACR,KAAK,YAAc,GACnB,KAAK,QAAQ,KAAK,KAAK,YAAY,IAAI,CAAC,GAG3C,CACD,CAMO,SAASE,GAAkBC,EAA0BxD,EAAmC,CAE9F,OADiB,IAAIoD,GAAgBI,EAAKxD,CAAK,EAC/B,QAAQ,KACzB,CAHOrC,EAAS,eAAA4F,GAQT,SAASE,GAAoBC,EAA2C,CAC9E,MAAO,CAAClF,EAAUC,EAAUC,IAAgB,CAC3C,IAAIR,EAAQ,EACRyF,EAAY,GACVC,EAAsB,CAC3B,aAAc,CACb1F,GACD,EACA,WAAY,CACXA,IACIA,IAAU,IACbwF,EAAW,cAAc,EACrBC,IACHA,EAAY,GACZnF,EAAS,KAAKC,CAAQ,GAGzB,EACA,sBAAuB,CAEvB,EACA,cAAe,CACdkF,EAAY,EACb,CACD,EACAD,EAAW,YAAYE,CAAQ,EAC/BF,EAAW,cAAc,EACzB,IAAMrF,EAAa,CAClB,SAAU,CACTqF,EAAW,eAAeE,CAAQ,CACnC,CACD,EAEA,OAAIlF,aAAuBmF,EAC1BnF,EAAY,IAAIL,CAAU,EAChB,MAAM,QAAQK,CAAW,GACnCA,EAAY,KAAKL,CAAU,EAGrBA,CACR,CACD,CAzCOV,EAAS,oBAAA8F,KA5pBA9F,KAAA,IAovBV,IAAMmG,EAAN,MAAMA,CAAe,CAc3B,YAAYC,EAAc,CAP1B,KAAO,cAAwB,EAC/B,KAAO,gBAAkB,EACzB,KAAO,eAAiB,EACxB,KAAO,UAAsB,CAAC,EAK7B,KAAK,KAAO,GAAGA,CAAI,IAAID,EAAe,SAAS,GAC/CA,EAAe,IAAI,IAAI,IAAI,CAC5B,CAEA,MAAME,EAA6B,CAClC,KAAK,WAAa,IAAIC,GACtB,KAAK,cAAgBD,CACtB,CAEA,MAAa,CACZ,GAAI,KAAK,WAAY,CACpB,IAAME,EAAU,KAAK,WAAW,QAAQ,EACxC,KAAK,UAAU,KAAKA,CAAO,EAC3B,KAAK,gBAAkBA,EACvB,KAAK,iBAAmB,EACxB,KAAK,WAAa,MACnB,CACD,CACD,EAjCaJ,EAEI,IAAM,IAAI,IAFdA,EAIG,QAAU,EAJnB,IAAMK,GAANL,EAmCHM,GAA8B,GAWlC,IAAMC,GAAN,MAAMA,EAAe,CAOpB,YACkBC,EACRC,EACAC,GAAgBH,GAAe,WAAW,SAAS,EAAE,EAAE,SAAS,EAAG,GAAG,EAC9E,CAHgB,mBAAAC,EACR,eAAAC,EACA,UAAAC,EALV,KAAQ,eAAyB,CAM7B,CAEJ,SAAgB,CACf,KAAK,SAAS,MAAM,CACrB,CAEA,MAAMC,EAAmBC,EAAiD,CAEzE,IAAMH,EAAY,KAAK,UACvB,GAAIA,GAAa,GAAKG,EAAgBH,EACrC,OAGI,KAAK,UACT,KAAK,QAAU,IAAI,KAEpB,IAAMI,EAAS,KAAK,QAAQ,IAAIF,EAAM,KAAK,GAAK,EAIhD,GAHA,KAAK,QAAQ,IAAIA,EAAM,MAAOE,EAAQ,CAAC,EACvC,KAAK,gBAAkB,EAEnB,KAAK,gBAAkB,EAAG,CAG7B,KAAK,eAAiBJ,EAAY,GAElC,GAAM,CAACK,EAAUC,CAAQ,EAAI,KAAK,qBAAqB,EACjDC,EAAU,IAAI,KAAK,IAAI,8CAA8CJ,CAAa,+CAA+CG,CAAQ,KAC/I,QAAQ,KAAKC,CAAO,EACpB,QAAQ,KAAKF,CAAS,EAEtB,IAAMG,EAAQ,IAAIC,GAAkBF,EAASF,CAAQ,EACrD,KAAK,cAAcG,CAAK,CACzB,CAEA,MAAO,IAAM,CACZ,IAAMJ,EAAS,KAAK,QAAS,IAAIF,EAAM,KAAK,GAAK,EACjD,KAAK,QAAS,IAAIA,EAAM,MAAOE,EAAQ,CAAC,CACzC,CACD,CAEA,sBAAqD,CACpD,GAAI,CAAC,KAAK,QACT,OAED,IAAIC,EACAC,EAAmB,EACvB,OAAW,CAACJ,EAAOE,CAAK,IAAK,KAAK,SAC7B,CAACC,GAAYC,EAAWF,KAC3BC,EAAW,CAACH,EAAOE,CAAK,EACxBE,EAAWF,GAGb,OAAOC,CACR,CACD,EAjEMP,GAEU,QAAU,EAF1B,IAAMY,GAANZ,GAmEMa,EAAN,MAAMC,CAAW,CAOR,YAAqBC,EAAe,CAAf,WAAAA,CAAiB,CAL9C,OAAO,QAAS,CACf,IAAMC,EAAM,IAAI,MAChB,OAAO,IAAIF,EAAWE,EAAI,OAAS,EAAE,CACtC,CAIA,OAAQ,CACP,QAAQ,KAAK,KAAK,MAAM,MAAM;AAAA,CAAI,EAAE,MAAM,CAAC,EAAE,KAAK;AAAA,CAAI,CAAC,CACxD,CACD,EAGaL,GAAN,cAAgC,KAAM,CAC5C,YAAYF,EAAiBL,EAAe,CAC3C,MAAMK,CAAO,EACb,KAAK,KAAO,oBACZ,KAAK,MAAQL,CACd,CACD,EAIaa,GAAN,cAAmC,KAAM,CAC/C,YAAYR,EAAiBL,EAAe,CAC3C,MAAMK,CAAO,EACb,KAAK,KAAO,uBACZ,KAAK,MAAQL,CACd,CACD,EAEIc,GAAK,EACHC,EAAN,KAAyB,CAGxB,YAA4BJ,EAAU,CAAV,WAAAA,EAD5B,KAAO,GAAKG,IAC4B,CACzC,EACME,GAAsB,EAKtBC,GAAkB,CAAIC,EAAmCC,IAA0C,CACxG,GAAID,aAAqBH,EACxBI,EAAGD,CAAS,MAEZ,SAASE,EAAI,EAAGA,EAAIF,EAAU,OAAQE,IAAK,CAC1C,IAAMC,EAAIH,EAAUE,CAAC,EACjBC,GACHF,EAAGE,CAAC,CAEN,CAEF,EAGIC,GAEJ,GAAIC,GAA4B,CAC/B,IAAMC,EAAkB,CAAC,EAEzB,YAAY,IAAM,CACbA,EAAM,SAAW,IAGrB,QAAQ,KAAK,uEAAwE,EACrF,QAAQ,KAAKA,EAAM,KAAK;AAAA,CAAI,CAAC,EAC7BA,EAAM,OAAS,EAChB,EAAG,GAAI,EAEPF,GAAsB,IAAI,qBAAqBG,GAAa,CACvD,OAAOA,GAAc,UACxBD,EAAM,KAAKC,CAAS,CAEtB,CAAC,CACF,CAuBO,IAAMC,EAAN,KAAiB,CAmCvB,YAAYC,EAA0B,CAFtC,KAAU,MAAQ,EAGjB,KAAK,SAAWA,EAChB,KAAK,YAAeC,GAA8B,GAAK,KAAK,UAAU,qBACnE,IAAIpB,GAAemB,GAAS,iBAAmBE,GAAmB,KAAK,UAAU,sBAAwBD,EAA2B,EACtI,OACD,KAAK,SAAW,KAAK,UAAU,UAAY,IAAIE,GAAe,KAAK,SAAS,SAAS,EAAI,OACzF,KAAK,eAAiB,KAAK,UAAU,aACtC,CAEA,SAAU,CACT,GAAI,CAAC,KAAK,UAAW,CAgBpB,GAfA,KAAK,UAAY,GAYb,KAAK,gBAAgB,UAAY,MACpC,KAAK,eAAe,MAAM,EAEvB,KAAK,WAAY,CACpB,GAAIC,GAAmC,CACtC,IAAMb,EAAY,KAAK,WACvB,eAAe,IAAM,CACpBD,GAAgBC,EAAWG,GAAKA,EAAE,OAAO,MAAM,CAAC,CACjD,CAAC,CACF,CAEA,KAAK,WAAa,OAClB,KAAK,MAAQ,CACd,CACA,KAAK,UAAU,0BAA0B,EACzC,KAAK,aAAa,QAAQ,CAC3B,CACD,CAMA,IAAI,OAAkB,CACrB,YAAK,SAAW,CAACW,EAAyBC,EAAgBC,IAAkD,CAC3G,GAAI,KAAK,aAAe,KAAK,MAAQ,KAAK,YAAY,WAAa,EAAG,CACrE,IAAM7B,EAAU,IAAI,KAAK,YAAY,IAAI,+EAA+E,KAAK,KAAK,OAAO,KAAK,YAAY,SAAS,IACnK,QAAQ,KAAKA,CAAO,EAEpB,IAAM8B,EAAQ,KAAK,YAAY,qBAAqB,GAAK,CAAC,gBAAiB,EAAE,EACvE7B,EAAQ,IAAIO,GAAqB,GAAGR,CAAO,+CAA+C8B,EAAM,CAAC,CAAC,UAAWA,EAAM,CAAC,CAAC,EAE3H,OADqB,KAAK,UAAU,iBAAmBN,IAC1CvB,CAAK,EAEX8B,EAAW,IACnB,CAEA,GAAI,KAAK,UAER,OAAOA,EAAW,KAGfH,IACHD,EAAWA,EAAS,KAAKC,CAAQ,GAGlC,IAAMI,EAAY,IAAItB,EAAgBiB,CAAQ,EAE1CM,EACAtC,EACA,KAAK,aAAe,KAAK,OAAS,KAAK,KAAK,KAAK,YAAY,UAAY,EAAG,IAE/EqC,EAAU,MAAQ5B,EAAW,OAAO,EACpC6B,EAAgB,KAAK,YAAY,MAAMD,EAAU,MAAO,KAAK,MAAQ,CAAC,GAGnEN,KACHM,EAAU,MAAQrC,GAASS,EAAW,OAAO,GAGzC,KAAK,WAIC,KAAK,sBAAsBM,GACrC,KAAK,iBAAmB,IAAIwB,GAC5B,KAAK,WAAa,CAAC,KAAK,WAAYF,CAAS,GAE7C,KAAK,WAAW,KAAKA,CAAS,GAP9B,KAAK,UAAU,yBAAyB,IAAI,EAC5C,KAAK,WAAaA,EAClB,KAAK,UAAU,wBAAwB,IAAI,GAQ5C,KAAK,QAGL,IAAMG,EAASC,EAAa,IAAM,CACjCnB,IAAqB,WAAWkB,CAAM,EACtCF,IAAgB,EAChB,KAAK,gBAAgBD,CAAS,CAC/B,CAAC,EAOD,GANIH,aAAuBQ,EAC1BR,EAAY,IAAIM,CAAM,EACZ,MAAM,QAAQN,CAAW,GACnCA,EAAY,KAAKM,CAAM,EAGpBlB,GAAqB,CACxB,IAAMtB,EAAQ,IAAI,MAAM,EAAE,MAAO,MAAM;AAAA,CAAI,EAAE,MAAM,EAAG,CAAC,EAAE,KAAK;AAAA,CAAI,EAAE,KAAK,EACnE2C,EAAQ,uDAAuD,KAAK3C,CAAK,EAC/EsB,GAAoB,SAASkB,EAAQG,IAAQ,CAAC,GAAK3C,EAAOwC,CAAM,CACjE,CAEA,OAAOA,CACR,EAEO,KAAK,MACb,CAEQ,gBAAgBI,EAAgC,CAGvD,GAFA,KAAK,UAAU,uBAAuB,IAAI,EAEtC,CAAC,KAAK,WACT,OAGD,GAAI,KAAK,QAAU,EAAG,CACrB,KAAK,WAAa,OAClB,KAAK,UAAU,0BAA0B,IAAI,EAC7C,KAAK,MAAQ,EACb,MACD,CAGA,IAAM1B,EAAY,KAAK,WAEjB2B,EAAQ3B,EAAU,QAAQ0B,CAAQ,EACxC,GAAIC,IAAU,GACb,cAAQ,IAAI,YAAa,KAAK,SAAS,EACvC,QAAQ,IAAI,QAAS,KAAK,KAAK,EAC/B,QAAQ,IAAI,OAAQ,KAAK,UAAU,KAAK,UAAU,CAAC,EAC7C,IAAI,MAAM,uCAAuC,EAGxD,KAAK,QACL3B,EAAU2B,CAAK,EAAI,OAEnB,IAAMC,EAAsB,KAAK,eAAgB,UAAY,KAC7D,GAAI,KAAK,MAAQ9B,IAAuBE,EAAU,OAAQ,CACzD,IAAI6B,EAAI,EACR,QAAS3B,EAAI,EAAGA,EAAIF,EAAU,OAAQE,IACjCF,EAAUE,CAAC,EACdF,EAAU6B,GAAG,EAAI7B,EAAUE,CAAC,EAClB0B,IACV,KAAK,eAAgB,MACjBC,EAAI,KAAK,eAAgB,GAC5B,KAAK,eAAgB,KAIxB7B,EAAU,OAAS6B,CACpB,CACD,CAEQ,SAASH,EAA2DjC,EAAU,CACrF,GAAI,CAACiC,EACJ,OAGD,IAAMI,EAAe,KAAK,UAAU,iBAAmBnB,GACvD,GAAI,CAACmB,EAAc,CAClBJ,EAAS,MAAMjC,CAAK,EACpB,MACD,CAEA,GAAI,CACHiC,EAAS,MAAMjC,CAAK,CACrB,OAASsC,EAAG,CACXD,EAAaC,CAAC,CACf,CACD,CAGQ,cAAcC,EAA+B,CACpD,IAAMhC,EAAYgC,EAAG,QAAS,WAC9B,KAAOA,EAAG,EAAIA,EAAG,KAEhB,KAAK,SAAShC,EAAUgC,EAAG,GAAG,EAAGA,EAAG,KAAU,EAE/CA,EAAG,MAAM,CACV,CAMA,KAAKC,EAAgB,CAQpB,GAPI,KAAK,gBAAgB,UACxB,KAAK,cAAc,KAAK,cAAc,EACtC,KAAK,UAAU,KAAK,GAGrB,KAAK,UAAU,MAAM,KAAK,KAAK,EAE1B,KAAK,WAEH,GAAI,KAAK,sBAAsBpC,EACrC,KAAK,SAAS,KAAK,WAAYoC,CAAK,MAC9B,CACN,IAAMD,EAAK,KAAK,eAChBA,EAAG,QAAQ,KAAMC,EAAO,KAAK,WAAW,MAAM,EAC9C,KAAK,cAAcD,CAAE,CACtB,CAEA,KAAK,UAAU,KAAK,CACrB,CAEA,cAAwB,CACvB,OAAO,KAAK,MAAQ,CACrB,CACD,EAQA,IAAME,GAAN,KAA8D,CAA9D,cAMC,KAAO,EAAI,GAKX,KAAO,IAAM,EAWN,QAAWC,EAAqBC,EAAUC,EAAa,CAC7D,KAAK,EAAI,EACT,KAAK,IAAMA,EACX,KAAK,QAAUF,EACf,KAAK,MAAQC,CACd,CAEO,OAAQ,CACd,KAAK,EAAI,KAAK,IACd,KAAK,QAAU,OACf,KAAK,MAAQ,MACd,CACD,EC/tCA,IAAME,GAAsB,qCACtBC,GAA2B,GAAK,IAChCC,GAA0B,IAEnBC,GAAN,cAA0BC,CAAkD,CAqBjF,YAAYC,EAAwC,CAClD,MAAM,EAnBR,KAAQ,kBAAiC,IAAI,IAC7C,KAAQ,sBAAsC,CAAC,EAC/C,KAAQ,oBAA0D,KAAK,UAAU,IAAIC,CAAmB,EAUxG,KAAQ,qBAAuB,EAC/B,KAAQ,uBAAyB,IAAIA,EAErC,KAAiB,oBAAsB,KAAK,UAAU,IAAIC,CAAuD,EACjH,KAAgB,mBAAqB,KAAK,oBAAoB,MAK5D,KAAK,gBAAkBF,GAAS,gBAAkBH,EACpD,CAEO,SAASM,EAA0B,CACxC,KAAK,UAAYA,EACjB,KAAK,UAAU,KAAK,UAAU,cAAc,IAAM,KAAK,eAAe,CAAC,CAAC,EACxE,KAAK,UAAU,KAAK,UAAU,SAAS,IAAM,KAAK,eAAe,CAAC,CAAC,EACnE,KAAK,UAAUC,EAAa,IAAM,KAAK,iBAAiB,CAAC,CAAC,CAC5D,CAEQ,gBAAuB,CACzB,KAAK,mBACP,OAAO,aAAa,KAAK,iBAAiB,EAExC,KAAK,mBAAqB,KAAK,oBAAoB,cACrD,KAAK,kBAAoB,WAAW,IAAM,CACxC,IAAMC,EAAO,KAAK,kBAClB,KAAK,kBAAoB,OACzB,KAAK,aAAaA,EAAO,CAAE,GAAG,KAAK,mBAAoB,YAAa,GAAM,SAAU,EAAK,CAAC,CAC5F,EAAG,GAAG,EAEV,CAEO,iBAAiBC,EAAwC,CAC9D,KAAK,oBAAoB,MAAM,EAC/BC,EAAQ,KAAK,qBAAqB,EAClC,KAAK,sBAAwB,CAAC,EAC9B,KAAK,kBAAkB,MAAM,EACxBD,IACH,KAAK,kBAAoB,OAE7B,CAEO,uBAA8B,CACnC,KAAK,oBAAoB,MAAM,CACjC,CASO,SAASD,EAAcG,EAAyC,CACrE,GAAI,CAAC,KAAK,UACR,MAAM,IAAI,MAAM,2CAA2C,EAE7D,IAAMC,EAAoB,KAAK,mBAAqB,KAAK,kBAAkB,KAAK,mBAAoBD,CAAa,EAAI,GACrH,KAAK,mBAAqBA,EACtBA,GAAe,cACb,KAAK,oBAAsB,QAAaH,IAAS,KAAK,mBAAqBI,IAC7E,KAAK,qBAAqBJ,EAAMG,CAAa,EAIjD,IAAME,EAAQ,KAAK,mBAAmBL,EAAMG,CAAa,EACzD,YAAK,aAAaA,CAAa,EAC/B,KAAK,kBAAoBH,EAElBK,CACT,CAEQ,qBAAqBL,EAAcG,EAAqC,CAC9E,GAAI,CAAC,KAAK,UACR,MAAM,IAAI,MAAM,2CAA2C,EAE7D,GAAI,CAACH,GAAQA,EAAK,SAAW,EAAG,CAC9B,KAAK,iBAAiB,EACtB,MACF,CACAG,EAAgBA,GAAiB,CAAC,EAGlC,KAAK,iBAAiB,EAAI,EAE1B,IAAMG,EAA8C,CAAC,EACjDC,EACAC,EAAS,KAAK,MAAMR,EAAM,EAAG,EAAGG,CAAa,EACjD,KAAOK,IAAWD,GAAY,MAAQC,EAAO,KAAOD,GAAY,MAAQC,EAAO,MACzE,EAAAF,EAA2B,QAAU,KAAK,kBAG9CC,EAAaC,EACbF,EAA2B,KAAKC,CAAU,EAC1CC,EAAS,KAAK,MACZR,EACAO,EAAW,IAAMA,EAAW,KAAK,QAAU,KAAK,UAAU,KAAOA,EAAW,IAAM,EAAIA,EAAW,IACjGA,EAAW,IAAMA,EAAW,KAAK,QAAU,KAAK,UAAU,KAAO,EAAIA,EAAW,IAAM,EACtFJ,CACF,EAEF,QAAWM,KAASH,EAA4B,CAC9C,IAAMI,EAAc,KAAK,yBAAyBD,EAAON,EAAc,YAAc,EAAK,EAC1F,GAAIO,EACF,QAAWC,KAAcD,EACvB,KAAK,iBAAiBC,EAAYF,CAAK,CAG7C,CACF,CAEQ,iBAAiBE,EAAyBF,EAA4B,CAC5E,KAAK,kBAAkB,IAAIE,EAAW,OAAO,IAAI,EACjD,KAAK,sBAAsB,KAAK,CAAE,WAAAA,EAAY,MAAAF,EAAO,SAAU,CAAEE,EAAW,QAAQ,CAAG,CAAE,CAAC,CAC5F,CAEQ,MAAMX,EAAcY,EAAkBC,EAAkBV,EAA2D,CACzH,GAAI,CAAC,KAAK,WAAa,CAACH,GAAQA,EAAK,SAAW,EAAG,CACjD,KAAK,WAAW,eAAe,EAC/B,KAAK,iBAAiB,EACtB,MACF,CACA,GAAIa,EAAW,KAAK,UAAU,KAC5B,MAAM,IAAI,MAAM,gBAAgBA,CAAQ,6BAA6B,KAAK,UAAU,IAAI,OAAO,EAGjG,IAAIL,EAEJ,KAAK,gBAAgB,EAErB,IAAMM,EAAkC,CACtC,SAAAF,EACA,SAAAC,CACF,EAKA,GAFAL,EAAS,KAAK,YAAYR,EAAMc,EAAgBX,CAAa,EAEzD,CAACK,EAEH,QAASO,EAAIH,EAAW,EAAGG,EAAI,KAAK,UAAU,OAAO,OAAO,MAAQ,KAAK,UAAU,OACjFD,EAAe,SAAWC,EAC1BD,EAAe,SAAW,EAG1BN,EAAS,KAAK,YAAYR,EAAMc,EAAgBX,CAAa,EACzD,CAAAK,GANmFO,IAMvF,CAKJ,OAAOP,CACT,CAEQ,mBAAmBR,EAAcG,EAAyC,CAChF,GAAI,CAAC,KAAK,WAAa,CAACH,GAAQA,EAAK,SAAW,EAC9C,YAAK,WAAW,eAAe,EAC/B,KAAK,iBAAiB,EACf,GAGT,IAAMgB,EAAkB,KAAK,UAAU,qBAAqB,EAC5D,KAAK,UAAU,eAAe,EAE9B,IAAIH,EAAW,EACXD,EAAW,EACXI,IACE,KAAK,oBAAsBhB,GAC7Ba,EAAWG,EAAgB,IAAI,EAC/BJ,EAAWI,EAAgB,IAAI,IAE/BH,EAAWG,EAAgB,MAAM,EACjCJ,EAAWI,EAAgB,MAAM,IAIrC,KAAK,gBAAgB,EAErB,IAAMF,EAAkC,CACtC,SAAAF,EACA,SAAAC,CACF,EAGIL,EAAS,KAAK,YAAYR,EAAMc,EAAgBX,CAAa,EAEjE,GAAI,CAACK,EAEH,QAASO,EAAIH,EAAW,EAAGG,EAAI,KAAK,UAAU,OAAO,OAAO,MAAQ,KAAK,UAAU,OACjFD,EAAe,SAAWC,EAC1BD,EAAe,SAAW,EAG1BN,EAAS,KAAK,YAAYR,EAAMc,EAAgBX,CAAa,EACzD,CAAAK,GANmFO,IAMvF,CAMJ,GAAI,CAACP,GAAUI,IAAa,EAC1B,QAASG,EAAI,EAAGA,EAAIH,IAClBE,EAAe,SAAWC,EAC1BD,EAAe,SAAW,EAC1BN,EAAS,KAAK,YAAYR,EAAMc,EAAgBX,CAAa,EACzD,CAAAK,GAJwBO,IAI5B,CAOJ,MAAI,CAACP,GAAUQ,IACbF,EAAe,SAAWE,EAAgB,MAAM,EAChDF,EAAe,SAAW,EAC1BN,EAAS,KAAK,YAAYR,EAAMc,EAAgBX,CAAa,GAIxD,KAAK,cAAcK,EAAQL,GAAe,YAAaA,GAAe,QAAQ,CACvF,CAQO,aAAaH,EAAcG,EAAyC,CACzE,GAAI,CAAC,KAAK,UACR,MAAM,IAAI,MAAM,2CAA2C,EAE7D,IAAMC,EAAoB,KAAK,mBAAqB,KAAK,kBAAkB,KAAK,mBAAoBD,CAAa,EAAI,GACrH,KAAK,mBAAqBA,EACtBA,GAAe,cACb,KAAK,oBAAsB,QAAaH,IAAS,KAAK,mBAAqBI,IAC7E,KAAK,qBAAqBJ,EAAMG,CAAa,EAIjD,IAAME,EAAQ,KAAK,uBAAuBL,EAAMG,CAAa,EAC7D,YAAK,aAAaA,CAAa,EAC/B,KAAK,kBAAoBH,EAElBK,CACT,CAEQ,kBAAkBY,EAAmCd,EAAyC,CACpG,OAAKA,EAGDc,EAAkB,gBAAkBd,EAAc,eAGlDc,EAAkB,QAAUd,EAAc,OAG1Cc,EAAkB,YAAcd,EAAc,UARzC,EAYX,CAEQ,aAAaA,EAAsC,CACzD,GAAIA,GAAe,YAAa,CAC9B,IAAIe,EAAc,GAClB,GAAI,KAAK,oBAAoB,MAAO,CAClC,IAAMC,EAAgB,KAAK,oBAAoB,MAAM,MACrD,QAASC,EAAI,EAAGA,EAAI,KAAK,sBAAsB,OAAQA,IAAK,CAC1D,IAAMX,EAAQ,KAAK,sBAAsBW,CAAC,EAAE,MAC5C,GAAIX,EAAM,MAAQU,EAAc,KAAOV,EAAM,MAAQU,EAAc,KAAOV,EAAM,OAASU,EAAc,KAAM,CAC3GD,EAAcE,EACd,KACF,CACF,CACF,CACA,KAAK,oBAAoB,KAAK,CAAE,YAAAF,EAAa,YAAa,KAAK,sBAAsB,MAAO,CAAC,CAC/F,CACF,CAEQ,uBAAuBlB,EAAcG,EAAyC,CACpF,GAAI,CAAC,KAAK,UACR,MAAM,IAAI,MAAM,2CAA2C,EAE7D,GAAI,CAAC,KAAK,WAAa,CAACH,GAAQA,EAAK,SAAW,EAC9C,YAAK,WAAW,eAAe,EAC/B,KAAK,iBAAiB,EACf,GAGT,IAAMgB,EAAkB,KAAK,UAAU,qBAAqB,EAC5D,KAAK,UAAU,eAAe,EAE9B,IAAIJ,EAAW,KAAK,UAAU,OAAO,OAAO,MAAQ,KAAK,UAAU,KAAO,EACtEC,EAAW,KAAK,UAAU,KACxBQ,EAAkB,GAExB,KAAK,gBAAgB,EACrB,IAAMP,EAAkC,CACtC,SAAAF,EACA,SAAAC,CACF,EAEIL,EAoBJ,GAnBIQ,IACFF,EAAe,SAAWF,EAAWI,EAAgB,MAAM,EAC3DF,EAAe,SAAWD,EAAWG,EAAgB,MAAM,EACvD,KAAK,oBAAsBhB,IAE7BQ,EAAS,KAAK,YAAYR,EAAMc,EAAgBX,EAAe,EAAK,EAC/DK,IAEHM,EAAe,SAAWF,EAAWI,EAAgB,IAAI,EACzDF,EAAe,SAAWD,EAAWG,EAAgB,IAAI,KAK1DR,IACHA,EAAS,KAAK,YAAYR,EAAMc,EAAgBX,EAAekB,CAAe,GAI5E,CAACb,EAAQ,CACXM,EAAe,SAAW,KAAK,IAAIA,EAAe,SAAU,KAAK,UAAU,IAAI,EAC/E,QAASC,EAAIH,EAAW,EAAGG,GAAK,IAC9BD,EAAe,SAAWC,EAC1BP,EAAS,KAAK,YAAYR,EAAMc,EAAgBX,EAAekB,CAAe,EAC1E,CAAAb,GAH6BO,IAGjC,CAIJ,CAEA,GAAI,CAACP,GAAUI,IAAc,KAAK,UAAU,OAAO,OAAO,MAAQ,KAAK,UAAU,KAAO,EACtF,QAASG,EAAK,KAAK,UAAU,OAAO,OAAO,MAAQ,KAAK,UAAU,KAAO,EAAIA,GAAKH,IAChFE,EAAe,SAAWC,EAC1BP,EAAS,KAAK,YAAYR,EAAMc,EAAgBX,EAAekB,CAAe,EAC1E,CAAAb,GAHsFO,IAG1F,CAOJ,OAAO,KAAK,cAAcP,EAAQL,GAAe,YAAaA,GAAe,QAAQ,CACvF,CAKQ,iBAAwB,CAC9B,IAAML,EAAW,KAAK,UACjB,KAAK,cACR,KAAK,YAAc,IAAI,MAAMA,EAAS,OAAO,OAAO,MAAM,EAC1D,KAAK,uBAAuB,MAAQwB,GAClCxB,EAAS,WAAW,IAAM,KAAK,mBAAmB,CAAC,EACnDA,EAAS,aAAa,IAAM,KAAK,mBAAmB,CAAC,EACrDA,EAAS,SAAS,IAAM,KAAK,mBAAmB,CAAC,CACnD,GAGF,OAAO,aAAa,KAAK,oBAAoB,EAC7C,KAAK,qBAAuB,OAAO,WAAW,IAAM,KAAK,mBAAmB,EAAGP,EAAwB,CACzG,CAEQ,oBAA2B,CACjC,KAAK,YAAc,OACnB,KAAK,uBAAuB,MAAM,EAC9B,KAAK,uBACP,OAAO,aAAa,KAAK,oBAAoB,EAC7C,KAAK,qBAAuB,EAEhC,CASQ,aAAagC,EAAqBC,EAAcxB,EAAuB,CAC7E,OAASuB,IAAgB,GAAOjC,GAAoB,SAASkC,EAAKD,EAAc,CAAC,CAAC,KAC7EA,EAAcvB,EAAK,SAAYwB,EAAK,QAAYlC,GAAoB,SAASkC,EAAKD,EAAcvB,EAAK,MAAM,CAAC,EACnH,CAcU,YAAYA,EAAcc,EAAiCX,EAAgC,CAAC,EAAGkB,EAA2B,GAAkC,CACpK,IAAMvB,EAAW,KAAK,UAChB2B,EAAMX,EAAe,SACrBY,EAAMZ,EAAe,SAI3B,GADkBhB,EAAS,OAAO,OAAO,QAAQ2B,CAAG,GACrC,UAAW,CACxB,GAAIJ,EAAiB,CACnBP,EAAe,UAAYhB,EAAS,KACpC,MACF,CAIA,OAAAgB,EAAe,WACfA,EAAe,UAAYhB,EAAS,KAC7B,KAAK,YAAYE,EAAMc,EAAgBX,CAAa,CAC7D,CACA,IAAIwB,EAAQ,KAAK,cAAcF,CAAG,EAC7BE,IACHA,EAAQ,KAAK,qCAAqCF,EAAK,EAAI,EACvD,KAAK,cACP,KAAK,YAAYA,CAAG,EAAIE,IAG5B,GAAM,CAACC,EAAYC,CAAO,EAAIF,EAExBG,EAAS,KAAK,0BAA0BL,EAAKC,CAAG,EAClDK,EAAa/B,EACbgC,EAAmBJ,EAClBzB,EAAc,QACjB4B,EAAa5B,EAAc,cAAgBH,EAAOA,EAAK,YAAY,EACnEgC,EAAmB7B,EAAc,cAAgByB,EAAaA,EAAW,YAAY,GAGvF,IAAIV,EAAc,GAClB,GAAIf,EAAc,MAAO,CACvB,IAAM8B,EAAc,OAAOF,EAAY5B,EAAc,cAAgB,IAAM,IAAI,EAC3E+B,EACJ,GAAIb,EAEF,KAAOa,EAAYD,EAAY,KAAKD,EAAiB,MAAM,EAAGF,CAAM,CAAC,GACnEZ,EAAce,EAAY,UAAYC,EAAU,CAAC,EAAE,OACnDlC,EAAOkC,EAAU,CAAC,EAClBD,EAAY,WAAcjC,EAAK,OAAS,OAG1CkC,EAAYD,EAAY,KAAKD,EAAiB,MAAMF,CAAM,CAAC,EACvDI,GAAaA,EAAU,CAAC,EAAE,OAAS,IACrChB,EAAcY,GAAUG,EAAY,UAAYC,EAAU,CAAC,EAAE,QAC7DlC,EAAOkC,EAAU,CAAC,EAGxB,MACMb,EACES,EAASC,EAAW,QAAU,IAChCb,EAAcc,EAAiB,YAAYD,EAAYD,EAASC,EAAW,MAAM,GAGnFb,EAAcc,EAAiB,QAAQD,EAAYD,CAAM,EAI7D,GAAIZ,GAAe,EAAG,CACpB,GAAIf,EAAc,WAAa,CAAC,KAAK,aAAae,EAAac,EAAkBhC,CAAI,EACnF,OAKF,IAAImC,EAAiB,EACrB,KAAOA,EAAiBN,EAAQ,OAAS,GAAKX,GAAeW,EAAQM,EAAiB,CAAC,GACrFA,IAEF,IAAIC,EAAeD,EACnB,KAAOC,EAAeP,EAAQ,OAAS,GAAKX,EAAclB,EAAK,QAAU6B,EAAQO,EAAe,CAAC,GAC/FA,IAEF,IAAMC,EAAiBnB,EAAcW,EAAQM,CAAc,EACrDG,EAAepB,EAAclB,EAAK,OAAS6B,EAAQO,CAAY,EAC/DG,EAAgB,KAAK,0BAA0Bd,EAAMU,EAAgBE,CAAc,EAEnFG,EADc,KAAK,0BAA0Bf,EAAMW,EAAcE,CAAY,EACxDC,EAAgBzC,EAAS,MAAQsC,EAAeD,GAE3E,MAAO,CACL,KAAAnC,EACA,IAAKuC,EACL,IAAKd,EAAMU,EACX,KAAAK,CACF,CACF,CACF,CAEQ,0BAA0Bf,EAAaK,EAAwB,CACrE,IAAMN,EAAO,KAAK,UAAW,OAAO,OAAO,QAAQC,CAAG,EACtD,GAAI,CAACD,EACH,MAAO,GAET,QAASJ,EAAI,EAAGA,EAAIU,EAAQV,IAAK,CAC/B,IAAMqB,EAAOjB,EAAK,QAAQJ,CAAC,EAC3B,GAAI,CAACqB,EACH,MAGF,IAAMC,EAAOD,EAAK,SAAS,EACvBC,EAAK,OAAS,IAChBZ,GAAUY,EAAK,OAAS,GAI1B,IAAMC,EAAWnB,EAAK,QAAQJ,EAAI,CAAC,EAC/BuB,GAAYA,EAAS,SAAS,IAAM,GACtCb,GAEJ,CACA,OAAOA,CACT,CAEQ,0BAA0BlB,EAAkBgC,EAAsB,CACxE,IAAM9C,EAAW,KAAK,UAClB+C,EAAYjC,EACZkB,EAAS,EACTN,EAAO1B,EAAS,OAAO,OAAO,QAAQ+C,CAAS,EACnD,KAAOD,EAAO,GAAKpB,GAAM,CACvB,QAASJ,EAAI,EAAGA,EAAIwB,GAAQxB,EAAItB,EAAS,KAAMsB,IAAK,CAClD,IAAMqB,EAAOjB,EAAK,QAAQJ,CAAC,EAC3B,GAAI,CAACqB,EACH,MAEEA,EAAK,SAAS,IAEhBX,GAAUW,EAAK,QAAQ,IAAM,EAAI,EAAIA,EAAK,SAAS,EAAE,OAEzD,CAGA,GAFAI,IACArB,EAAO1B,EAAS,OAAO,OAAO,QAAQ+C,CAAS,EAC3CrB,GAAQ,CAACA,EAAK,UAChB,MAEFoB,GAAQ9C,EAAS,IACnB,CACA,OAAOgC,CACT,CAUQ,qCAAqCe,EAAmBC,EAAoC,CAClG,IAAMhD,EAAW,KAAK,UAChBiD,EAAU,CAAC,EACXC,EAAc,CAAC,CAAC,EAClBxB,EAAO1B,EAAS,OAAO,OAAO,QAAQ+C,CAAS,EACnD,KAAOrB,GAAM,CACX,IAAMyB,EAAWnD,EAAS,OAAO,OAAO,QAAQ+C,EAAY,CAAC,EACvDK,EAAkBD,EAAWA,EAAS,UAAY,GACpDE,EAAS3B,EAAK,kBAAkB,CAAC0B,GAAmBJ,CAAS,EACjE,GAAII,GAAmBD,EAAU,CAC/B,IAAMG,EAAW5B,EAAK,QAAQA,EAAK,OAAS,CAAC,EACtB4B,GAAYA,EAAS,QAAQ,IAAM,GAAKA,EAAS,SAAS,IAAM,GAEjEH,EAAS,QAAQ,CAAC,GAAG,SAAS,IAAM,IACxDE,EAASA,EAAO,MAAM,EAAG,EAAE,EAE/B,CAEA,GADAJ,EAAQ,KAAKI,CAAM,EACfD,EACFF,EAAY,KAAKA,EAAYA,EAAY,OAAS,CAAC,EAAIG,EAAO,MAAM,MAEpE,OAEFN,IACArB,EAAOyB,CACT,CACA,MAAO,CAACF,EAAQ,KAAK,EAAE,EAAGC,CAAW,CACvC,CAOQ,cAAcxC,EAAmCb,EAAoC0D,EAA6B,CACxH,IAAMvD,EAAW,KAAK,UAEtB,GADA,KAAK,oBAAoB,MAAM,EAC3B,CAACU,EACH,OAAAV,EAAS,eAAe,EACjB,GAGT,GADAA,EAAS,OAAOU,EAAO,IAAKA,EAAO,IAAKA,EAAO,IAAI,EAC/Cb,EAAS,CACX,IAAMe,EAAc,KAAK,yBAAyBF,EAAQb,EAAS,EAAI,EACnEe,IACF,KAAK,oBAAoB,MAAQ,CAAE,YAAAA,EAAa,MAAOF,EAAQ,SAAU,CAAEN,EAAQQ,CAAW,CAAG,CAAE,EAEvG,CAEA,GAAI,CAAC2C,IAEC7C,EAAO,KAAQV,EAAS,OAAO,OAAO,UAAYA,EAAS,MAASU,EAAO,IAAMV,EAAS,OAAO,OAAO,WAAW,CACrH,IAAIwD,EAAS9C,EAAO,IAAMV,EAAS,OAAO,OAAO,UACjDwD,GAAU,KAAK,MAAMxD,EAAS,KAAO,CAAC,EACtCA,EAAS,YAAYwD,CAAM,CAC7B,CAEF,MAAO,EACT,CASQ,aAAaC,EAAsBC,EAAiCC,EAA+B,CACpGF,EAAQ,UAAU,SAAS,8BAA8B,IAC5DA,EAAQ,UAAU,IAAI,8BAA8B,EAChDC,IACFD,EAAQ,MAAM,QAAU,aAAaC,CAAW,KAGhDC,GACFF,EAAQ,UAAU,IAAI,qCAAqC,CAE/D,CAQQ,yBAAyB/C,EAAuBb,EAAmC8D,EAAoD,CAC7I,IAAM3D,EAAW,KAAK,UAGhB4D,EAA+C,CAAC,EAClDC,EAAanD,EAAO,IACpBoD,EAAgBpD,EAAO,KACvBqD,EAAe,CAAC/D,EAAS,OAAO,OAAO,MAAQA,EAAS,OAAO,OAAO,QAAUU,EAAO,IAC3F,KAAOoD,EAAgB,GAAG,CACxB,IAAME,EAAgB,KAAK,IAAIhE,EAAS,KAAO6D,EAAYC,CAAa,EACxEF,EAAiB,KAAK,CAACG,EAAcF,EAAYG,CAAa,CAAC,EAC/DH,EAAa,EACbC,GAAiBE,EACjBD,GACF,CAGA,IAAMnD,EAA6B,CAAC,EACpC,QAAWqD,KAASL,EAAkB,CACpC,IAAMM,EAASlE,EAAS,eAAeiE,EAAM,CAAC,CAAC,EACzCpD,EAAab,EAAS,mBAAmB,CAC7C,OAAAkE,EACA,EAAGD,EAAM,CAAC,EACV,MAAOA,EAAM,CAAC,EACd,gBAAiBN,EAAiB9D,EAAQ,sBAAwBA,EAAQ,gBAC1E,qBAAsB,KAAK,kBAAkB,IAAIqE,EAAO,IAAI,EAAI,OAAY,CAC1E,MAAOP,EAAiB9D,EAAQ,8BAAgCA,EAAQ,mBACxE,SAAU,QACZ,CACF,CAAC,EACD,GAAIgB,EAAY,CACd,IAAMsD,EAA6B,CAAC,EACpCA,EAAY,KAAKD,CAAM,EACvBC,EAAY,KAAKtD,EAAW,SAAUuD,GAAM,KAAK,aAAaA,EAAGT,EAAiB9D,EAAQ,kBAAoBA,EAAQ,YAAa,EAAK,CAAC,CAAC,EAC1IsE,EAAY,KAAKtD,EAAW,UAAU,IAAMT,EAAQ+D,CAAW,CAAC,CAAC,EACjEvD,EAAY,KAAKC,CAAU,CAC7B,CACF,CAEA,OAAOD,EAAY,SAAW,EAAI,OAAYA,CAChD,CACF", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ErrorNoTelemetry", "listener", "newUnexpectedErrorHandler", "<PERSON><PERSON><PERSON><PERSON>", "onUnexpectedError", "e", "isCancellationError", "<PERSON><PERSON><PERSON><PERSON>", "canceledName", "isCancellationError", "error", "CancellationError", "ErrorNoTelemetry", "_ErrorNoTelemetry", "msg", "err", "result", "createSingleCallFunction", "fn", "fnDidRunCallback", "_this", "didCall", "result", "findLastIdxMonotonous", "array", "predicate", "startIdx", "endIdxEx", "j", "k", "_MonotonousArray", "_array", "predicate", "item", "idx", "findLastIdxMonotonous", "MonotonousArray", "CompareResult", "is<PERSON><PERSON><PERSON><PERSON>", "result", "isLessThanOrEqual", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isNeitherLessOrGreaterThan", "compareBy", "selector", "comparator", "a", "b", "numberComparator", "a", "b", "_CallbackIterable", "iterate", "handler", "item", "result", "predicate", "cb", "mapFn", "comparator", "first", "CompareResult", "_callback", "CallbackIterable", "groupBy", "data", "groupFn", "result", "element", "key", "target", "_a", "_b", "SetWith<PERSON>ey", "values", "to<PERSON><PERSON>", "value", "key", "entry", "callbackfn", "thisArg", "SetMap", "key", "value", "values", "fn", "Iterable", "is", "thing", "_empty", "empty", "single", "element", "wrap", "iterableOrElement", "from", "iterable", "reverse", "array", "i", "isEmpty", "first", "some", "predicate", "find", "filter", "map", "fn", "index", "flatMap", "concat", "iterables", "reduce", "reducer", "initialValue", "value", "slice", "arr", "to", "consume", "atMost", "consumed", "iterator", "next", "asyncToArray", "result", "item", "TRACK_DISPOSABLES", "disposableTracker", "_DisposableTracker", "d", "val", "data", "child", "parent", "x", "disposable", "cache", "cacheValue", "result", "rootParentCache", "v", "k", "maxReported", "preComputedLeaks", "uncoveredLeakingObjs", "leakingObjects", "info", "leakingObjsSet", "o", "l", "getStackTracePath", "leaking", "removePrefix", "array", "linesToRemove", "regexp", "lines", "p", "stackTraceStarts", "SetMap", "stackTracePath", "i", "compareBy", "numberComparator", "message", "stackTraceFormattedLines", "line", "prevStarts", "continuations", "groupBy", "cont", "set", "DisposableTracker", "setDisposableTracker", "tracker", "__is_disposable_tracked__", "stack", "Disposable", "trackDisposable", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setParentOfDisposable", "setParentOfDisposables", "children", "dispose", "arg", "Iterable", "errors", "d", "e", "combinedDisposable", "disposables", "parent", "toDisposable", "dispose", "setParentOfDisposables", "fn", "self", "trackDisposable", "createSingleCallFunction", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_DisposableStore", "o", "setParentOfDisposable", "DisposableStore", "Disposable", "MutableDisposable", "value", "oldValue", "_Node", "element", "Node", "hasPerformanceNow", "StopWatch", "_StopWatch", "highResolution", "_enableListenerGCedWarning", "_enableDisposeWithListenerWarning", "_enableSnapshotPotentialLeakWarning", "Event", "Disposable", "_addLeakageTraceLogic", "options", "origListenerDidAdd", "stack", "Stacktrace", "count", "defer", "event", "disposable", "debounce", "once", "listener", "thisArgs", "disposables", "<PERSON><PERSON><PERSON>", "result", "e", "map", "snapshot", "i", "for<PERSON>ach", "each", "filter", "signal", "any", "events", "combinedDisposable", "addAndReturnDisposable", "reduce", "merge", "initial", "output", "emitter", "Emitter", "d", "store", "delay", "leading", "flushOnListenerRemove", "leakWarningThreshold", "subscription", "handle", "numDebouncedCalls", "doFire", "cur", "_output", "accumulate", "last", "latch", "equals", "a", "b", "firstCall", "cache", "value", "shouldEmit", "split", "isT", "buffer", "flushAfterTimeout", "_buffer", "flush", "chain", "sythen<PERSON><PERSON>", "cs", "ChainableSynthesis", "HaltChainable", "fn", "v", "step", "fromNodeEventEmitter", "eventName", "id", "args", "onFirstListenerAdd", "onLastListenerRemove", "fromDOMEventEmitter", "to<PERSON>romise", "resolve", "fromPromise", "promise", "res", "forward", "from", "to", "runAndSubscribe", "handler", "EmitterObserver", "_observable", "_change", "fromObservable", "obs", "fromObservableLight", "observable", "<PERSON><PERSON><PERSON><PERSON>", "observer", "DisposableStore", "_EventProfiling", "name", "listenerCount", "StopWatch", "elapsed", "EventProfiling", "_globalLeakWarningThreshold", "_LeakageMonitor", "_error<PERSON><PERSON><PERSON>", "threshold", "name", "stack", "listenerCount", "count", "topStack", "topCount", "message", "error", "ListenerLeakError", "LeakageMonitor", "Stacktrace", "_Stacktrace", "value", "err", "ListenerRefusalError", "id", "UniqueContainer", "compactionThreshold", "forEachListener", "listeners", "fn", "i", "l", "_listenerFinalizers", "_enableListenerGCedWarning", "leaks", "heldValue", "Emitter", "options", "_globalLeakWarningThreshold", "onUnexpectedError", "EventProfiling", "_enableDisposeWithListenerWarning", "callback", "thisArgs", "disposables", "tuple", "Disposable", "contained", "removeMonitor", "EventDeliveryQueuePrivate", "result", "toDisposable", "DisposableStore", "match", "listener", "index", "adjustDeliveryQueue", "n", "<PERSON><PERSON><PERSON><PERSON>", "e", "dq", "event", "EventDeliveryQueuePrivate", "emitter", "value", "end", "NON_WORD_CHARACTERS", "LINES_CACHE_TIME_TO_LIVE", "DEFAULT_HIGHLIGHT_LIMIT", "SearchAddon", "Disposable", "options", "MutableDisposable", "Emitter", "terminal", "toDisposable", "term", "retainCachedSearchTerm", "dispose", "searchOptions", "didOptionsChanged", "found", "searchResultsWithHighlight", "prevResult", "result", "match", "decorations", "decoration", "startRow", "startCol", "searchPosition", "y", "prevSelectedPos", "lastSearchOptions", "resultIndex", "selectedM<PERSON>", "i", "isReverseSearch", "combinedDisposable", "searchIndex", "line", "row", "col", "cache", "stringLine", "offsets", "offset", "searchTerm", "searchStringLine", "searchRegex", "foundTerm", "startRowOffset", "endRowOffset", "startColOffset", "endColOffset", "startColIndex", "size", "cell", "char", "nextCell", "cols", "lineIndex", "trimRight", "strings", "lineOffsets", "nextLine", "lineWrapsToNext", "string", "lastCell", "noScroll", "scroll", "element", "borderColor", "isActiveResult", "decorationRanges", "currentCol", "remainingSize", "markerOffset", "amountThisRow", "range", "marker", "disposables", "e"]}