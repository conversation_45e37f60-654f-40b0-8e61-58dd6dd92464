{"name": "@xterm/xterm", "description": "Full xterm terminal, in your browser", "version": "5.6.0-beta.112", "main": "lib/xterm.js", "module": "lib/xterm.mjs", "style": "css/xterm.css", "types": "typings/xterm.d.ts", "repository": "https://github.com/xtermjs/xterm.js", "license": "MIT", "keywords": ["cli", "command-line", "console", "pty", "shell", "ssh", "styles", "terminal-emulator", "terminal", "tty", "vt100", "webgl", "xterm"], "scripts": {"setup": "npm run build", "presetup": "npm run install-addons", "install-addons": "node ./bin/install-addons.js", "start": "node demo/start", "build": "npm run tsc", "watch": "npm run tsc-watch", "tsc": "tsc -b ./tsconfig.all.json", "tsc-watch": "tsc -b -w ./tsconfig.all.json --preserveWatchOutput", "esbuild": "node bin/esbuild_all.mjs", "esbuild-watch": "node bin/esbuild_all.mjs --watch", "esbuild-package": "node bin/esbuild_all.mjs --prod", "esbuild-package-watch": "node bin/esbuild_all.mjs --prod --watch", "esbuild-package-headless-only": "node bin/esbuild.mjs --prod --headless", "esbuild-demo": "node bin/esbuild.mjs --demo-client", "esbuild-demo-watch": "node bin/esbuild.mjs --demo-client --watch", "test": "npm run test-unit", "posttest": "npm run lint", "lint": "eslint -c .eslintrc.json --max-warnings 0 --ext .ts src/ addons/", "lint-api": "eslint --no-eslintrc -c .eslintrc.json.typings --max-warnings 0 --no-ignore --ext .d.ts typings/", "test-unit": "node ./bin/test_unit.js", "test-unit-coverage": "node ./bin/test_unit.js --coverage", "test-unit-dev": "cross-env NODE_PATH='./out' mocha", "test-integration": "node ./bin/test_integration.js --workers=75%", "test-integration-chromium": "node ./bin/test_integration.js --workers=75% \"--project=ChromeStable\"", "test-integration-firefox": "node ./bin/test_integration.js --workers=75% \"--project=FirefoxStable\"", "test-integration-webkit": "node ./bin/test_integration.js --workers=75% \"--project=WebKit\"", "test-integration-debug": "node ./bin/test_integration.js  --workers=1 --headed --timeout=30000", "benchmark": "NODE_PATH=./out xterm-benchmark -r 5 -c test/benchmark/benchmark.json", "benchmark-baseline": "NODE_PATH=./out xterm-benchmark -r 5 -c test/benchmark/benchmark.json --baseline out-test/benchmark/*benchmark.js", "benchmark-eval": "NODE_PATH=./out xterm-benchmark -r 5 -c test/benchmark/benchmark.json --eval out-test/benchmark/*benchmark.js", "clean": "rm -rf lib out addons/*/lib addons/*/out", "vtfeatures": "node bin/extract_vtfeatures.js src/**/*.ts src/*.ts", "prepackage": "npm run build", "package": "webpack", "postpackage": "npm run esbuild-package", "prepackage-headless": "npm run esbuild-package-headless-only", "package-headless": "webpack --config ./webpack.config.headless.js", "postpackage-headless": "node ./bin/package_headless.js", "prepublishOnly": "npm run package"}, "devDependencies": {"@lunapaint/png-codec": "^0.2.0", "@playwright/test": "^1.37.1", "@stylistic/eslint-plugin": "^2.3.0", "@types/chai": "^4.2.22", "@types/debug": "^4.1.7", "@types/deep-equal": "^1.0.1", "@types/express": "4", "@types/express-ws": "^3.0.1", "@types/glob": "^7.2.0", "@types/jsdom": "^16.2.13", "@types/mocha": "^9.0.0", "@types/node": "^18.16.0", "@types/trusted-types": "^1.0.6", "@types/utf8": "^3.0.0", "@types/webpack": "^5.28.0", "@types/ws": "^8.2.0", "@typescript-eslint/eslint-plugin": "^6.2.00", "@typescript-eslint/parser": "^6.2.00", "chai": "^4.3.4", "cross-env": "^7.0.3", "deep-equal": "^2.0.5", "esbuild": "~0.25.2", "eslint": "^8.56.0", "eslint-plugin-jsdoc": "^46.9.1", "express": "^4.19.2", "express-ws": "^5.0.2", "glob": "^7.2.0", "jsdom": "^18.0.1", "mocha": "^10.1.0", "mustache": "^4.2.0", "node-pty": "1.1.0-beta19", "nyc": "^15.1.0", "source-map-loader": "^3.0.0", "source-map-support": "^0.5.20", "ts-loader": "^9.3.1", "typescript": "5.5", "utf8": "^3.0.0", "webpack": "^5.61.0", "webpack-cli": "^4.9.1", "ws": "^8.2.3", "xterm-benchmark": "^0.3.1"}, "commit": "8a16a551e09113d9a0eaa557dbc25e0977f627bf"}