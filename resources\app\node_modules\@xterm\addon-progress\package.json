{"name": "@xterm/addon-progress", "version": "0.2.0-beta.18", "author": {"name": "The xterm.js authors", "url": "https://xtermjs.org/"}, "main": "lib/addon-progress.js", "module": "lib/addon-progress.mjs", "types": "typings/addon-progress.d.ts", "repository": "https://github.com/xtermjs/xterm.js/tree/master/addons/addon-progress", "license": "MIT", "keywords": ["terminal", "xterm", "xterm.js"], "scripts": {"build": "../../node_modules/.bin/tsc -p .", "prepackage": "npm run build", "package": "../../node_modules/.bin/webpack", "prepublishOnly": "npm run package", "start": "node ../../demo/start"}, "peerDependencies": {"@xterm/xterm": "^5.6.0-beta.112"}, "commit": "8a16a551e09113d9a0eaa557dbc25e0977f627bf"}