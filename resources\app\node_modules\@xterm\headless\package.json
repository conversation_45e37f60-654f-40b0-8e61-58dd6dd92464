{"name": "@xterm/headless", "description": "A headless terminal component that runs in Node.js", "version": "5.6.0-beta.112", "main": "lib-headless/xterm-headless.js", "module": "lib/xterm.mjs", "types": "typings/xterm-headless.d.ts", "repository": "https://github.com/xtermjs/xterm.js", "license": "MIT", "keywords": ["cli", "command-line", "console", "pty", "shell", "ssh", "styles", "terminal-emulator", "terminal", "tty", "vt100", "webgl", "xterm"], "commit": "8a16a551e09113d9a0eaa557dbc25e0977f627bf"}