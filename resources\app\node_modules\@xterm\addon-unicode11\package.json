{"name": "@xterm/addon-unicode11", "version": "0.9.0-beta.112", "author": {"name": "The xterm.js authors", "url": "https://xtermjs.org/"}, "main": "lib/addon-unicode11.js", "module": "lib/addon-unicode11.mjs", "types": "typings/addon-unicode11.d.ts", "repository": "https://github.com/xtermjs/xterm.js/tree/master/addons/addon-unicode11", "license": "MIT", "keywords": ["terminal", "xterm", "xterm.js"], "scripts": {"build": "../../node_modules/.bin/tsc -p .", "prepackage": "npm run build", "package": "../../node_modules/.bin/webpack", "prepublishOnly": "npm run package", "start": "node ../../demo/start"}, "peerDependencies": {"@xterm/xterm": "^5.6.0-beta.112"}, "commit": "8a16a551e09113d9a0eaa557dbc25e0977f627bf"}