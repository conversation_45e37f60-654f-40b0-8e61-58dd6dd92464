/*!--------------------------------------------------------
 * Copyright (C) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------*/(async function(){performance.mark("code/didStartRenderer");const h=window.vscode,p=h.process;function y(o){performance.mark("code/willShowPartsSplash");let i=o.partsSplash;i&&(o.autoDetectHighContrast&&o.colorScheme.highContrast?(o.colorScheme.dark&&i.baseTheme!=="hc-black"||!o.colorScheme.dark&&i.baseTheme!=="hc-light")&&(i=void 0):o.autoDetectColorScheme&&(o.colorScheme.dark&&i.baseTheme!=="vs-dark"||!o.colorScheme.dark&&i.baseTheme!=="vs")&&(i=void 0)),i&&o.extensionDevelopmentPath&&(i.layoutInfo=void 0);let l,d,n;i?(l=i.baseTheme,d=i.colorInfo.editorBackground,n=i.colorInfo.foreground):o.autoDetectHighContrast&&o.colorScheme.highContrast?o.colorScheme.dark?(l="hc-black",d="#000000",n="#FFFFFF"):(l="hc-light",d="#FFFFFF",n="#000000"):o.autoDetectColorScheme&&(o.colorScheme.dark?(l="vs-dark",d="#1E1E1E",n="#CCCCCC"):(l="vs",d="#FFFFFF",n="#000000"));const c=document.createElement("style");if(c.className="initialShellColors",window.document.head.appendChild(c),c.textContent=`body {	background-color: ${d}; color: ${n}; margin: 0; padding: 0; }`,typeof i?.zoomLevel=="number"&&typeof h?.webFrame?.setZoomLevel=="function"&&h.webFrame.setZoomLevel(i.zoomLevel),i?.layoutInfo){const{layoutInfo:t,colorInfo:r}=i,a=document.createElement("div");if(a.id="monaco-parts-splash",a.className=l??"vs-dark",t.windowBorder&&r.windowBorder){const e=document.createElement("div");e.style.position="absolute",e.style.width="calc(100vw - 2px)",e.style.height="calc(100vh - 2px)",e.style.zIndex="1",e.style.border="1px solid var(--window-border-color)",e.style.setProperty("--window-border-color",r.windowBorder),t.windowBorderRadius&&(e.style.borderRadius=t.windowBorderRadius),a.appendChild(e)}if(t.auxiliaryBarWidth===Number.MAX_SAFE_INTEGER?t.auxiliaryBarWidth=window.innerWidth-t.activityBarWidth:t.auxiliaryBarWidth=Math.min(t.auxiliaryBarWidth,window.innerWidth-(t.activityBarWidth+t.editorPartMinWidth+t.sideBarWidth)),t.sideBarWidth=Math.min(t.sideBarWidth,window.innerWidth-(t.activityBarWidth+t.editorPartMinWidth+t.auxiliaryBarWidth)),t.titleBarHeight>0){const e=document.createElement("div");if(e.style.position="absolute",e.style.width="100%",e.style.height=`${t.titleBarHeight}px`,e.style.left="0",e.style.top="0",e.style.backgroundColor=`${r.titleBarBackground}`,e.style["-webkit-app-region"]="drag",a.appendChild(e),r.titleBarBorder){const s=document.createElement("div");s.style.position="absolute",s.style.width="100%",s.style.height="1px",s.style.left="0",s.style.bottom="0",s.style.borderBottom=`1px solid ${r.titleBarBorder}`,e.appendChild(s)}}if(t.activityBarWidth>0){const e=document.createElement("div");if(e.style.position="absolute",e.style.width=`${t.activityBarWidth}px`,e.style.height=`calc(100% - ${t.titleBarHeight+t.statusBarHeight}px)`,e.style.top=`${t.titleBarHeight}px`,t.sideBarSide==="left"?e.style.left="0":e.style.right="0",e.style.backgroundColor=`${r.activityBarBackground}`,a.appendChild(e),r.activityBarBorder){const s=document.createElement("div");s.style.position="absolute",s.style.width="1px",s.style.height="100%",s.style.top="0",t.sideBarSide==="left"?(s.style.right="0",s.style.borderRight=`1px solid ${r.activityBarBorder}`):(s.style.left="0",s.style.borderLeft=`1px solid ${r.activityBarBorder}`),e.appendChild(s)}}if(t.sideBarWidth>0){const e=document.createElement("div");if(e.style.position="absolute",e.style.width=`${t.sideBarWidth}px`,e.style.height=`calc(100% - ${t.titleBarHeight+t.statusBarHeight}px)`,e.style.top=`${t.titleBarHeight}px`,t.sideBarSide==="left"?e.style.left=`${t.activityBarWidth}px`:e.style.right=`${t.activityBarWidth}px`,e.style.backgroundColor=`${r.sideBarBackground}`,a.appendChild(e),r.sideBarBorder){const s=document.createElement("div");s.style.position="absolute",s.style.width="1px",s.style.height="100%",s.style.top="0",s.style.right="0",t.sideBarSide==="left"?s.style.borderRight=`1px solid ${r.sideBarBorder}`:(s.style.left="0",s.style.borderLeft=`1px solid ${r.sideBarBorder}`),e.appendChild(s)}}if(t.auxiliaryBarWidth>0){const e=document.createElement("div");if(e.style.position="absolute",e.style.width=`${t.auxiliaryBarWidth}px`,e.style.height=`calc(100% - ${t.titleBarHeight+t.statusBarHeight}px)`,e.style.top=`${t.titleBarHeight}px`,t.sideBarSide==="left"?e.style.right="0":e.style.left="0",e.style.backgroundColor=`${r.sideBarBackground}`,a.appendChild(e),r.sideBarBorder){const s=document.createElement("div");s.style.position="absolute",s.style.width="1px",s.style.height="100%",s.style.top="0",t.sideBarSide==="left"?(s.style.left="0",s.style.borderLeft=`1px solid ${r.sideBarBorder}`):(s.style.right="0",s.style.borderRight=`1px solid ${r.sideBarBorder}`),e.appendChild(s)}}if(t.statusBarHeight>0){const e=document.createElement("div");if(e.style.position="absolute",e.style.width="100%",e.style.height=`${t.statusBarHeight}px`,e.style.bottom="0",e.style.left="0",o.workspace&&r.statusBarBackground?e.style.backgroundColor=r.statusBarBackground:!o.workspace&&r.statusBarNoFolderBackground&&(e.style.backgroundColor=r.statusBarNoFolderBackground),a.appendChild(e),r.statusBarBorder){const s=document.createElement("div");s.style.position="absolute",s.style.width="100%",s.style.height="1px",s.style.top="0",s.style.borderTop=`1px solid ${r.statusBarBorder}`,e.appendChild(s)}}window.document.body.appendChild(a)}performance.mark("code/didShowPartsSplash")}async function u(o,i){const l=await m();i?.beforeImport?.(l);const{enableDeveloperKeybindings:d,removeDeveloperKeybindingsAfterLoad:n,developerDeveloperKeybindingsDisposable:c,forceDisableShowDevtoolsOnError:t}=f(l,i);b(l);const r=new URL(`${B(l.appRoot,{isWindows:p.platform==="win32",scheme:"vscode-file",fallbackAuthority:"vscode-app"})}/out/`);globalThis._VSCODE_FILE_ROOT=r.toString(),g(l,r);try{const a=await import(new URL(`${o}.js`,r).href);return c&&n&&c(),{result:a,configuration:l}}catch(a){throw w(a,d&&!t),a}}async function m(){const o=setTimeout(()=>{console.error("[resolve window config] Could not resolve window configuration within 10 seconds, but will continue to wait...")},1e4);performance.mark("code/willWaitForWindowConfig");const i=await h.context.resolveConfiguration();return performance.mark("code/didWaitForWindowConfig"),clearTimeout(o),i}function f(o,i){const{forceEnableDeveloperKeybindings:l,disallowReloadKeybinding:d,removeDeveloperKeybindingsAfterLoad:n,forceDisableShowDevtoolsOnError:c}=typeof i?.configureDeveloperSettings=="function"?i.configureDeveloperSettings(o):{forceEnableDeveloperKeybindings:!1,disallowReloadKeybinding:!1,removeDeveloperKeybindingsAfterLoad:!1,forceDisableShowDevtoolsOnError:!1},r=!!(!!p.env.VSCODE_DEV||l);let a;return r&&(a=v(d)),{enableDeveloperKeybindings:r,removeDeveloperKeybindingsAfterLoad:n,developerDeveloperKeybindingsDisposable:a,forceDisableShowDevtoolsOnError:c}}function v(o){const i=h.ipcRenderer,l=function(r){return[r.ctrlKey?"ctrl-":"",r.metaKey?"meta-":"",r.altKey?"alt-":"",r.shiftKey?"shift-":"",r.keyCode].join("")},d=p.platform==="darwin"?"meta-alt-73":"ctrl-shift-73",n="123",c=p.platform==="darwin"?"meta-82":"ctrl-82";let t=function(r){const a=l(r);a===d||a===n?i.send("vscode:toggleDevTools"):a===c&&!o&&i.send("vscode:reloadWindow")};return window.addEventListener("keydown",t),function(){t&&(window.removeEventListener("keydown",t),t=void 0)}}function b(o){globalThis._VSCODE_NLS_MESSAGES=o.nls.messages,globalThis._VSCODE_NLS_LANGUAGE=o.nls.language;let i=o.nls.language||"en";i==="zh-tw"?i="zh-Hant":i==="zh-cn"&&(i="zh-Hans"),window.document.documentElement.setAttribute("lang",i)}function w(o,i){i&&h.ipcRenderer.send("vscode:openDevTools"),console.error(`[uncaught exception]: ${o}`),o&&typeof o!="string"&&o.stack&&console.error(o.stack)}function B(o,i){let l=o.replace(/\\/g,"/");l.length>0&&l.charAt(0)!=="/"&&(l=`/${l}`);let d;return i.isWindows&&l.startsWith("//")?d=encodeURI(`${i.scheme||"file"}:${l}`):d=encodeURI(`${i.scheme||"file"}://${i.fallbackAuthority||""}${l}`),d.replace(/#/g,"%23")}function g(o,i){if(Array.isArray(o.cssModules)&&o.cssModules.length>0){performance.mark("code/willAddCssLoader"),globalThis._VSCODE_CSS_LOAD=function(t){const r=document.createElement("link");r.setAttribute("rel","stylesheet"),r.setAttribute("type","text/css"),r.setAttribute("href",t),window.document.head.appendChild(r)};const l={imports:{}};for(const t of o.cssModules){const r=new URL(t,i).href,a=`globalThis._VSCODE_CSS_LOAD('${r}');
`,e=new Blob([a],{type:"application/javascript"});l.imports[r]=URL.createObjectURL(e)}const d=window.trustedTypes?.createPolicy("vscode-bootstrapImportMap",{createScript(t){return t}}),n=JSON.stringify(l,void 0,2),c=document.createElement("script");c.type="importmap",c.setAttribute("nonce","0c6a828f1297"),c.textContent=d?.createScript(n)??n,window.document.head.appendChild(c),performance.mark("code/didAddCssLoader")}}const{result:S,configuration:D}=await u("vs/workbench/workbench.desktop.main",{configureDeveloperSettings:function(o){return{forceDisableShowDevtoolsOnError:typeof o.extensionTestsPath=="string"||o["enable-smoke-test-driver"]===!0,forceEnableDeveloperKeybindings:Array.isArray(o.extensionDevelopmentPath)&&o.extensionDevelopmentPath.length>0,removeDeveloperKeybindingsAfterLoad:!0}},beforeImport:function(o){y(o),Object.defineProperty(window,"vscodeWindowId",{get:()=>o.windowId}),window.requestIdleCallback(()=>{const i=document.createElement("canvas");i.getContext("2d")?.clearRect(0,0,i.width,i.height),i.remove()},{timeout:50}),performance.mark("code/willLoadWorkbenchMain")}});performance.mark("code/didLoadWorkbenchMain"),S.main(D)})();

//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/488a1f239235055e34e673291fb8d8c810886f81/core/vs/code/electron-browser/workbench/workbench.js.map
