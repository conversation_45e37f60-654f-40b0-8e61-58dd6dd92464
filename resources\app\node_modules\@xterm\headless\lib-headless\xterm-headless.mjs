/**
 * Copyright (c) 2014-2024 The xterm.js authors. All rights reserved.
 * @license MIT
 *
 * Copyright (c) 2012-2013, <PERSON> (MIT License)
 * @license MIT
 *
 * Originally forked from (with the author's permission):
 *   <PERSON><PERSON><PERSON>'s javascript vt100 for jslinux:
 *   http://bellard.org/jslinux/
 *   Copyright (c) 2011 F<PERSON>rice <PERSON>
 */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
var ti=Object.defineProperty;var ri=Object.getOwnPropertyDescriptor;var K=(n,t,e,r)=>{for(var i=r>1?void 0:r?ri(t,e):t,s=n.length-1,a;s>=0;s--)(a=n[s])&&(i=(r?a(t,e,i):a(i))||i);return r&&i&&ti(t,e,i),i},W=(n,t)=>(e,r)=>t(e,r,n);function j(n){return n>65535?(n-=65536,String.fromCharCode((n>>10)+55296)+String.fromCharCode(n%1024+56320)):String.fromCharCode(n)}function le(n,t=0,e=n.length){let r="";for(let i=t;i<e;++i){let s=n[i];s>65535?(s-=65536,r+=String.fromCharCode((s>>10)+55296)+String.fromCharCode(s%1024+56320)):r+=String.fromCharCode(s)}return r}var Ge=class{constructor(){this._interim=0}clear(){this._interim=0}decode(t,e){let r=t.length;if(!r)return 0;let i=0,s=0;if(this._interim){let a=t.charCodeAt(s++);56320<=a&&a<=57343?e[i++]=(this._interim-55296)*1024+a-56320+65536:(e[i++]=this._interim,e[i++]=a),this._interim=0}for(let a=s;a<r;++a){let l=t.charCodeAt(a);if(55296<=l&&l<=56319){if(++a>=r)return this._interim=l,i;let c=t.charCodeAt(a);56320<=c&&c<=57343?e[i++]=(l-55296)*1024+c-56320+65536:(e[i++]=l,e[i++]=c);continue}l!==65279&&(e[i++]=l)}return i}},Ke=class{constructor(){this.interim=new Uint8Array(3)}clear(){this.interim.fill(0)}decode(t,e){let r=t.length;if(!r)return 0;let i=0,s,a,l,c,u=0,_=0;if(this.interim[0]){let x=!1,v=this.interim[0];v&=(v&224)===192?31:(v&240)===224?15:7;let m=0,d;for(;(d=this.interim[++m]&63)&&m<4;)v<<=6,v|=d;let O=(this.interim[0]&224)===192?2:(this.interim[0]&240)===224?3:4,w=O-m;for(;_<w;){if(_>=r)return 0;if(d=t[_++],(d&192)!==128){_--,x=!0;break}else this.interim[m++]=d,v<<=6,v|=d&63}x||(O===2?v<128?_--:e[i++]=v:O===3?v<2048||v>=55296&&v<=57343||v===65279||(e[i++]=v):v<65536||v>1114111||(e[i++]=v)),this.interim.fill(0)}let o=r-4,h=_;for(;h<r;){for(;h<o&&!((s=t[h])&128)&&!((a=t[h+1])&128)&&!((l=t[h+2])&128)&&!((c=t[h+3])&128);)e[i++]=s,e[i++]=a,e[i++]=l,e[i++]=c,h+=4;if(s=t[h++],s<128)e[i++]=s;else if((s&224)===192){if(h>=r)return this.interim[0]=s,i;if(a=t[h++],(a&192)!==128){h--;continue}if(u=(s&31)<<6|a&63,u<128){h--;continue}e[i++]=u}else if((s&240)===224){if(h>=r)return this.interim[0]=s,i;if(a=t[h++],(a&192)!==128){h--;continue}if(h>=r)return this.interim[0]=s,this.interim[1]=a,i;if(l=t[h++],(l&192)!==128){h--;continue}if(u=(s&15)<<12|(a&63)<<6|l&63,u<2048||u>=55296&&u<=57343||u===65279)continue;e[i++]=u}else if((s&248)===240){if(h>=r)return this.interim[0]=s,i;if(a=t[h++],(a&192)!==128){h--;continue}if(h>=r)return this.interim[0]=s,this.interim[1]=a,i;if(l=t[h++],(l&192)!==128){h--;continue}if(h>=r)return this.interim[0]=s,this.interim[1]=a,this.interim[2]=l,i;if(c=t[h++],(c&192)!==128){h--;continue}if(u=(s&7)<<18|(a&63)<<12|(l&63)<<6|c&63,u<65536||u>1114111)continue;e[i++]=u}}return i}};var Ve="";var qe=" ";var ee=class n{constructor(){this.fg=0;this.bg=0;this.extended=new te}static toColorRGB(t){return[t>>>16&255,t>>>8&255,t&255]}static fromColorRGB(t){return(t[0]&255)<<16|(t[1]&255)<<8|t[2]&255}clone(){let t=new n;return t.fg=this.fg,t.bg=this.bg,t.extended=this.extended.clone(),t}isInverse(){return this.fg&67108864}isBold(){return this.fg&134217728}isUnderline(){return this.hasExtendedAttrs()&&this.extended.underlineStyle!==0?1:this.fg&268435456}isBlink(){return this.fg&536870912}isInvisible(){return this.fg&1073741824}isItalic(){return this.bg&67108864}isDim(){return this.bg&134217728}isStrikethrough(){return this.fg&2147483648}isProtected(){return this.bg&536870912}isOverline(){return this.bg&1073741824}getFgColorMode(){return this.fg&50331648}getBgColorMode(){return this.bg&50331648}isFgRGB(){return(this.fg&50331648)===50331648}isBgRGB(){return(this.bg&50331648)===50331648}isFgPalette(){return(this.fg&50331648)===16777216||(this.fg&50331648)===33554432}isBgPalette(){return(this.bg&50331648)===16777216||(this.bg&50331648)===33554432}isFgDefault(){return(this.fg&50331648)===0}isBgDefault(){return(this.bg&50331648)===0}isAttributeDefault(){return this.fg===0&&this.bg===0}getFgColor(){switch(this.fg&50331648){case 16777216:case 33554432:return this.fg&255;case 50331648:return this.fg&16777215;default:return-1}}getBgColor(){switch(this.bg&50331648){case 16777216:case 33554432:return this.bg&255;case 50331648:return this.bg&16777215;default:return-1}}hasExtendedAttrs(){return this.bg&268435456}updateExtended(){this.extended.isEmpty()?this.bg&=-268435457:this.bg|=268435456}getUnderlineColor(){if(this.bg&268435456&&~this.extended.underlineColor)switch(this.extended.underlineColor&50331648){case 16777216:case 33554432:return this.extended.underlineColor&255;case 50331648:return this.extended.underlineColor&16777215;default:return this.getFgColor()}return this.getFgColor()}getUnderlineColorMode(){return this.bg&268435456&&~this.extended.underlineColor?this.extended.underlineColor&50331648:this.getFgColorMode()}isUnderlineColorRGB(){return this.bg&268435456&&~this.extended.underlineColor?(this.extended.underlineColor&50331648)===50331648:this.isFgRGB()}isUnderlineColorPalette(){return this.bg&268435456&&~this.extended.underlineColor?(this.extended.underlineColor&50331648)===16777216||(this.extended.underlineColor&50331648)===33554432:this.isFgPalette()}isUnderlineColorDefault(){return this.bg&268435456&&~this.extended.underlineColor?(this.extended.underlineColor&50331648)===0:this.isFgDefault()}getUnderlineStyle(){return this.fg&268435456?this.bg&268435456?this.extended.underlineStyle:1:0}getUnderlineVariantOffset(){return this.extended.underlineVariantOffset}},te=class n{constructor(t=0,e=0){this._ext=0;this._urlId=0;this._ext=t,this._urlId=e}get ext(){return this._urlId?this._ext&-469762049|this.underlineStyle<<26:this._ext}set ext(t){this._ext=t}get underlineStyle(){return this._urlId?5:(this._ext&469762048)>>26}set underlineStyle(t){this._ext&=-469762049,this._ext|=t<<26&469762048}get underlineColor(){return this._ext&67108863}set underlineColor(t){this._ext&=-67108864,this._ext|=t&67108863}get urlId(){return this._urlId}set urlId(t){this._urlId=t}get underlineVariantOffset(){let t=(this._ext&3758096384)>>29;return t<0?t^4294967288:t}set underlineVariantOffset(t){this._ext&=536870911,this._ext|=t<<29&3758096384}clone(){return new n(this._ext,this._urlId)}isEmpty(){return this.underlineStyle===0&&this._urlId===0}};var G=class n extends ee{constructor(){super(...arguments);this.content=0;this.fg=0;this.bg=0;this.extended=new te;this.combinedData=""}static fromCharData(e){let r=new n;return r.setFromCharData(e),r}isCombined(){return this.content&2097152}getWidth(){return this.content>>22}getChars(){return this.content&2097152?this.combinedData:this.content&2097151?j(this.content&2097151):""}getCode(){return this.isCombined()?this.combinedData.charCodeAt(this.combinedData.length-1):this.content&2097151}setFromCharData(e){this.fg=e[0],this.bg=0;let r=!1;if(e[1].length>2)r=!0;else if(e[1].length===2){let i=e[1].charCodeAt(0);if(55296<=i&&i<=56319){let s=e[1].charCodeAt(1);56320<=s&&s<=57343?this.content=(i-55296)*1024+s-56320+65536|e[2]<<22:r=!0}else r=!0}else this.content=e[1].charCodeAt(0)|e[2]<<22;r&&(this.combinedData=e[1],this.content=2097152|e[2]<<22)}getAsCharData(){return[this.fg,this.getChars(),this.getWidth(),this.getCode()]}};var $e=class{constructor(t){this._line=t}get isWrapped(){return this._line.isWrapped}get length(){return this._line.length}getCell(t,e){if(!(t<0||t>=this._line.length))return e?(this._line.loadCell(t,e),e):this._line.loadCell(t,new G)}translateToString(t,e,r){return this._line.translateToString(t,e,r)}};var De=class{constructor(t,e){this._buffer=t;this.type=e}init(t){return this._buffer=t,this}get cursorY(){return this._buffer.y}get cursorX(){return this._buffer.x}get viewportY(){return this._buffer.ydisp}get baseY(){return this._buffer.ybase}get length(){return this._buffer.lines.length}getLine(t){let e=this._buffer.lines.get(t);if(e)return new $e(e)}getNullCell(){return new G}};var Pt=class{constructor(){this.listeners=[],this.unexpectedErrorHandler=function(t){setTimeout(()=>{throw t.stack?ze.isErrorNoTelemetry(t)?new ze(t.message+`

`+t.stack):new Error(t.message+`

`+t.stack):t},0)}}addListener(t){return this.listeners.push(t),()=>{this._removeListener(t)}}emit(t){this.listeners.forEach(e=>{e(t)})}_removeListener(t){this.listeners.splice(this.listeners.indexOf(t),1)}setUnexpectedErrorHandler(t){this.unexpectedErrorHandler=t}getUnexpectedErrorHandler(){return this.unexpectedErrorHandler}onUnexpectedError(t){this.unexpectedErrorHandler(t),this.emit(t)}onUnexpectedExternalError(t){this.unexpectedErrorHandler(t)}},ni=new Pt;function Xe(n){oi(n)||ni.onUnexpectedError(n)}var Bt="Canceled";function oi(n){return n instanceof je?!0:n instanceof Error&&n.name===Bt&&n.message===Bt}var je=class extends Error{constructor(){super(Bt),this.name=this.message}};var ze=class n extends Error{constructor(t){super(t),this.name="CodeExpectedError"}static fromError(t){if(t instanceof n)return t;let e=new n;return e.message=t.message,e.stack=t.stack,e}static isErrorNoTelemetry(t){return t.name==="CodeExpectedError"}};function ai(n,t,e=0,r=n.length){let i=e,s=r;for(;i<s;){let a=Math.floor((i+s)/2);t(n[a])?i=a+1:s=a}return i-1}var Ye=class Ye{constructor(t){this._array=t;this._findLastMonotonousLastIdx=0}findLastMonotonous(t){if(Ye.assertInvariants){if(this._prevFindLastPredicate){for(let r of this._array)if(this._prevFindLastPredicate(r)&&!t(r))throw new Error("MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.")}this._prevFindLastPredicate=t}let e=ai(this._array,t,this._findLastMonotonousLastIdx);return this._findLastMonotonousLastIdx=e+1,e===-1?void 0:this._array[e]}};Ye.assertInvariants=!1;var dr=Ye;var fr;(l=>{function n(c){return c<0}l.isLessThan=n;function t(c){return c<=0}l.isLessThanOrEqual=t;function e(c){return c>0}l.isGreaterThan=e;function r(c){return c===0}l.isNeitherLessOrGreaterThan=r,l.greaterThan=1,l.lessThan=-1,l.neitherLessOrGreaterThan=0})(fr||={});function pr(n,t){return(e,r)=>t(n(e),n(r))}var br=(n,t)=>n-t;var ue=class ue{constructor(t){this.iterate=t}forEach(t){this.iterate(e=>(t(e),!0))}toArray(){let t=[];return this.iterate(e=>(t.push(e),!0)),t}filter(t){return new ue(e=>this.iterate(r=>t(r)?e(r):!0))}map(t){return new ue(e=>this.iterate(r=>e(t(r))))}some(t){let e=!1;return this.iterate(r=>(e=t(r),!e)),e}findFirst(t){let e;return this.iterate(r=>t(r)?(e=r,!1):!0),e}findLast(t){let e;return this.iterate(r=>(t(r)&&(e=r),!0)),e}findLastMaxBy(t){let e,r=!0;return this.iterate(i=>((r||fr.isGreaterThan(t(i,e)))&&(r=!1,e=i),!0)),e}};ue.empty=new ue(t=>{});var hr=ue;function gr(n,t){let e=Object.create(null);for(let r of n){let i=t(r),s=e[i];s||(s=e[i]=[]),s.push(r)}return e}var mr,vr,_r=class{constructor(t,e){this.toKey=e;this._map=new Map;this[mr]="SetWithKey";for(let r of t)this.add(r)}get size(){return this._map.size}add(t){let e=this.toKey(t);return this._map.set(e,t),this}delete(t){return this._map.delete(this.toKey(t))}has(t){return this._map.has(this.toKey(t))}*entries(){for(let t of this._map.values())yield[t,t]}keys(){return this.values()}*values(){for(let t of this._map.values())yield t}clear(){this._map.clear()}forEach(t,e){this._map.forEach(r=>t.call(e,r,r,this))}[(vr=Symbol.iterator,mr=Symbol.toStringTag,vr)](){return this.values()}};var Qe=class{constructor(){this.map=new Map}add(t,e){let r=this.map.get(t);r||(r=new Set,this.map.set(t,r)),r.add(e)}delete(t,e){let r=this.map.get(t);r&&(r.delete(e),r.size===0&&this.map.delete(t))}forEach(t,e){let r=this.map.get(t);r&&r.forEach(e)}get(t){let e=this.map.get(t);return e||new Set}};function Lt(n,t){let e=this,r=!1,i;return function(){if(r)return i;if(r=!0,t)try{i=n.apply(e,arguments)}finally{t()}else i=n.apply(e,arguments);return i}}var Mt;(L=>{function n(g){return g&&typeof g=="object"&&typeof g[Symbol.iterator]=="function"}L.is=n;let t=Object.freeze([]);function e(){return t}L.empty=e;function*r(g){yield g}L.single=r;function i(g){return n(g)?g:r(g)}L.wrap=i;function s(g){return g||t}L.from=s;function*a(g){for(let D=g.length-1;D>=0;D--)yield g[D]}L.reverse=a;function l(g){return!g||g[Symbol.iterator]().next().done===!0}L.isEmpty=l;function c(g){return g[Symbol.iterator]().next().value}L.first=c;function u(g,D){let E=0;for(let U of g)if(D(U,E++))return!0;return!1}L.some=u;function _(g,D){for(let E of g)if(D(E))return E}L.find=_;function*o(g,D){for(let E of g)D(E)&&(yield E)}L.filter=o;function*h(g,D){let E=0;for(let U of g)yield D(U,E++)}L.map=h;function*x(g,D){let E=0;for(let U of g)yield*D(U,E++)}L.flatMap=x;function*v(...g){for(let D of g)yield*D}L.concat=v;function m(g,D,E){let U=E;for(let se of g)U=D(U,se);return U}L.reduce=m;function*d(g,D,E=g.length){for(D<0&&(D+=g.length),E<0?E+=g.length:E>g.length&&(E=g.length);D<E;D++)yield g[D]}L.slice=d;function O(g,D=Number.POSITIVE_INFINITY){let E=[];if(D===0)return[E,g];let U=g[Symbol.iterator]();for(let se=0;se<D;se++){let ye=U.next();if(ye.done)return[E,L.empty()];E.push(ye.value)}return[E,{[Symbol.iterator](){return U}}]}L.consume=O;async function w(g){let D=[];for await(let E of g)D.push(E);return Promise.resolve(D)}L.asyncToArray=w})(Mt||={});var li=!1,de=null,Ze=class Ze{constructor(){this.livingDisposables=new Map}getDisposableData(t){let e=this.livingDisposables.get(t);return e||(e={parent:null,source:null,isSingleton:!1,value:t,idx:Ze.idx++},this.livingDisposables.set(t,e)),e}trackDisposable(t){let e=this.getDisposableData(t);e.source||(e.source=new Error().stack)}setParent(t,e){let r=this.getDisposableData(t);r.parent=e}markAsDisposed(t){this.livingDisposables.delete(t)}markAsSingleton(t){this.getDisposableData(t).isSingleton=!0}getRootParent(t,e){let r=e.get(t);if(r)return r;let i=t.parent?this.getRootParent(this.getDisposableData(t.parent),e):t;return e.set(t,i),i}getTrackedDisposables(){let t=new Map;return[...this.livingDisposables.entries()].filter(([,r])=>r.source!==null&&!this.getRootParent(r,t).isSingleton).flatMap(([r])=>r)}computeLeakingDisposables(t=10,e){let r;if(e)r=e;else{let c=new Map,u=[...this.livingDisposables.values()].filter(o=>o.source!==null&&!this.getRootParent(o,c).isSingleton);if(u.length===0)return;let _=new Set(u.map(o=>o.value));if(r=u.filter(o=>!(o.parent&&_.has(o.parent))),r.length===0)throw new Error("There are cyclic diposable chains!")}if(!r)return;function i(c){function u(o,h){for(;o.length>0&&h.some(x=>typeof x=="string"?x===o[0]:o[0].match(x));)o.shift()}let _=c.source.split(`
`).map(o=>o.trim().replace("at ","")).filter(o=>o!=="");return u(_,["Error",/^trackDisposable \(.*\)$/,/^DisposableTracker.trackDisposable \(.*\)$/]),_.reverse()}let s=new Qe;for(let c of r){let u=i(c);for(let _=0;_<=u.length;_++)s.add(u.slice(0,_).join(`
`),c)}r.sort(pr(c=>c.idx,br));let a="",l=0;for(let c of r.slice(0,t)){l++;let u=i(c),_=[];for(let o=0;o<u.length;o++){let h=u[o];h=`(shared with ${s.get(u.slice(0,o+1).join(`
`)).size}/${r.length} leaks) at ${h}`;let v=s.get(u.slice(0,o).join(`
`)),m=gr([...v].map(d=>i(d)[o]),d=>d);delete m[u[o]];for(let[d,O]of Object.entries(m))_.unshift(`    - stacktraces of ${O.length} other leaks continue with ${d}`);_.unshift(h)}a+=`


==================== Leaking disposable ${l}/${r.length}: ${c.value.constructor.name} ====================
${_.join(`
`)}
============================================================

`}return r.length>t&&(a+=`


... and ${r.length-t} more leaking disposables

`),{leaks:r,details:a}}};Ze.idx=0;var Ir=Ze;function ci(n){de=n}if(li){let n="__is_disposable_tracked__";ci(new class{trackDisposable(t){let e=new Error("Potentially leaked disposable").stack;setTimeout(()=>{t[n]||console.log(e)},3e3)}setParent(t,e){if(t&&t!==A.None)try{t[n]=!0}catch{}}markAsDisposed(t){if(t&&t!==A.None)try{t[n]=!0}catch{}}markAsSingleton(t){}})}function tt(n){return de?.trackDisposable(n),n}function rt(n){de?.markAsDisposed(n)}function Ae(n,t){de?.setParent(n,t)}function ui(n,t){if(de)for(let e of n)de.setParent(e,t)}function it(n){if(Mt.is(n)){let t=[];for(let e of n)if(e)try{e.dispose()}catch(r){t.push(r)}if(t.length===1)throw t[0];if(t.length>1)throw new AggregateError(t,"Encountered errors while disposing of store");return Array.isArray(n)?[]:n}else if(n)return n.dispose(),n}function xr(...n){let t=Q(()=>it(n));return ui(n,t),t}function Q(n){let t=tt({dispose:Lt(()=>{rt(t),n()})});return t}var et=class et{constructor(){this._toDispose=new Set;this._isDisposed=!1;tt(this)}dispose(){this._isDisposed||(rt(this),this._isDisposed=!0,this.clear())}get isDisposed(){return this._isDisposed}clear(){if(this._toDispose.size!==0)try{it(this._toDispose)}finally{this._toDispose.clear()}}add(t){if(!t)return t;if(t===this)throw new Error("Cannot register a disposable on itself!");return Ae(t,this),this._isDisposed?et.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this._toDispose.add(t),t}delete(t){if(t){if(t===this)throw new Error("Cannot dispose a disposable on itself!");this._toDispose.delete(t),t.dispose()}}deleteAndLeak(t){t&&this._toDispose.has(t)&&(this._toDispose.delete(t),Ae(t,null))}};et.DISABLE_DISPOSED_WARNING=!1;var he=et,A=class{constructor(){this._store=new he;tt(this),Ae(this._store,this)}dispose(){rt(this),this._store.dispose()}_register(t){if(t===this)throw new Error("Cannot register a disposable on itself!");return this._store.add(t)}};A.None=Object.freeze({dispose(){}});var Je=class{constructor(){this._isDisposed=!1;tt(this)}get value(){return this._isDisposed?void 0:this._value}set value(t){this._isDisposed||t===this._value||(this._value?.dispose(),t&&Ae(t,this),this._value=t)}clear(){this.value=void 0}dispose(){this._isDisposed=!0,rt(this),this._value?.dispose(),this._value=void 0}clearAndLeak(){let t=this._value;return this._value=void 0,t&&Ae(t,null),t}};var fe=class fe{constructor(t){this.element=t,this.next=fe.Undefined,this.prev=fe.Undefined}};fe.Undefined=new fe(void 0);var Tr=fe;var di=globalThis.performance&&typeof globalThis.performance.now=="function",st=class n{static create(t){return new n(t)}constructor(t){this._now=di&&t===!1?Date.now:globalThis.performance.now.bind(globalThis.performance),this._startTime=this._now(),this._stopTime=-1}stop(){this._stopTime=this._now()}reset(){this._startTime=this._now(),this._stopTime=-1}elapsed(){return this._stopTime!==-1?this._stopTime-this._startTime:this._now()-this._startTime}};var hi=!1,Sr=!1,fi=!1,V;(oe=>{oe.None=()=>A.None;function t(T){if(fi){let{onDidAddListener:b}=T,f=Re.create(),I=0;T.onDidAddListener=()=>{++I===2&&(console.warn("snapshotted emitter LIKELY used public and SHOULD HAVE BEEN created with DisposableStore. snapshotted here"),f.print()),b?.()}}}function e(T,b){return h(T,()=>{},0,void 0,!0,void 0,b)}oe.defer=e;function r(T){return(b,f=null,I)=>{let p=!1,y;return y=T(R=>{if(!p)return y?y.dispose():p=!0,b.call(f,R)},null,I),p&&y.dispose(),y}}oe.once=r;function i(T,b,f){return _((I,p=null,y)=>T(R=>I.call(p,b(R)),null,y),f)}oe.map=i;function s(T,b,f){return _((I,p=null,y)=>T(R=>{b(R),I.call(p,R)},null,y),f)}oe.forEach=s;function a(T,b,f){return _((I,p=null,y)=>T(R=>b(R)&&I.call(p,R),null,y),f)}oe.filter=a;function l(T){return T}oe.signal=l;function c(...T){return(b,f=null,I)=>{let p=xr(...T.map(y=>y(R=>b.call(f,R))));return o(p,I)}}oe.any=c;function u(T,b,f,I){let p=f;return i(T,y=>(p=b(p,y),p),I)}oe.reduce=u;function _(T,b){let f,I={onWillAddFirstListener(){f=T(p.fire,p)},onDidRemoveLastListener(){f?.dispose()}};b||t(I);let p=new S(I);return b?.add(p),p.event}function o(T,b){return b instanceof Array?b.push(T):b&&b.add(T),T}function h(T,b,f=100,I=!1,p=!1,y,R){let N,H,ae,Ue=0,Ce,ar={leakWarningThreshold:y,onWillAddFirstListener(){N=T(Zr=>{Ue++,H=b(H,Zr),I&&!ae&&(We.fire(H),H=void 0),Ce=()=>{let ei=H;H=void 0,ae=void 0,(!I||Ue>1)&&We.fire(ei),Ue=0},typeof f=="number"?(clearTimeout(ae),ae=setTimeout(Ce,f)):ae===void 0&&(ae=0,queueMicrotask(Ce))})},onWillRemoveListener(){p&&Ue>0&&Ce?.()},onDidRemoveLastListener(){Ce=void 0,N.dispose()}};R||t(ar);let We=new S(ar);return R?.add(We),We.event}oe.debounce=h;function x(T,b=0,f){return oe.debounce(T,(I,p)=>I?(I.push(p),I):[p],b,void 0,!0,void 0,f)}oe.accumulate=x;function v(T,b=(I,p)=>I===p,f){let I=!0,p;return a(T,y=>{let R=I||!b(y,p);return I=!1,p=y,R},f)}oe.latch=v;function m(T,b,f){return[oe.filter(T,b,f),oe.filter(T,I=>!b(I),f)]}oe.split=m;function d(T,b=!1,f=[],I){let p=f.slice(),y=T(H=>{p?p.push(H):N.fire(H)});I&&I.add(y);let R=()=>{p?.forEach(H=>N.fire(H)),p=null},N=new S({onWillAddFirstListener(){y||(y=T(H=>N.fire(H)),I&&I.add(y))},onDidAddFirstListener(){p&&(b?setTimeout(R):R())},onDidRemoveLastListener(){y&&y.dispose(),y=null}});return I&&I.add(N),N.event}oe.buffer=d;function O(T,b){return(I,p,y)=>{let R=b(new L);return T(function(N){let H=R.evaluate(N);H!==w&&I.call(p,H)},void 0,y)}}oe.chain=O;let w=Symbol("HaltChainable");class L{constructor(){this.steps=[]}map(b){return this.steps.push(b),this}forEach(b){return this.steps.push(f=>(b(f),f)),this}filter(b){return this.steps.push(f=>b(f)?f:w),this}reduce(b,f){let I=f;return this.steps.push(p=>(I=b(I,p),I)),this}latch(b=(f,I)=>f===I){let f=!0,I;return this.steps.push(p=>{let y=f||!b(p,I);return f=!1,I=p,y?p:w}),this}evaluate(b){for(let f of this.steps)if(b=f(b),b===w)break;return b}}function g(T,b,f=I=>I){let I=(...N)=>R.fire(f(...N)),p=()=>T.on(b,I),y=()=>T.removeListener(b,I),R=new S({onWillAddFirstListener:p,onDidRemoveLastListener:y});return R.event}oe.fromNodeEventEmitter=g;function D(T,b,f=I=>I){let I=(...N)=>R.fire(f(...N)),p=()=>T.addEventListener(b,I),y=()=>T.removeEventListener(b,I),R=new S({onWillAddFirstListener:p,onDidRemoveLastListener:y});return R.event}oe.fromDOMEventEmitter=D;function E(T){return new Promise(b=>r(T)(b))}oe.toPromise=E;function U(T){let b=new S;return T.then(f=>{b.fire(f)},()=>{b.fire(void 0)}).finally(()=>{b.dispose()}),b.event}oe.fromPromise=U;function se(T,b){return T(f=>b.fire(f))}oe.forward=se;function ye(T,b,f){return b(f),T(I=>b(I))}oe.runAndSubscribe=ye;class wt{constructor(b,f){this._observable=b;this._counter=0;this._hasChanged=!1;let I={onWillAddFirstListener:()=>{b.addObserver(this)},onDidRemoveLastListener:()=>{b.removeObserver(this)}};f||t(I),this.emitter=new S(I),f&&f.add(this.emitter)}beginUpdate(b){this._counter++}handlePossibleChange(b){}handleChange(b,f){this._hasChanged=!0}endUpdate(b){this._counter--,this._counter===0&&(this._observable.reportChanges(),this._hasChanged&&(this._hasChanged=!1,this.emitter.fire(this._observable.get())))}}function nr(T,b){return new wt(T,b).emitter.event}oe.fromObservable=nr;function or(T){return(b,f,I)=>{let p=0,y=!1,R={beginUpdate(){p++},endUpdate(){p--,p===0&&(T.reportChanges(),y&&(y=!1,b.call(f)))},handlePossibleChange(){},handleChange(){y=!0}};T.addObserver(R),T.reportChanges();let N={dispose(){T.removeObserver(R)}};return I instanceof he?I.add(N):Array.isArray(I)&&I.push(N),N}}oe.fromObservableLight=or})(V||={});var pe=class pe{constructor(t){this.listenerCount=0;this.invocationCount=0;this.elapsedOverall=0;this.durations=[];this.name=`${t}_${pe._idPool++}`,pe.all.add(this)}start(t){this._stopWatch=new st,this.listenerCount=t}stop(){if(this._stopWatch){let t=this._stopWatch.elapsed();this.durations.push(t),this.elapsedOverall+=t,this.invocationCount+=1,this._stopWatch=void 0}}};pe.all=new Set,pe._idPool=0;var Nt=pe,Er=-1;var ot=class ot{constructor(t,e,r=(ot._idPool++).toString(16).padStart(3,"0")){this._errorHandler=t;this.threshold=e;this.name=r;this._warnCountdown=0}dispose(){this._stacks?.clear()}check(t,e){let r=this.threshold;if(r<=0||e<r)return;this._stacks||(this._stacks=new Map);let i=this._stacks.get(t.value)||0;if(this._stacks.set(t.value,i+1),this._warnCountdown-=1,this._warnCountdown<=0){this._warnCountdown=r*.5;let[s,a]=this.getMostFrequentStack(),l=`[${this.name}] potential listener LEAK detected, having ${e} listeners already. MOST frequent listener (${a}):`;console.warn(l),console.warn(s);let c=new Ft(l,s);this._errorHandler(c)}return()=>{let s=this._stacks.get(t.value)||0;this._stacks.set(t.value,s-1)}}getMostFrequentStack(){if(!this._stacks)return;let t,e=0;for(let[r,i]of this._stacks)(!t||e<i)&&(t=[r,i],e=i);return t}};ot._idPool=1;var Ht=ot,Re=class n{constructor(t){this.value=t}static create(){let t=new Error;return new n(t.stack??"")}print(){console.warn(this.value.split(`
`).slice(2).join(`
`))}},Ft=class extends Error{constructor(t,e){super(t),this.name="ListenerLeakError",this.stack=e}},Ut=class extends Error{constructor(t,e){super(t),this.name="ListenerRefusalError",this.stack=e}},pi=0,be=class{constructor(t){this.value=t;this.id=pi++}},bi=2,_i=(n,t)=>{if(n instanceof be)t(n);else for(let e=0;e<n.length;e++){let r=n[e];r&&t(r)}},nt;if(hi){let n=[];setInterval(()=>{n.length!==0&&(console.warn("[LEAKING LISTENERS] GC'ed these listeners that were NOT yet disposed:"),console.warn(n.join(`
`)),n.length=0)},3e3),nt=new FinalizationRegistry(t=>{typeof t=="string"&&n.push(t)})}var S=class{constructor(t){this._size=0;this._options=t,this._leakageMon=Er>0||this._options?.leakWarningThreshold?new Ht(t?.onListenerError??Xe,this._options?.leakWarningThreshold??Er):void 0,this._perfMon=this._options?._profName?new Nt(this._options._profName):void 0,this._deliveryQueue=this._options?.deliveryQueue}dispose(){if(!this._disposed){if(this._disposed=!0,this._deliveryQueue?.current===this&&this._deliveryQueue.reset(),this._listeners){if(Sr){let t=this._listeners;queueMicrotask(()=>{_i(t,e=>e.stack?.print())})}this._listeners=void 0,this._size=0}this._options?.onDidRemoveLastListener?.(),this._leakageMon?.dispose()}}get event(){return this._event??=(t,e,r)=>{if(this._leakageMon&&this._size>this._leakageMon.threshold**2){let c=`[${this._leakageMon.name}] REFUSES to accept new listeners because it exceeded its threshold by far (${this._size} vs ${this._leakageMon.threshold})`;console.warn(c);let u=this._leakageMon.getMostFrequentStack()??["UNKNOWN stack",-1],_=new Ut(`${c}. HINT: Stack shows most frequent listener (${u[1]}-times)`,u[0]);return(this._options?.onListenerError||Xe)(_),A.None}if(this._disposed)return A.None;e&&(t=t.bind(e));let i=new be(t),s,a;this._leakageMon&&this._size>=Math.ceil(this._leakageMon.threshold*.2)&&(i.stack=Re.create(),s=this._leakageMon.check(i.stack,this._size+1)),Sr&&(i.stack=a??Re.create()),this._listeners?this._listeners instanceof be?(this._deliveryQueue??=new Wt,this._listeners=[this._listeners,i]):this._listeners.push(i):(this._options?.onWillAddFirstListener?.(this),this._listeners=i,this._options?.onDidAddFirstListener?.(this)),this._size++;let l=Q(()=>{nt?.unregister(l),s?.(),this._removeListener(i)});if(r instanceof he?r.add(l):Array.isArray(r)&&r.push(l),nt){let c=new Error().stack.split(`
`).slice(2,3).join(`
`).trim(),u=/(file:|vscode-file:\/\/vscode-app)?(\/[^:]*:\d+:\d+)/.exec(c);nt.register(l,u?.[2]??c,l)}return l},this._event}_removeListener(t){if(this._options?.onWillRemoveListener?.(this),!this._listeners)return;if(this._size===1){this._listeners=void 0,this._options?.onDidRemoveLastListener?.(this),this._size=0;return}let e=this._listeners,r=e.indexOf(t);if(r===-1)throw console.log("disposed?",this._disposed),console.log("size?",this._size),console.log("arr?",JSON.stringify(this._listeners)),new Error("Attempted to dispose unknown listener");this._size--,e[r]=void 0;let i=this._deliveryQueue.current===this;if(this._size*bi<=e.length){let s=0;for(let a=0;a<e.length;a++)e[a]?e[s++]=e[a]:i&&(this._deliveryQueue.end--,s<this._deliveryQueue.i&&this._deliveryQueue.i--);e.length=s}}_deliver(t,e){if(!t)return;let r=this._options?.onListenerError||Xe;if(!r){t.value(e);return}try{t.value(e)}catch(i){r(i)}}_deliverQueue(t){let e=t.current._listeners;for(;t.i<t.end;)this._deliver(e[t.i++],t.value);t.reset()}fire(t){if(this._deliveryQueue?.current&&(this._deliverQueue(this._deliveryQueue),this._perfMon?.stop()),this._perfMon?.start(this._size),this._listeners)if(this._listeners instanceof be)this._deliver(this._listeners,t);else{let e=this._deliveryQueue;e.enqueue(this,t,this._listeners.length),this._deliverQueue(e)}this._perfMon?.stop()}hasListeners(){return this._size>0}};var Wt=class{constructor(){this.i=-1;this.end=0}enqueue(t,e,r){this.i=0,this.end=r,this.current=t,this.value=e}reset(){this.i=this.end,this.current=void 0,this.value=void 0}};var at=class extends A{constructor(e){super();this._core=e;this._onBufferChange=this._register(new S);this.onBufferChange=this._onBufferChange.event;this._normal=new De(this._core.buffers.normal,"normal"),this._alternate=new De(this._core.buffers.alt,"alternate"),this._core.buffers.onBufferActivate(()=>this._onBufferChange.fire(this.active))}get active(){if(this._core.buffers.active===this._core.buffers.normal)return this.normal;if(this._core.buffers.active===this._core.buffers.alt)return this.alternate;throw new Error("Active buffer is neither normal nor alternate")}get normal(){return this._normal.init(this._core.buffers.normal)}get alternate(){return this._alternate.init(this._core.buffers.alt)}};var lt=class{constructor(t){this._core=t}registerCsiHandler(t,e){return this._core.registerCsiHandler(t,r=>e(r.toArray()))}addCsiHandler(t,e){return this.registerCsiHandler(t,e)}registerDcsHandler(t,e){return this._core.registerDcsHandler(t,(r,i)=>e(r,i.toArray()))}addDcsHandler(t,e){return this.registerDcsHandler(t,e)}registerEscHandler(t,e){return this._core.registerEscHandler(t,e)}addEscHandler(t,e){return this.registerEscHandler(t,e)}registerOscHandler(t,e){return this._core.registerOscHandler(t,e)}addOscHandler(t,e){return this.registerOscHandler(t,e)}};var ct=class{constructor(t){this._core=t}register(t){this._core.unicodeService.register(t)}get versions(){return this._core.unicodeService.versions}get activeVersion(){return this._core.unicodeService.activeVersion}set activeVersion(t){this._core.unicodeService.activeVersion=t}};var C=3;var P=Object.freeze(new ee),ut=0,Gt=2,J=class n{constructor(t,e,r=!1){this.isWrapped=r;this._combined={};this._extendedAttrs={};this._data=new Uint32Array(t*C);let i=e||G.fromCharData([0,Ve,1,0]);for(let s=0;s<t;++s)this.setCell(s,i);this.length=t}get(t){let e=this._data[t*C+0],r=e&2097151;return[this._data[t*C+1],e&2097152?this._combined[t]:r?j(r):"",e>>22,e&2097152?this._combined[t].charCodeAt(this._combined[t].length-1):r]}set(t,e){this._data[t*C+1]=e[0],e[1].length>1?(this._combined[t]=e[1],this._data[t*C+0]=t|2097152|e[2]<<22):this._data[t*C+0]=e[1].charCodeAt(0)|e[2]<<22}getWidth(t){return this._data[t*C+0]>>22}hasWidth(t){return this._data[t*C+0]&12582912}getFg(t){return this._data[t*C+1]}getBg(t){return this._data[t*C+2]}hasContent(t){return this._data[t*C+0]&4194303}getCodePoint(t){let e=this._data[t*C+0];return e&2097152?this._combined[t].charCodeAt(this._combined[t].length-1):e&2097151}isCombined(t){return this._data[t*C+0]&2097152}getString(t){let e=this._data[t*C+0];return e&2097152?this._combined[t]:e&2097151?j(e&2097151):""}isProtected(t){return this._data[t*C+2]&536870912}loadCell(t,e){return ut=t*C,e.content=this._data[ut+0],e.fg=this._data[ut+1],e.bg=this._data[ut+2],e.content&2097152&&(e.combinedData=this._combined[t]),e.bg&268435456&&(e.extended=this._extendedAttrs[t]),e}setCell(t,e){e.content&2097152&&(this._combined[t]=e.combinedData),e.bg&268435456&&(this._extendedAttrs[t]=e.extended),this._data[t*C+0]=e.content,this._data[t*C+1]=e.fg,this._data[t*C+2]=e.bg}setCellFromCodepoint(t,e,r,i){i.bg&268435456&&(this._extendedAttrs[t]=i.extended),this._data[t*C+0]=e|r<<22,this._data[t*C+1]=i.fg,this._data[t*C+2]=i.bg}addCodepointToCell(t,e,r){let i=this._data[t*C+0];i&2097152?this._combined[t]+=j(e):i&2097151?(this._combined[t]=j(i&2097151)+j(e),i&=-2097152,i|=2097152):i=e|1<<22,r&&(i&=-12582913,i|=r<<22),this._data[t*C+0]=i}insertCells(t,e,r){if(t%=this.length,t&&this.getWidth(t-1)===2&&this.setCellFromCodepoint(t-1,0,1,r),e<this.length-t){let i=new G;for(let s=this.length-t-e-1;s>=0;--s)this.setCell(t+e+s,this.loadCell(t+s,i));for(let s=0;s<e;++s)this.setCell(t+s,r)}else for(let i=t;i<this.length;++i)this.setCell(i,r);this.getWidth(this.length-1)===2&&this.setCellFromCodepoint(this.length-1,0,1,r)}deleteCells(t,e,r){if(t%=this.length,e<this.length-t){let i=new G;for(let s=0;s<this.length-t-e;++s)this.setCell(t+s,this.loadCell(t+e+s,i));for(let s=this.length-e;s<this.length;++s)this.setCell(s,r)}else for(let i=t;i<this.length;++i)this.setCell(i,r);t&&this.getWidth(t-1)===2&&this.setCellFromCodepoint(t-1,0,1,r),this.getWidth(t)===0&&!this.hasContent(t)&&this.setCellFromCodepoint(t,0,1,r)}replaceCells(t,e,r,i=!1){if(i){for(t&&this.getWidth(t-1)===2&&!this.isProtected(t-1)&&this.setCellFromCodepoint(t-1,0,1,r),e<this.length&&this.getWidth(e-1)===2&&!this.isProtected(e)&&this.setCellFromCodepoint(e,0,1,r);t<e&&t<this.length;)this.isProtected(t)||this.setCell(t,r),t++;return}for(t&&this.getWidth(t-1)===2&&this.setCellFromCodepoint(t-1,0,1,r),e<this.length&&this.getWidth(e-1)===2&&this.setCellFromCodepoint(e,0,1,r);t<e&&t<this.length;)this.setCell(t++,r)}resize(t,e){if(t===this.length)return this._data.length*4*Gt<this._data.buffer.byteLength;let r=t*C;if(t>this.length){if(this._data.buffer.byteLength>=r*4)this._data=new Uint32Array(this._data.buffer,0,r);else{let i=new Uint32Array(r);i.set(this._data),this._data=i}for(let i=this.length;i<t;++i)this.setCell(i,e)}else{this._data=this._data.subarray(0,r);let i=Object.keys(this._combined);for(let a=0;a<i.length;a++){let l=parseInt(i[a],10);l>=t&&delete this._combined[l]}let s=Object.keys(this._extendedAttrs);for(let a=0;a<s.length;a++){let l=parseInt(s[a],10);l>=t&&delete this._extendedAttrs[l]}}return this.length=t,r*4*Gt<this._data.buffer.byteLength}cleanupMemory(){if(this._data.length*4*Gt<this._data.buffer.byteLength){let t=new Uint32Array(this._data.length);return t.set(this._data),this._data=t,1}return 0}fill(t,e=!1){if(e){for(let r=0;r<this.length;++r)this.isProtected(r)||this.setCell(r,t);return}this._combined={},this._extendedAttrs={};for(let r=0;r<this.length;++r)this.setCell(r,t)}copyFrom(t){this.length!==t.length?this._data=new Uint32Array(t._data):this._data.set(t._data),this.length=t.length,this._combined={};for(let e in t._combined)this._combined[e]=t._combined[e];this._extendedAttrs={};for(let e in t._extendedAttrs)this._extendedAttrs[e]=t._extendedAttrs[e];this.isWrapped=t.isWrapped}clone(){let t=new n(0);t._data=new Uint32Array(this._data),t.length=this.length;for(let e in this._combined)t._combined[e]=this._combined[e];for(let e in this._extendedAttrs)t._extendedAttrs[e]=this._extendedAttrs[e];return t.isWrapped=this.isWrapped,t}getTrimmedLength(){for(let t=this.length-1;t>=0;--t)if(this._data[t*C+0]&4194303)return t+(this._data[t*C+0]>>22);return 0}getNoBgTrimmedLength(){for(let t=this.length-1;t>=0;--t)if(this._data[t*C+0]&4194303||this._data[t*C+2]&50331648)return t+(this._data[t*C+0]>>22);return 0}copyCellsFrom(t,e,r,i,s){let a=t._data;if(s)for(let c=i-1;c>=0;c--){for(let u=0;u<C;u++)this._data[(r+c)*C+u]=a[(e+c)*C+u];a[(e+c)*C+2]&268435456&&(this._extendedAttrs[r+c]=t._extendedAttrs[e+c])}else for(let c=0;c<i;c++){for(let u=0;u<C;u++)this._data[(r+c)*C+u]=a[(e+c)*C+u];a[(e+c)*C+2]&268435456&&(this._extendedAttrs[r+c]=t._extendedAttrs[e+c])}let l=Object.keys(t._combined);for(let c=0;c<l.length;c++){let u=parseInt(l[c],10);u>=e&&(this._combined[u-e+r]=t._combined[u])}}translateToString(t,e,r,i){e=e??0,r=r??this.length,t&&(r=Math.min(r,this.getTrimmedLength())),i&&(i.length=0);let s="";for(;e<r;){let a=this._data[e*C+0],l=a&2097151,c=a&2097152?this._combined[e]:l?j(l):qe;if(s+=c,i)for(let u=0;u<c.length;++u)i.push(e);e+=a>>22||1}return i&&i.push(e),s}};var yr="di$target",Vt="di$dependencies",Kt=new Map;function Cr(n){return n[Vt]||[]}function z(n){if(Kt.has(n))return Kt.get(n);let t=function(e,r,i){if(arguments.length!==3)throw new Error("@IServiceName-decorator can only be used to decorate a parameter");mi(t,e,i)};return t._id=n,Kt.set(n,t),t}function mi(n,t,e){t[yr]===t?t[Vt].push({id:n,index:e}):(t[Vt]=[{id:n,index:e}],t[yr]=t)}var X=z("BufferService"),Dr=z("CoreMouseService"),dt=z("CoreService"),Ar=z("CharsetService"),Rr=z("InstantiationService");var ht=z("LogService"),re=z("OptionsService"),Or=z("OscLinkService"),wr=z("UnicodeService"),Ns=z("DecorationService");var qt=class{constructor(...t){this._entries=new Map;for(let[e,r]of t)this.set(e,r)}set(t,e){let r=this._entries.get(t);return this._entries.set(t,e),r}forEach(t){for(let[e,r]of this._entries.entries())t(e,r)}has(t){return this._entries.has(t)}get(t){return this._entries.get(t)}},ft=class{constructor(){this._services=new qt;this._services.set(Rr,this)}setService(t,e){this._services.set(t,e)}getService(t){return this._services.get(t)}createInstance(t,...e){let r=Cr(t).sort((a,l)=>a.index-l.index),i=[];for(let a of r){let l=this._services.get(a.id);if(!l)throw new Error(`[createInstance] ${t.name} depends on UNKNOWN service ${a.id._id}.`);i.push(l)}let s=r.length>0?r[0].index:e.length;if(e.length!==s)throw new Error(`[createInstance] First service dependency of ${t.name} at position ${s+1} conflicts with ${e.length} static arguments`);return new t(...e,...i)}};var vi={trace:0,debug:1,info:2,warn:3,error:4,off:5},gi="xterm.js: ",_e=class extends A{constructor(e){super();this._optionsService=e;this._logLevel=5;this._updateLogLevel(),this._register(this._optionsService.onSpecificOptionChange("logLevel",()=>this._updateLogLevel())),Ii=this}get logLevel(){return this._logLevel}_updateLogLevel(){this._logLevel=vi[this._optionsService.rawOptions.logLevel]}_evalLazyOptionalParams(e){for(let r=0;r<e.length;r++)typeof e[r]=="function"&&(e[r]=e[r]())}_log(e,r,i){this._evalLazyOptionalParams(i),e.call(console,(this._optionsService.options.logger?"":gi)+r,...i)}trace(e,...r){this._logLevel<=0&&this._log(this._optionsService.options.logger?.trace.bind(this._optionsService.options.logger)??console.log,e,r)}debug(e,...r){this._logLevel<=1&&this._log(this._optionsService.options.logger?.debug.bind(this._optionsService.options.logger)??console.log,e,r)}info(e,...r){this._logLevel<=2&&this._log(this._optionsService.options.logger?.info.bind(this._optionsService.options.logger)??console.info,e,r)}warn(e,...r){this._logLevel<=3&&this._log(this._optionsService.options.logger?.warn.bind(this._optionsService.options.logger)??console.warn,e,r)}error(e,...r){this._logLevel<=4&&this._log(this._optionsService.options.logger?.error.bind(this._optionsService.options.logger)??console.error,e,r)}};_e=K([W(0,re)],_e);var Ii;var we=class extends A{constructor(e){super();this._maxLength=e;this.onDeleteEmitter=this._register(new S);this.onDelete=this.onDeleteEmitter.event;this.onInsertEmitter=this._register(new S);this.onInsert=this.onInsertEmitter.event;this.onTrimEmitter=this._register(new S);this.onTrim=this.onTrimEmitter.event;this._array=new Array(this._maxLength),this._startIndex=0,this._length=0}get maxLength(){return this._maxLength}set maxLength(e){if(this._maxLength===e)return;let r=new Array(e);for(let i=0;i<Math.min(e,this.length);i++)r[i]=this._array[this._getCyclicIndex(i)];this._array=r,this._maxLength=e,this._startIndex=0}get length(){return this._length}set length(e){if(e>this._length)for(let r=this._length;r<e;r++)this._array[r]=void 0;this._length=e}get(e){return this._array[this._getCyclicIndex(e)]}set(e,r){this._array[this._getCyclicIndex(e)]=r}push(e){this._array[this._getCyclicIndex(this._length)]=e,this._length===this._maxLength?(this._startIndex=++this._startIndex%this._maxLength,this.onTrimEmitter.fire(1)):this._length++}recycle(){if(this._length!==this._maxLength)throw new Error("Can only recycle when the buffer is full");return this._startIndex=++this._startIndex%this._maxLength,this.onTrimEmitter.fire(1),this._array[this._getCyclicIndex(this._length-1)]}get isFull(){return this._length===this._maxLength}pop(){return this._array[this._getCyclicIndex(this._length---1)]}splice(e,r,...i){if(r){for(let s=e;s<this._length-r;s++)this._array[this._getCyclicIndex(s)]=this._array[this._getCyclicIndex(s+r)];this._length-=r,this.onDeleteEmitter.fire({index:e,amount:r})}for(let s=this._length-1;s>=e;s--)this._array[this._getCyclicIndex(s+i.length)]=this._array[this._getCyclicIndex(s)];for(let s=0;s<i.length;s++)this._array[this._getCyclicIndex(e+s)]=i[s];if(i.length&&this.onInsertEmitter.fire({index:e,amount:i.length}),this._length+i.length>this._maxLength){let s=this._length+i.length-this._maxLength;this._startIndex+=s,this._length=this._maxLength,this.onTrimEmitter.fire(s)}else this._length+=i.length}trimStart(e){e>this._length&&(e=this._length),this._startIndex+=e,this._length-=e,this.onTrimEmitter.fire(e)}shiftElements(e,r,i){if(!(r<=0)){if(e<0||e>=this._length)throw new Error("start argument out of range");if(e+i<0)throw new Error("Cannot shift elements in list beyond index 0");if(i>0){for(let a=r-1;a>=0;a--)this.set(e+a+i,this.get(e+a));let s=e+r+i-this._length;if(s>0)for(this._length+=s;this._length>this._maxLength;)this._length--,this._startIndex++,this.onTrimEmitter.fire(1)}else for(let s=0;s<r;s++)this.set(e+s+i,this.get(e+s))}}_getCyclicIndex(e){return(this._startIndex+e)%this._maxLength}};var pt=typeof process<"u"&&"title"in process,bt=pt?"node":navigator.userAgent,$t=pt?"node":navigator.platform,Qs=bt.includes("Firefox"),Js=bt.includes("Edge"),Zs=/^((?!chrome|android).)*safari/i.test(bt);var Pr=["Macintosh","MacIntel","MacPPC","Mac68K"].includes($t);var en=["Windows","Win16","Win32","WinCE"].includes($t),tn=$t.indexOf("Linux")>=0,rn=/\bCrOS\b/.test(bt);var _t=class{constructor(){this._tasks=[];this._i=0}enqueue(t){this._tasks.push(t),this._start()}flush(){for(;this._i<this._tasks.length;)this._tasks[this._i]()||this._i++;this.clear()}clear(){this._idleCallback&&(this._cancelCallback(this._idleCallback),this._idleCallback=void 0),this._i=0,this._tasks.length=0}_start(){this._idleCallback||(this._idleCallback=this._requestCallback(this._process.bind(this)))}_process(t){this._idleCallback=void 0;let e=0,r=0,i=t.timeRemaining(),s=0;for(;this._i<this._tasks.length;){if(e=Date.now(),this._tasks[this._i]()||this._i++,e=Math.max(1,Date.now()-e),r=Math.max(e,r),s=t.timeRemaining(),r*1.5>s){i-e<-20&&console.warn(`task queue exceeded allotted deadline by ${Math.abs(Math.round(i-e))}ms`),this._start();return}i=s}this.clear()}},jt=class extends _t{_requestCallback(t){return setTimeout(()=>t(this._createDeadline(16)))}_cancelCallback(t){clearTimeout(t)}_createDeadline(t){let e=Date.now()+t;return{timeRemaining:()=>Math.max(0,e-Date.now())}}},zt=class extends _t{_requestCallback(t){return requestIdleCallback(t)}_cancelCallback(t){cancelIdleCallback(t)}},Br=!pt&&"requestIdleCallback"in window?zt:jt;function Lr(n,t,e,r,i,s){let a=[];for(let l=0;l<n.length-1;l++){let c=l,u=n.get(++c);if(!u.isWrapped)continue;let _=[n.get(l)];for(;c<n.length&&u.isWrapped;)_.push(u),u=n.get(++c);if(!s&&r>=l&&r<c){l+=_.length-1;continue}let o=0,h=me(_,o,t),x=1,v=0;for(;x<_.length;){let d=me(_,x,t),O=d-v,w=e-h,L=Math.min(O,w);_[o].copyCellsFrom(_[x],v,h,L,!1),h+=L,h===e&&(o++,h=0),v+=L,v===d&&(x++,v=0),h===0&&o!==0&&_[o-1].getWidth(e-1)===2&&(_[o].copyCellsFrom(_[o-1],e-1,h++,1,!1),_[o-1].setCell(e-1,i))}_[o].replaceCells(h,e,i);let m=0;for(let d=_.length-1;d>0&&(d>o||_[d].getTrimmedLength()===0);d--)m++;m>0&&(a.push(l+_.length-m),a.push(m)),l+=_.length-1}return a}function Mr(n,t){let e=[],r=0,i=t[r],s=0;for(let a=0;a<n.length;a++)if(i===a){let l=t[++r];n.onDeleteEmitter.fire({index:a-s,amount:l}),a+=l-1,s+=l,i=t[++r]}else e.push(a);return{layout:e,countRemoved:s}}function Nr(n,t){let e=[];for(let r=0;r<t.length;r++)e.push(n.get(t[r]));for(let r=0;r<e.length;r++)n.set(r,e[r]);n.length=t.length}function Hr(n,t,e){let r=[],i=n.map((c,u)=>me(n,u,t)).reduce((c,u)=>c+u),s=0,a=0,l=0;for(;l<i;){if(i-l<e){r.push(i-l);break}s+=e;let c=me(n,a,t);s>c&&(s-=c,a++);let u=n[a].getWidth(s-1)===2;u&&s--;let _=u?e-1:e;r.push(_),l+=_}return r}function me(n,t,e){if(t===n.length-1)return n[t].getTrimmedLength();let r=!n[t].hasContent(e-1)&&n[t].getWidth(e-1)===1,i=n[t+1].getWidth(0)===2;return r&&i?e-1:e}var vt=class vt{constructor(t){this.line=t;this.isDisposed=!1;this._disposables=[];this._id=vt._nextId++;this._onDispose=this.register(new S);this.onDispose=this._onDispose.event}get id(){return this._id}dispose(){this.isDisposed||(this.isDisposed=!0,this.line=-1,this._onDispose.fire(),it(this._disposables),this._disposables.length=0)}register(t){return this._disposables.push(t),t}};vt._nextId=1;var mt=vt;var B={},Z=B.B;B[0]={"`":"\u25C6",a:"\u2592",b:"\u2409",c:"\u240C",d:"\u240D",e:"\u240A",f:"\xB0",g:"\xB1",h:"\u2424",i:"\u240B",j:"\u2518",k:"\u2510",l:"\u250C",m:"\u2514",n:"\u253C",o:"\u23BA",p:"\u23BB",q:"\u2500",r:"\u23BC",s:"\u23BD",t:"\u251C",u:"\u2524",v:"\u2534",w:"\u252C",x:"\u2502",y:"\u2264",z:"\u2265","{":"\u03C0","|":"\u2260","}":"\xA3","~":"\xB7"};B.A={"#":"\xA3"};B.B=void 0;B[4]={"#":"\xA3","@":"\xBE","[":"ij","\\":"\xBD","]":"|","{":"\xA8","|":"f","}":"\xBC","~":"\xB4"};B.C=B[5]={"[":"\xC4","\\":"\xD6","]":"\xC5","^":"\xDC","`":"\xE9","{":"\xE4","|":"\xF6","}":"\xE5","~":"\xFC"};B.R={"#":"\xA3","@":"\xE0","[":"\xB0","\\":"\xE7","]":"\xA7","{":"\xE9","|":"\xF9","}":"\xE8","~":"\xA8"};B.Q={"@":"\xE0","[":"\xE2","\\":"\xE7","]":"\xEA","^":"\xEE","`":"\xF4","{":"\xE9","|":"\xF9","}":"\xE8","~":"\xFB"};B.K={"@":"\xA7","[":"\xC4","\\":"\xD6","]":"\xDC","{":"\xE4","|":"\xF6","}":"\xFC","~":"\xDF"};B.Y={"#":"\xA3","@":"\xA7","[":"\xB0","\\":"\xE7","]":"\xE9","`":"\xF9","{":"\xE0","|":"\xF2","}":"\xE8","~":"\xEC"};B.E=B[6]={"@":"\xC4","[":"\xC6","\\":"\xD8","]":"\xC5","^":"\xDC","`":"\xE4","{":"\xE6","|":"\xF8","}":"\xE5","~":"\xFC"};B.Z={"#":"\xA3","@":"\xA7","[":"\xA1","\\":"\xD1","]":"\xBF","{":"\xB0","|":"\xF1","}":"\xE7"};B.H=B[7]={"@":"\xC9","[":"\xC4","\\":"\xD6","]":"\xC5","^":"\xDC","`":"\xE9","{":"\xE4","|":"\xF6","}":"\xE5","~":"\xFC"};B["="]={"#":"\xF9","@":"\xE0","[":"\xE9","\\":"\xE7","]":"\xEA","^":"\xEE",_:"\xE8","`":"\xF4","{":"\xE4","|":"\xF6","}":"\xFC","~":"\xFB"};var Fr=4294967295,ke=class{constructor(t,e,r){this._hasScrollback=t;this._optionsService=e;this._bufferService=r;this.ydisp=0;this.ybase=0;this.y=0;this.x=0;this.tabs={};this.savedY=0;this.savedX=0;this.savedCurAttrData=P.clone();this.savedCharset=Z;this.markers=[];this._nullCell=G.fromCharData([0,Ve,1,0]);this._whitespaceCell=G.fromCharData([0,qe,1,32]);this._isClearing=!1;this._memoryCleanupQueue=new Br;this._memoryCleanupPosition=0;this._cols=this._bufferService.cols,this._rows=this._bufferService.rows,this.lines=new we(this._getCorrectBufferLength(this._rows)),this.scrollTop=0,this.scrollBottom=this._rows-1,this.setupTabStops()}getNullCell(t){return t?(this._nullCell.fg=t.fg,this._nullCell.bg=t.bg,this._nullCell.extended=t.extended):(this._nullCell.fg=0,this._nullCell.bg=0,this._nullCell.extended=new te),this._nullCell}getWhitespaceCell(t){return t?(this._whitespaceCell.fg=t.fg,this._whitespaceCell.bg=t.bg,this._whitespaceCell.extended=t.extended):(this._whitespaceCell.fg=0,this._whitespaceCell.bg=0,this._whitespaceCell.extended=new te),this._whitespaceCell}getBlankLine(t,e){return new J(this._bufferService.cols,this.getNullCell(t),e)}get hasScrollback(){return this._hasScrollback&&this.lines.maxLength>this._rows}get isCursorInViewport(){let e=this.ybase+this.y-this.ydisp;return e>=0&&e<this._rows}_getCorrectBufferLength(t){if(!this._hasScrollback)return t;let e=t+this._optionsService.rawOptions.scrollback;return e>Fr?Fr:e}fillViewportRows(t){if(this.lines.length===0){t===void 0&&(t=P);let e=this._rows;for(;e--;)this.lines.push(this.getBlankLine(t))}}clear(){this.ydisp=0,this.ybase=0,this.y=0,this.x=0,this.lines=new we(this._getCorrectBufferLength(this._rows)),this.scrollTop=0,this.scrollBottom=this._rows-1,this.setupTabStops()}resize(t,e){let r=this.getNullCell(P),i=0,s=this._getCorrectBufferLength(e);if(s>this.lines.maxLength&&(this.lines.maxLength=s),this.lines.length>0){if(this._cols<t)for(let l=0;l<this.lines.length;l++)i+=+this.lines.get(l).resize(t,r);let a=0;if(this._rows<e)for(let l=this._rows;l<e;l++)this.lines.length<e+this.ybase&&(this._optionsService.rawOptions.windowsMode||this._optionsService.rawOptions.windowsPty.backend!==void 0||this._optionsService.rawOptions.windowsPty.buildNumber!==void 0?this.lines.push(new J(t,r)):this.ybase>0&&this.lines.length<=this.ybase+this.y+a+1?(this.ybase--,a++,this.ydisp>0&&this.ydisp--):this.lines.push(new J(t,r)));else for(let l=this._rows;l>e;l--)this.lines.length>e+this.ybase&&(this.lines.length>this.ybase+this.y+1?this.lines.pop():(this.ybase++,this.ydisp++));if(s<this.lines.maxLength){let l=this.lines.length-s;l>0&&(this.lines.trimStart(l),this.ybase=Math.max(this.ybase-l,0),this.ydisp=Math.max(this.ydisp-l,0),this.savedY=Math.max(this.savedY-l,0)),this.lines.maxLength=s}this.x=Math.min(this.x,t-1),this.y=Math.min(this.y,e-1),a&&(this.y+=a),this.savedX=Math.min(this.savedX,t-1),this.scrollTop=0}if(this.scrollBottom=e-1,this._isReflowEnabled&&(this._reflow(t,e),this._cols>t))for(let a=0;a<this.lines.length;a++)i+=+this.lines.get(a).resize(t,r);this._cols=t,this._rows=e,this._memoryCleanupQueue.clear(),i>.1*this.lines.length&&(this._memoryCleanupPosition=0,this._memoryCleanupQueue.enqueue(()=>this._batchedMemoryCleanup()))}_batchedMemoryCleanup(){let t=!0;this._memoryCleanupPosition>=this.lines.length&&(this._memoryCleanupPosition=0,t=!1);let e=0;for(;this._memoryCleanupPosition<this.lines.length;)if(e+=this.lines.get(this._memoryCleanupPosition++).cleanupMemory(),e>100)return!0;return t}get _isReflowEnabled(){let t=this._optionsService.rawOptions.windowsPty;return t&&t.buildNumber?this._hasScrollback&&t.backend==="conpty"&&t.buildNumber>=21376:this._hasScrollback&&!this._optionsService.rawOptions.windowsMode}_reflow(t,e){this._cols!==t&&(t>this._cols?this._reflowLarger(t,e):this._reflowSmaller(t,e))}_reflowLarger(t,e){let r=this._optionsService.rawOptions.reflowCursorLine,i=Lr(this.lines,this._cols,t,this.ybase+this.y,this.getNullCell(P),r);if(i.length>0){let s=Mr(this.lines,i);Nr(this.lines,s.layout),this._reflowLargerAdjustViewport(t,e,s.countRemoved)}}_reflowLargerAdjustViewport(t,e,r){let i=this.getNullCell(P),s=r;for(;s-- >0;)this.ybase===0?(this.y>0&&this.y--,this.lines.length<e&&this.lines.push(new J(t,i))):(this.ydisp===this.ybase&&this.ydisp--,this.ybase--);this.savedY=Math.max(this.savedY-r,0)}_reflowSmaller(t,e){let r=this._optionsService.rawOptions.reflowCursorLine,i=this.getNullCell(P),s=[],a=0;for(let l=this.lines.length-1;l>=0;l--){let c=this.lines.get(l);if(!c||!c.isWrapped&&c.getTrimmedLength()<=t)continue;let u=[c];for(;c.isWrapped&&l>0;)c=this.lines.get(--l),u.unshift(c);if(!r){let g=this.ybase+this.y;if(g>=l&&g<l+u.length)continue}let _=u[u.length-1].getTrimmedLength(),o=Hr(u,this._cols,t),h=o.length-u.length,x;this.ybase===0&&this.y!==this.lines.length-1?x=Math.max(0,this.y-this.lines.maxLength+h):x=Math.max(0,this.lines.length-this.lines.maxLength+h);let v=[];for(let g=0;g<h;g++){let D=this.getBlankLine(P,!0);v.push(D)}v.length>0&&(s.push({start:l+u.length+a,newLines:v}),a+=v.length),u.push(...v);let m=o.length-1,d=o[m];d===0&&(m--,d=o[m]);let O=u.length-h-1,w=_;for(;O>=0;){let g=Math.min(w,d);if(u[m]===void 0)break;if(u[m].copyCellsFrom(u[O],w-g,d-g,g,!0),d-=g,d===0&&(m--,d=o[m]),w-=g,w===0){O--;let D=Math.max(O,0);w=me(u,D,this._cols)}}for(let g=0;g<u.length;g++)o[g]<t&&u[g].setCell(o[g],i);let L=h-x;for(;L-- >0;)this.ybase===0?this.y<e-1?(this.y++,this.lines.pop()):(this.ybase++,this.ydisp++):this.ybase<Math.min(this.lines.maxLength,this.lines.length+a)-e&&(this.ybase===this.ydisp&&this.ydisp++,this.ybase++);this.savedY=Math.min(this.savedY+h,this.ybase+e-1)}if(s.length>0){let l=[],c=[];for(let d=0;d<this.lines.length;d++)c.push(this.lines.get(d));let u=this.lines.length,_=u-1,o=0,h=s[o];this.lines.length=Math.min(this.lines.maxLength,this.lines.length+a);let x=0;for(let d=Math.min(this.lines.maxLength-1,u+a-1);d>=0;d--)if(h&&h.start>_+x){for(let O=h.newLines.length-1;O>=0;O--)this.lines.set(d--,h.newLines[O]);d++,l.push({index:_+1,amount:h.newLines.length}),x+=h.newLines.length,h=s[++o]}else this.lines.set(d,c[_--]);let v=0;for(let d=l.length-1;d>=0;d--)l[d].index+=v,this.lines.onInsertEmitter.fire(l[d]),v+=l[d].amount;let m=Math.max(0,u+a-this.lines.maxLength);m>0&&this.lines.onTrimEmitter.fire(m)}}translateBufferLineToString(t,e,r=0,i){let s=this.lines.get(t);return s?s.translateToString(e,r,i):""}getWrappedRangeForLine(t){let e=t,r=t;for(;e>0&&this.lines.get(e).isWrapped;)e--;for(;r+1<this.lines.length&&this.lines.get(r+1).isWrapped;)r++;return{first:e,last:r}}setupTabStops(t){for(t!=null?this.tabs[t]||(t=this.prevStop(t)):(this.tabs={},t=0);t<this._cols;t+=this._optionsService.rawOptions.tabStopWidth)this.tabs[t]=!0}prevStop(t){for(t==null&&(t=this.x);!this.tabs[--t]&&t>0;);return t>=this._cols?this._cols-1:t<0?0:t}nextStop(t){for(t==null&&(t=this.x);!this.tabs[++t]&&t<this._cols;);return t>=this._cols?this._cols-1:t<0?0:t}clearMarkers(t){this._isClearing=!0;for(let e=0;e<this.markers.length;e++)this.markers[e].line===t&&(this.markers[e].dispose(),this.markers.splice(e--,1));this._isClearing=!1}clearAllMarkers(){this._isClearing=!0;for(let t=0;t<this.markers.length;t++)this.markers[t].dispose();this.markers.length=0,this._isClearing=!1}addMarker(t){let e=new mt(t);return this.markers.push(e),e.register(this.lines.onTrim(r=>{e.line-=r,e.line<0&&e.dispose()})),e.register(this.lines.onInsert(r=>{e.line>=r.index&&(e.line+=r.amount)})),e.register(this.lines.onDelete(r=>{e.line>=r.index&&e.line<r.index+r.amount&&e.dispose(),e.line>r.index&&(e.line-=r.amount)})),e.register(e.onDispose(()=>this._removeMarker(e))),e}_removeMarker(t){this._isClearing||this.markers.splice(this.markers.indexOf(t),1)}};var gt=class extends A{constructor(e,r){super();this._optionsService=e;this._bufferService=r;this._onBufferActivate=this._register(new S);this.onBufferActivate=this._onBufferActivate.event;this.reset(),this._register(this._optionsService.onSpecificOptionChange("scrollback",()=>this.resize(this._bufferService.cols,this._bufferService.rows))),this._register(this._optionsService.onSpecificOptionChange("tabStopWidth",()=>this.setupTabStops()))}reset(){this._normal=new ke(!0,this._optionsService,this._bufferService),this._normal.fillViewportRows(),this._alt=new ke(!1,this._optionsService,this._bufferService),this._activeBuffer=this._normal,this._onBufferActivate.fire({activeBuffer:this._normal,inactiveBuffer:this._alt}),this.setupTabStops()}get alt(){return this._alt}get active(){return this._activeBuffer}get normal(){return this._normal}activateNormalBuffer(){this._activeBuffer!==this._normal&&(this._normal.x=this._alt.x,this._normal.y=this._alt.y,this._alt.clearAllMarkers(),this._alt.clear(),this._activeBuffer=this._normal,this._onBufferActivate.fire({activeBuffer:this._normal,inactiveBuffer:this._alt}))}activateAltBuffer(e){this._activeBuffer!==this._alt&&(this._alt.fillViewportRows(e),this._alt.x=this._normal.x,this._alt.y=this._normal.y,this._activeBuffer=this._alt,this._onBufferActivate.fire({activeBuffer:this._alt,inactiveBuffer:this._normal}))}resize(e,r){this._normal.resize(e,r),this._alt.resize(e,r),this.setupTabStops(e)}setupTabStops(e){this._normal.setupTabStops(e),this._alt.setupTabStops(e)}};var Yt=2,Qt=1,ve=class extends A{constructor(e){super();this.isUserScrolling=!1;this._onResize=this._register(new S);this.onResize=this._onResize.event;this._onScroll=this._register(new S);this.onScroll=this._onScroll.event;this.cols=Math.max(e.rawOptions.cols||0,Yt),this.rows=Math.max(e.rawOptions.rows||0,Qt),this.buffers=this._register(new gt(e,this))}get buffer(){return this.buffers.active}resize(e,r){this.cols=e,this.rows=r,this.buffers.resize(e,r),this._onResize.fire({cols:e,rows:r})}reset(){this.buffers.reset(),this.isUserScrolling=!1}scroll(e,r=!1){let i=this.buffer,s;s=this._cachedBlankLine,(!s||s.length!==this.cols||s.getFg(0)!==e.fg||s.getBg(0)!==e.bg)&&(s=i.getBlankLine(e,r),this._cachedBlankLine=s),s.isWrapped=r;let a=i.ybase+i.scrollTop,l=i.ybase+i.scrollBottom;if(i.scrollTop===0){let c=i.lines.isFull;l===i.lines.length-1?c?i.lines.recycle().copyFrom(s):i.lines.push(s.clone()):i.lines.splice(l+1,0,s.clone()),c?this.isUserScrolling&&(i.ydisp=Math.max(i.ydisp-1,0)):(i.ybase++,this.isUserScrolling||i.ydisp++)}else{let c=l-a+1;i.lines.shiftElements(a+1,c-1,-1),i.lines.set(l,s.clone())}this.isUserScrolling||(i.ydisp=i.ybase),this._onScroll.fire(i.ydisp)}scrollLines(e,r){let i=this.buffer;if(e<0){if(i.ydisp===0)return;this.isUserScrolling=!0}else e+i.ydisp>=i.ybase&&(this.isUserScrolling=!1);let s=i.ydisp;i.ydisp=Math.max(Math.min(i.ydisp+e,i.ybase),0),s!==i.ydisp&&(r||this._onScroll.fire(i.ydisp))}};ve=K([W(0,re)],ve);var ge={cols:80,rows:24,cursorBlink:!1,cursorStyle:"block",cursorWidth:1,cursorInactiveStyle:"outline",customGlyphs:!0,drawBoldTextInBrightColors:!0,documentOverride:null,fastScrollModifier:"alt",fastScrollSensitivity:5,fontFamily:"monospace",fontSize:15,fontWeight:"normal",fontWeightBold:"bold",ignoreBracketedPasteMode:!1,lineHeight:1,letterSpacing:0,linkHandler:null,logLevel:"info",logger:null,scrollback:1e3,scrollOnEraseInDisplay:!1,scrollOnUserInput:!0,scrollSensitivity:1,screenReaderMode:!1,smoothScrollDuration:0,macOptionIsMeta:!1,macOptionClickForcesSelection:!1,minimumContrastRatio:1,disableStdin:!1,allowProposedApi:!1,allowTransparency:!1,tabStopWidth:8,theme:{},reflowCursorLine:!1,rescaleOverlappingGlyphs:!1,rightClickSelectsWord:Pr,windowOptions:{},windowsMode:!1,windowsPty:{},wordSeparator:" ()[]{}',\"`",altClickMovesCursor:!0,convertEol:!1,termName:"xterm",cancelEvents:!1,overviewRuler:{}},Ti=["normal","bold","100","200","300","400","500","600","700","800","900"],It=class extends A{constructor(e){super();this._onOptionChange=this._register(new S);this.onOptionChange=this._onOptionChange.event;let r={...ge};for(let i in e)if(i in r)try{let s=e[i];r[i]=this._sanitizeAndValidateOption(i,s)}catch(s){console.error(s)}this.rawOptions=r,this.options={...r},this._setupOptions(),this._register(Q(()=>{this.rawOptions.linkHandler=null,this.rawOptions.documentOverride=null}))}onSpecificOptionChange(e,r){return this.onOptionChange(i=>{i===e&&r(this.rawOptions[e])})}onMultipleOptionChange(e,r){return this.onOptionChange(i=>{e.indexOf(i)!==-1&&r()})}_setupOptions(){let e=i=>{if(!(i in ge))throw new Error(`No option with key "${i}"`);return this.rawOptions[i]},r=(i,s)=>{if(!(i in ge))throw new Error(`No option with key "${i}"`);s=this._sanitizeAndValidateOption(i,s),this.rawOptions[i]!==s&&(this.rawOptions[i]=s,this._onOptionChange.fire(i))};for(let i in this.rawOptions){let s={get:e.bind(this,i),set:r.bind(this,i)};Object.defineProperty(this.options,i,s)}}_sanitizeAndValidateOption(e,r){switch(e){case"cursorStyle":if(r||(r=ge[e]),!Si(r))throw new Error(`"${r}" is not a valid value for ${e}`);break;case"wordSeparator":r||(r=ge[e]);break;case"fontWeight":case"fontWeightBold":if(typeof r=="number"&&1<=r&&r<=1e3)break;r=Ti.includes(r)?r:ge[e];break;case"cursorWidth":r=Math.floor(r);case"lineHeight":case"tabStopWidth":if(r<1)throw new Error(`${e} cannot be less than 1, value: ${r}`);break;case"minimumContrastRatio":r=Math.max(1,Math.min(21,Math.round(r*10)/10));break;case"scrollback":if(r=Math.min(r,4294967295),r<0)throw new Error(`${e} cannot be less than 0, value: ${r}`);break;case"fastScrollSensitivity":case"scrollSensitivity":if(r<=0)throw new Error(`${e} cannot be less than or equal to 0, value: ${r}`);break;case"rows":case"cols":if(!r&&r!==0)throw new Error(`${e} must be numeric, value: ${r}`);break;case"windowsPty":r=r??{};break}return r}};function Si(n){return n==="block"||n==="underline"||n==="bar"}function Ie(n,t=5){if(typeof n!="object")return n;let e=Array.isArray(n)?[]:{};for(let r in n)e[r]=t<=1?n[r]:n[r]&&Ie(n[r],t-1);return e}var Ur=Object.freeze({insertMode:!1}),Wr=Object.freeze({applicationCursorKeys:!1,applicationKeypad:!1,bracketedPasteMode:!1,cursorBlink:void 0,cursorStyle:void 0,origin:!1,reverseWraparound:!1,sendFocus:!1,wraparound:!0}),xe=class extends A{constructor(e,r,i){super();this._bufferService=e;this._logService=r;this._optionsService=i;this.isCursorInitialized=!1;this.isCursorHidden=!1;this._onData=this._register(new S);this.onData=this._onData.event;this._onUserInput=this._register(new S);this.onUserInput=this._onUserInput.event;this._onBinary=this._register(new S);this.onBinary=this._onBinary.event;this._onRequestScrollToBottom=this._register(new S);this.onRequestScrollToBottom=this._onRequestScrollToBottom.event;this.modes=Ie(Ur),this.decPrivateModes=Ie(Wr)}reset(){this.modes=Ie(Ur),this.decPrivateModes=Ie(Wr)}triggerDataEvent(e,r=!1){if(this._optionsService.rawOptions.disableStdin)return;let i=this._bufferService.buffer;r&&this._optionsService.rawOptions.scrollOnUserInput&&i.ybase!==i.ydisp&&this._onRequestScrollToBottom.fire(),r&&this._onUserInput.fire(),this._logService.debug(`sending data "${e}"`,()=>e.split("").map(s=>s.charCodeAt(0))),this._onData.fire(e)}triggerBinaryEvent(e){this._optionsService.rawOptions.disableStdin||(this._logService.debug(`sending binary "${e}"`,()=>e.split("").map(r=>r.charCodeAt(0))),this._onBinary.fire(e))}};xe=K([W(0,X),W(1,ht),W(2,re)],xe);var Gr={NONE:{events:0,restrict:()=>!1},X10:{events:1,restrict:n=>n.button===4||n.action!==1?!1:(n.ctrl=!1,n.alt=!1,n.shift=!1,!0)},VT200:{events:19,restrict:n=>n.action!==32},DRAG:{events:23,restrict:n=>!(n.action===32&&n.button===3)},ANY:{events:31,restrict:n=>!0}};function Jt(n,t){let e=(n.ctrl?16:0)|(n.shift?4:0)|(n.alt?8:0);return n.button===4?(e|=64,e|=n.action):(e|=n.button&3,n.button&4&&(e|=64),n.button&8&&(e|=128),n.action===32?e|=32:n.action===0&&!t&&(e|=3)),e}var Zt=String.fromCharCode,Kr={DEFAULT:n=>{let t=[Jt(n,!1)+32,n.col+32,n.row+32];return t[0]>255||t[1]>255||t[2]>255?"":`\x1B[M${Zt(t[0])}${Zt(t[1])}${Zt(t[2])}`},SGR:n=>{let t=n.action===0&&n.button!==4?"m":"M";return`\x1B[<${Jt(n,!0)};${n.col};${n.row}${t}`},SGR_PIXELS:n=>{let t=n.action===0&&n.button!==4?"m":"M";return`\x1B[<${Jt(n,!0)};${n.x};${n.y}${t}`}},Te=class extends A{constructor(e,r){super();this._bufferService=e;this._coreService=r;this._protocols={};this._encodings={};this._activeProtocol="";this._activeEncoding="";this._lastEvent=null;this._onProtocolChange=this._register(new S);this.onProtocolChange=this._onProtocolChange.event;for(let i of Object.keys(Gr))this.addProtocol(i,Gr[i]);for(let i of Object.keys(Kr))this.addEncoding(i,Kr[i]);this.reset()}addProtocol(e,r){this._protocols[e]=r}addEncoding(e,r){this._encodings[e]=r}get activeProtocol(){return this._activeProtocol}get areMouseEventsActive(){return this._protocols[this._activeProtocol].events!==0}set activeProtocol(e){if(!this._protocols[e])throw new Error(`unknown protocol "${e}"`);this._activeProtocol=e,this._onProtocolChange.fire(this._protocols[e].events)}get activeEncoding(){return this._activeEncoding}set activeEncoding(e){if(!this._encodings[e])throw new Error(`unknown encoding "${e}"`);this._activeEncoding=e}reset(){this.activeProtocol="NONE",this.activeEncoding="DEFAULT",this._lastEvent=null}triggerMouseEvent(e){if(e.col<0||e.col>=this._bufferService.cols||e.row<0||e.row>=this._bufferService.rows||e.button===4&&e.action===32||e.button===3&&e.action!==32||e.button!==4&&(e.action===2||e.action===3)||(e.col++,e.row++,e.action===32&&this._lastEvent&&this._equalEvents(this._lastEvent,e,this._activeEncoding==="SGR_PIXELS"))||!this._protocols[this._activeProtocol].restrict(e))return!1;let r=this._encodings[this._activeEncoding](e);return r&&(this._activeEncoding==="DEFAULT"?this._coreService.triggerBinaryEvent(r):this._coreService.triggerDataEvent(r,!0)),this._lastEvent=e,!0}explainEvents(e){return{down:!!(e&1),up:!!(e&2),drag:!!(e&4),move:!!(e&8),wheel:!!(e&16)}}_equalEvents(e,r,i){if(i){if(e.x!==r.x||e.y!==r.y)return!1}else if(e.col!==r.col||e.row!==r.row)return!1;return!(e.button!==r.button||e.action!==r.action||e.ctrl!==r.ctrl||e.alt!==r.alt||e.shift!==r.shift)}};Te=K([W(0,X),W(1,dt)],Te);var er=[[768,879],[1155,1158],[1160,1161],[1425,1469],[1471,1471],[1473,1474],[1476,1477],[1479,1479],[1536,1539],[1552,1557],[1611,1630],[1648,1648],[1750,1764],[1767,1768],[1770,1773],[1807,1807],[1809,1809],[1840,1866],[1958,1968],[2027,2035],[2305,2306],[2364,2364],[2369,2376],[2381,2381],[2385,2388],[2402,2403],[2433,2433],[2492,2492],[2497,2500],[2509,2509],[2530,2531],[2561,2562],[2620,2620],[2625,2626],[2631,2632],[2635,2637],[2672,2673],[2689,2690],[2748,2748],[2753,2757],[2759,2760],[2765,2765],[2786,2787],[2817,2817],[2876,2876],[2879,2879],[2881,2883],[2893,2893],[2902,2902],[2946,2946],[3008,3008],[3021,3021],[3134,3136],[3142,3144],[3146,3149],[3157,3158],[3260,3260],[3263,3263],[3270,3270],[3276,3277],[3298,3299],[3393,3395],[3405,3405],[3530,3530],[3538,3540],[3542,3542],[3633,3633],[3636,3642],[3655,3662],[3761,3761],[3764,3769],[3771,3772],[3784,3789],[3864,3865],[3893,3893],[3895,3895],[3897,3897],[3953,3966],[3968,3972],[3974,3975],[3984,3991],[3993,4028],[4038,4038],[4141,4144],[4146,4146],[4150,4151],[4153,4153],[4184,4185],[4448,4607],[4959,4959],[5906,5908],[5938,5940],[5970,5971],[6002,6003],[6068,6069],[6071,6077],[6086,6086],[6089,6099],[6109,6109],[6155,6157],[6313,6313],[6432,6434],[6439,6440],[6450,6450],[6457,6459],[6679,6680],[6912,6915],[6964,6964],[6966,6970],[6972,6972],[6978,6978],[7019,7027],[7616,7626],[7678,7679],[8203,8207],[8234,8238],[8288,8291],[8298,8303],[8400,8431],[12330,12335],[12441,12442],[43014,43014],[43019,43019],[43045,43046],[64286,64286],[65024,65039],[65056,65059],[65279,65279],[65529,65531]],Ei=[[68097,68099],[68101,68102],[68108,68111],[68152,68154],[68159,68159],[119143,119145],[119155,119170],[119173,119179],[119210,119213],[119362,119364],[917505,917505],[917536,917631],[917760,917999]],M;function yi(n,t){let e=0,r=t.length-1,i;if(n<t[0][0]||n>t[r][1])return!1;for(;r>=e;)if(i=e+r>>1,n>t[i][1])e=i+1;else if(n<t[i][0])r=i-1;else return!0;return!1}var xt=class{constructor(){this.version="6";if(!M){M=new Uint8Array(65536),M.fill(1),M[0]=0,M.fill(0,1,32),M.fill(0,127,160),M.fill(2,4352,4448),M[9001]=2,M[9002]=2,M.fill(2,11904,42192),M[12351]=1,M.fill(2,44032,55204),M.fill(2,63744,64256),M.fill(2,65040,65050),M.fill(2,65072,65136),M.fill(2,65280,65377),M.fill(2,65504,65511);for(let t=0;t<er.length;++t)M.fill(0,er[t][0],er[t][1]+1)}}wcwidth(t){return t<32?0:t<127?1:t<65536?M[t]:yi(t,Ei)?0:t>=131072&&t<=196605||t>=196608&&t<=262141?2:1}charProperties(t,e){let r=this.wcwidth(t),i=r===0&&e!==0;if(i){let s=q.extractWidth(e);s===0?i=!1:s>r&&(r=s)}return q.createPropertyValue(0,r,i)}};var q=class n{constructor(){this._providers=Object.create(null);this._active="";this._onChange=new S;this.onChange=this._onChange.event;let t=new xt;this.register(t),this._active=t.version,this._activeProvider=t}static extractShouldJoin(t){return(t&1)!==0}static extractWidth(t){return t>>1&3}static extractCharKind(t){return t>>3}static createPropertyValue(t,e,r=!1){return(t&16777215)<<3|(e&3)<<1|(r?1:0)}dispose(){this._onChange.dispose()}get versions(){return Object.keys(this._providers)}get activeVersion(){return this._active}set activeVersion(t){if(!this._providers[t])throw new Error(`unknown Unicode version "${t}"`);this._active=t,this._activeProvider=this._providers[t],this._onChange.fire(t)}register(t){this._providers[t.version]=t}wcwidth(t){return this._activeProvider.wcwidth(t)}getStringCellWidth(t){let e=0,r=0,i=t.length;for(let s=0;s<i;++s){let a=t.charCodeAt(s);if(55296<=a&&a<=56319){if(++s>=i)return e+this.wcwidth(a);let u=t.charCodeAt(s);56320<=u&&u<=57343?a=(a-55296)*1024+u-56320+65536:e+=this.wcwidth(u)}let l=this.charProperties(a,r),c=n.extractWidth(l);n.extractShouldJoin(l)&&(c-=n.extractWidth(r)),e+=c,r=l}return e}charProperties(t,e){return this._activeProvider.charProperties(t,e)}};var Tt=class{constructor(){this.glevel=0;this._charsets=[]}reset(){this.charset=void 0,this._charsets=[],this.glevel=0}setgLevel(t){this.glevel=t,this.charset=this._charsets[t]}setgCharset(t,e){this._charsets[t]=e,this.glevel===t&&(this.charset=e)}};function tr(n){let e=n.buffer.lines.get(n.buffer.ybase+n.buffer.y-1)?.get(n.cols-1),r=n.buffer.lines.get(n.buffer.ybase+n.buffer.y);r&&e&&(r.isWrapped=e[3]!==0&&e[3]!==32)}var k;(p=>(p.NUL="\0",p.SOH="",p.STX="",p.ETX="",p.EOT="",p.ENQ="",p.ACK="",p.BEL="\x07",p.BS="\b",p.HT="	",p.LF=`
`,p.VT="\v",p.FF="\f",p.CR="\r",p.SO="",p.SI="",p.DLE="",p.DC1="",p.DC2="",p.DC3="",p.DC4="",p.NAK="",p.SYN="",p.ETB="",p.CAN="",p.EM="",p.SUB="",p.ESC="\x1B",p.FS="",p.GS="",p.RS="",p.US="",p.SP=" ",p.DEL="\x7F"))(k||={});var Pe;(f=>(f.PAD="\x80",f.HOP="\x81",f.BPH="\x82",f.NBH="\x83",f.IND="\x84",f.NEL="\x85",f.SSA="\x86",f.ESA="\x87",f.HTS="\x88",f.HTJ="\x89",f.VTS="\x8A",f.PLD="\x8B",f.PLU="\x8C",f.RI="\x8D",f.SS2="\x8E",f.SS3="\x8F",f.DCS="\x90",f.PU1="\x91",f.PU2="\x92",f.STS="\x93",f.CCH="\x94",f.MW="\x95",f.SPA="\x96",f.EPA="\x97",f.SOS="\x98",f.SGCI="\x99",f.SCI="\x9A",f.CSI="\x9B",f.ST="\x9C",f.OSC="\x9D",f.PM="\x9E",f.APC="\x9F"))(Pe||={});var Ci;(t=>t.ST=`${k.ESC}\\`)(Ci||={});var Be=2147483647,Di=256,Se=class n{constructor(t=32,e=32){this.maxLength=t;this.maxSubParamsLength=e;if(e>Di)throw new Error("maxSubParamsLength must not be greater than 256");this.params=new Int32Array(t),this.length=0,this._subParams=new Int32Array(e),this._subParamsLength=0,this._subParamsIdx=new Uint16Array(t),this._rejectDigits=!1,this._rejectSubDigits=!1,this._digitIsSub=!1}static fromArray(t){let e=new n;if(!t.length)return e;for(let r=Array.isArray(t[0])?1:0;r<t.length;++r){let i=t[r];if(Array.isArray(i))for(let s=0;s<i.length;++s)e.addSubParam(i[s]);else e.addParam(i)}return e}clone(){let t=new n(this.maxLength,this.maxSubParamsLength);return t.params.set(this.params),t.length=this.length,t._subParams.set(this._subParams),t._subParamsLength=this._subParamsLength,t._subParamsIdx.set(this._subParamsIdx),t._rejectDigits=this._rejectDigits,t._rejectSubDigits=this._rejectSubDigits,t._digitIsSub=this._digitIsSub,t}toArray(){let t=[];for(let e=0;e<this.length;++e){t.push(this.params[e]);let r=this._subParamsIdx[e]>>8,i=this._subParamsIdx[e]&255;i-r>0&&t.push(Array.prototype.slice.call(this._subParams,r,i))}return t}reset(){this.length=0,this._subParamsLength=0,this._rejectDigits=!1,this._rejectSubDigits=!1,this._digitIsSub=!1}addParam(t){if(this._digitIsSub=!1,this.length>=this.maxLength){this._rejectDigits=!0;return}if(t<-1)throw new Error("values lesser than -1 are not allowed");this._subParamsIdx[this.length]=this._subParamsLength<<8|this._subParamsLength,this.params[this.length++]=t>Be?Be:t}addSubParam(t){if(this._digitIsSub=!0,!!this.length){if(this._rejectDigits||this._subParamsLength>=this.maxSubParamsLength){this._rejectSubDigits=!0;return}if(t<-1)throw new Error("values lesser than -1 are not allowed");this._subParams[this._subParamsLength++]=t>Be?Be:t,this._subParamsIdx[this.length-1]++}}hasSubParams(t){return(this._subParamsIdx[t]&255)-(this._subParamsIdx[t]>>8)>0}getSubParams(t){let e=this._subParamsIdx[t]>>8,r=this._subParamsIdx[t]&255;return r-e>0?this._subParams.subarray(e,r):null}getSubParamsAll(){let t={};for(let e=0;e<this.length;++e){let r=this._subParamsIdx[e]>>8,i=this._subParamsIdx[e]&255;i-r>0&&(t[e]=this._subParams.slice(r,i))}return t}addDigit(t){let e;if(this._rejectDigits||!(e=this._digitIsSub?this._subParamsLength:this.length)||this._digitIsSub&&this._rejectSubDigits)return;let r=this._digitIsSub?this._subParams:this.params,i=r[e-1];r[e-1]=~i?Math.min(i*10+t,Be):t}};var Le=[],St=class{constructor(){this._state=0;this._active=Le;this._id=-1;this._handlers=Object.create(null);this._handlerFb=()=>{};this._stack={paused:!1,loopPosition:0,fallThrough:!1}}registerHandler(t,e){this._handlers[t]===void 0&&(this._handlers[t]=[]);let r=this._handlers[t];return r.push(e),{dispose:()=>{let i=r.indexOf(e);i!==-1&&r.splice(i,1)}}}clearHandler(t){this._handlers[t]&&delete this._handlers[t]}setHandlerFallback(t){this._handlerFb=t}dispose(){this._handlers=Object.create(null),this._handlerFb=()=>{},this._active=Le}reset(){if(this._state===2)for(let t=this._stack.paused?this._stack.loopPosition-1:this._active.length-1;t>=0;--t)this._active[t].end(!1);this._stack.paused=!1,this._active=Le,this._id=-1,this._state=0}_start(){if(this._active=this._handlers[this._id]||Le,!this._active.length)this._handlerFb(this._id,"START");else for(let t=this._active.length-1;t>=0;t--)this._active[t].start()}_put(t,e,r){if(!this._active.length)this._handlerFb(this._id,"PUT",le(t,e,r));else for(let i=this._active.length-1;i>=0;i--)this._active[i].put(t,e,r)}start(){this.reset(),this._state=1}put(t,e,r){if(this._state!==3){if(this._state===1)for(;e<r;){let i=t[e++];if(i===59){this._state=2,this._start();break}if(i<48||57<i){this._state=3;return}this._id===-1&&(this._id=0),this._id=this._id*10+i-48}this._state===2&&r-e>0&&this._put(t,e,r)}}end(t,e=!0){if(this._state!==0){if(this._state!==3)if(this._state===1&&this._start(),!this._active.length)this._handlerFb(this._id,"END",t);else{let r=!1,i=this._active.length-1,s=!1;if(this._stack.paused&&(i=this._stack.loopPosition-1,r=e,s=this._stack.fallThrough,this._stack.paused=!1),!s&&r===!1){for(;i>=0&&(r=this._active[i].end(t),r!==!0);i--)if(r instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=i,this._stack.fallThrough=!1,r;i--}for(;i>=0;i--)if(r=this._active[i].end(!1),r instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=i,this._stack.fallThrough=!0,r}this._active=Le,this._id=-1,this._state=0}}},F=class{constructor(t){this._handler=t;this._data="";this._hitLimit=!1}start(){this._data="",this._hitLimit=!1}put(t,e,r){this._hitLimit||(this._data+=le(t,e,r),this._data.length>1e7&&(this._data="",this._hitLimit=!0))}end(t){let e=!1;if(this._hitLimit)e=!1;else if(t&&(e=this._handler(this._data),e instanceof Promise))return e.then(r=>(this._data="",this._hitLimit=!1,r));return this._data="",this._hitLimit=!1,e}};var Me=[],Et=class{constructor(){this._handlers=Object.create(null);this._active=Me;this._ident=0;this._handlerFb=()=>{};this._stack={paused:!1,loopPosition:0,fallThrough:!1}}dispose(){this._handlers=Object.create(null),this._handlerFb=()=>{},this._active=Me}registerHandler(t,e){this._handlers[t]===void 0&&(this._handlers[t]=[]);let r=this._handlers[t];return r.push(e),{dispose:()=>{let i=r.indexOf(e);i!==-1&&r.splice(i,1)}}}clearHandler(t){this._handlers[t]&&delete this._handlers[t]}setHandlerFallback(t){this._handlerFb=t}reset(){if(this._active.length)for(let t=this._stack.paused?this._stack.loopPosition-1:this._active.length-1;t>=0;--t)this._active[t].unhook(!1);this._stack.paused=!1,this._active=Me,this._ident=0}hook(t,e){if(this.reset(),this._ident=t,this._active=this._handlers[t]||Me,!this._active.length)this._handlerFb(this._ident,"HOOK",e);else for(let r=this._active.length-1;r>=0;r--)this._active[r].hook(e)}put(t,e,r){if(!this._active.length)this._handlerFb(this._ident,"PUT",le(t,e,r));else for(let i=this._active.length-1;i>=0;i--)this._active[i].put(t,e,r)}unhook(t,e=!0){if(!this._active.length)this._handlerFb(this._ident,"UNHOOK",t);else{let r=!1,i=this._active.length-1,s=!1;if(this._stack.paused&&(i=this._stack.loopPosition-1,r=e,s=this._stack.fallThrough,this._stack.paused=!1),!s&&r===!1){for(;i>=0&&(r=this._active[i].unhook(t),r!==!0);i--)if(r instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=i,this._stack.fallThrough=!1,r;i--}for(;i>=0;i--)if(r=this._active[i].unhook(!1),r instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=i,this._stack.fallThrough=!0,r}this._active=Me,this._ident=0}},Ne=new Se;Ne.addParam(0);var He=class{constructor(t){this._handler=t;this._data="";this._params=Ne;this._hitLimit=!1}hook(t){this._params=t.length>1||t.params[0]?t.clone():Ne,this._data="",this._hitLimit=!1}put(t,e,r){this._hitLimit||(this._data+=le(t,e,r),this._data.length>1e7&&(this._data="",this._hitLimit=!0))}unhook(t){let e=!1;if(this._hitLimit)e=!1;else if(t&&(e=this._handler(this._data,this._params),e instanceof Promise))return e.then(r=>(this._params=Ne,this._data="",this._hitLimit=!1,r));return this._params=Ne,this._data="",this._hitLimit=!1,e}};var ir=class{constructor(t){this.table=new Uint8Array(t)}setDefault(t,e){this.table.fill(t<<4|e)}add(t,e,r,i){this.table[e<<8|t]=r<<4|i}addMany(t,e,r,i){for(let s=0;s<t.length;s++)this.table[e<<8|t[s]]=r<<4|i}},$=160,Ai=function(){let n=new ir(4095),e=Array.apply(null,Array(256)).map((c,u)=>u),r=(c,u)=>e.slice(c,u),i=r(32,127),s=r(0,24);s.push(25),s.push.apply(s,r(28,32));let a=r(0,14),l;n.setDefault(1,0),n.addMany(i,0,2,0);for(l in a)n.addMany([24,26,153,154],l,3,0),n.addMany(r(128,144),l,3,0),n.addMany(r(144,152),l,3,0),n.add(156,l,0,0),n.add(27,l,11,1),n.add(157,l,4,8),n.addMany([152,158,159],l,0,7),n.add(155,l,11,3),n.add(144,l,11,9);return n.addMany(s,0,3,0),n.addMany(s,1,3,1),n.add(127,1,0,1),n.addMany(s,8,0,8),n.addMany(s,3,3,3),n.add(127,3,0,3),n.addMany(s,4,3,4),n.add(127,4,0,4),n.addMany(s,6,3,6),n.addMany(s,5,3,5),n.add(127,5,0,5),n.addMany(s,2,3,2),n.add(127,2,0,2),n.add(93,1,4,8),n.addMany(i,8,5,8),n.add(127,8,5,8),n.addMany([156,27,24,26,7],8,6,0),n.addMany(r(28,32),8,0,8),n.addMany([88,94,95],1,0,7),n.addMany(i,7,0,7),n.addMany(s,7,0,7),n.add(156,7,0,0),n.add(127,7,0,7),n.add(91,1,11,3),n.addMany(r(64,127),3,7,0),n.addMany(r(48,60),3,8,4),n.addMany([60,61,62,63],3,9,4),n.addMany(r(48,60),4,8,4),n.addMany(r(64,127),4,7,0),n.addMany([60,61,62,63],4,0,6),n.addMany(r(32,64),6,0,6),n.add(127,6,0,6),n.addMany(r(64,127),6,0,0),n.addMany(r(32,48),3,9,5),n.addMany(r(32,48),5,9,5),n.addMany(r(48,64),5,0,6),n.addMany(r(64,127),5,7,0),n.addMany(r(32,48),4,9,5),n.addMany(r(32,48),1,9,2),n.addMany(r(32,48),2,9,2),n.addMany(r(48,127),2,10,0),n.addMany(r(48,80),1,10,0),n.addMany(r(81,88),1,10,0),n.addMany([89,90,92],1,10,0),n.addMany(r(96,127),1,10,0),n.add(80,1,11,9),n.addMany(s,9,0,9),n.add(127,9,0,9),n.addMany(r(28,32),9,0,9),n.addMany(r(32,48),9,9,12),n.addMany(r(48,60),9,8,10),n.addMany([60,61,62,63],9,9,10),n.addMany(s,11,0,11),n.addMany(r(32,128),11,0,11),n.addMany(r(28,32),11,0,11),n.addMany(s,10,0,10),n.add(127,10,0,10),n.addMany(r(28,32),10,0,10),n.addMany(r(48,60),10,8,10),n.addMany([60,61,62,63],10,0,11),n.addMany(r(32,48),10,9,12),n.addMany(s,12,0,12),n.add(127,12,0,12),n.addMany(r(28,32),12,0,12),n.addMany(r(32,48),12,9,12),n.addMany(r(48,64),12,0,11),n.addMany(r(64,127),12,12,13),n.addMany(r(64,127),10,12,13),n.addMany(r(64,127),9,12,13),n.addMany(s,13,13,13),n.addMany(i,13,13,13),n.add(127,13,0,13),n.addMany([27,156,24,26],13,14,0),n.add($,0,2,0),n.add($,8,5,8),n.add($,6,0,6),n.add($,11,0,11),n.add($,13,13,13),n}(),yt=class extends A{constructor(e=Ai){super();this._transitions=e;this._parseStack={state:0,handlers:[],handlerPos:0,transition:0,chunkPos:0};this.initialState=0,this.currentState=this.initialState,this._params=new Se,this._params.addParam(0),this._collect=0,this.precedingJoinState=0,this._printHandlerFb=(r,i,s)=>{},this._executeHandlerFb=r=>{},this._csiHandlerFb=(r,i)=>{},this._escHandlerFb=r=>{},this._errorHandlerFb=r=>r,this._printHandler=this._printHandlerFb,this._executeHandlers=Object.create(null),this._csiHandlers=Object.create(null),this._escHandlers=Object.create(null),this._register(Q(()=>{this._csiHandlers=Object.create(null),this._executeHandlers=Object.create(null),this._escHandlers=Object.create(null)})),this._oscParser=this._register(new St),this._dcsParser=this._register(new Et),this._errorHandler=this._errorHandlerFb,this.registerEscHandler({final:"\\"},()=>!0)}_identifier(e,r=[64,126]){let i=0;if(e.prefix){if(e.prefix.length>1)throw new Error("only one byte as prefix supported");if(i=e.prefix.charCodeAt(0),i&&60>i||i>63)throw new Error("prefix must be in range 0x3c .. 0x3f")}if(e.intermediates){if(e.intermediates.length>2)throw new Error("only two bytes as intermediates are supported");for(let a=0;a<e.intermediates.length;++a){let l=e.intermediates.charCodeAt(a);if(32>l||l>47)throw new Error("intermediate must be in range 0x20 .. 0x2f");i<<=8,i|=l}}if(e.final.length!==1)throw new Error("final must be a single byte");let s=e.final.charCodeAt(0);if(r[0]>s||s>r[1])throw new Error(`final must be in range ${r[0]} .. ${r[1]}`);return i<<=8,i|=s,i}identToString(e){let r=[];for(;e;)r.push(String.fromCharCode(e&255)),e>>=8;return r.reverse().join("")}setPrintHandler(e){this._printHandler=e}clearPrintHandler(){this._printHandler=this._printHandlerFb}registerEscHandler(e,r){let i=this._identifier(e,[48,126]);this._escHandlers[i]===void 0&&(this._escHandlers[i]=[]);let s=this._escHandlers[i];return s.push(r),{dispose:()=>{let a=s.indexOf(r);a!==-1&&s.splice(a,1)}}}clearEscHandler(e){this._escHandlers[this._identifier(e,[48,126])]&&delete this._escHandlers[this._identifier(e,[48,126])]}setEscHandlerFallback(e){this._escHandlerFb=e}setExecuteHandler(e,r){this._executeHandlers[e.charCodeAt(0)]=r}clearExecuteHandler(e){this._executeHandlers[e.charCodeAt(0)]&&delete this._executeHandlers[e.charCodeAt(0)]}setExecuteHandlerFallback(e){this._executeHandlerFb=e}registerCsiHandler(e,r){let i=this._identifier(e);this._csiHandlers[i]===void 0&&(this._csiHandlers[i]=[]);let s=this._csiHandlers[i];return s.push(r),{dispose:()=>{let a=s.indexOf(r);a!==-1&&s.splice(a,1)}}}clearCsiHandler(e){this._csiHandlers[this._identifier(e)]&&delete this._csiHandlers[this._identifier(e)]}setCsiHandlerFallback(e){this._csiHandlerFb=e}registerDcsHandler(e,r){return this._dcsParser.registerHandler(this._identifier(e),r)}clearDcsHandler(e){this._dcsParser.clearHandler(this._identifier(e))}setDcsHandlerFallback(e){this._dcsParser.setHandlerFallback(e)}registerOscHandler(e,r){return this._oscParser.registerHandler(e,r)}clearOscHandler(e){this._oscParser.clearHandler(e)}setOscHandlerFallback(e){this._oscParser.setHandlerFallback(e)}setErrorHandler(e){this._errorHandler=e}clearErrorHandler(){this._errorHandler=this._errorHandlerFb}reset(){this.currentState=this.initialState,this._oscParser.reset(),this._dcsParser.reset(),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingJoinState=0,this._parseStack.state!==0&&(this._parseStack.state=2,this._parseStack.handlers=[])}_preserveStack(e,r,i,s,a){this._parseStack.state=e,this._parseStack.handlers=r,this._parseStack.handlerPos=i,this._parseStack.transition=s,this._parseStack.chunkPos=a}parse(e,r,i){let s=0,a=0,l=0,c;if(this._parseStack.state)if(this._parseStack.state===2)this._parseStack.state=0,l=this._parseStack.chunkPos+1;else{if(i===void 0||this._parseStack.state===1)throw this._parseStack.state=1,new Error("improper continuation due to previous async handler, giving up parsing");let u=this._parseStack.handlers,_=this._parseStack.handlerPos-1;switch(this._parseStack.state){case 3:if(i===!1&&_>-1){for(;_>=0&&(c=u[_](this._params),c!==!0);_--)if(c instanceof Promise)return this._parseStack.handlerPos=_,c}this._parseStack.handlers=[];break;case 4:if(i===!1&&_>-1){for(;_>=0&&(c=u[_](),c!==!0);_--)if(c instanceof Promise)return this._parseStack.handlerPos=_,c}this._parseStack.handlers=[];break;case 6:if(s=e[this._parseStack.chunkPos],c=this._dcsParser.unhook(s!==24&&s!==26,i),c)return c;s===27&&(this._parseStack.transition|=1),this._params.reset(),this._params.addParam(0),this._collect=0;break;case 5:if(s=e[this._parseStack.chunkPos],c=this._oscParser.end(s!==24&&s!==26,i),c)return c;s===27&&(this._parseStack.transition|=1),this._params.reset(),this._params.addParam(0),this._collect=0;break}this._parseStack.state=0,l=this._parseStack.chunkPos+1,this.precedingJoinState=0,this.currentState=this._parseStack.transition&15}for(let u=l;u<r;++u){switch(s=e[u],a=this._transitions.table[this.currentState<<8|(s<160?s:$)],a>>4){case 2:for(let m=u+1;;++m){if(m>=r||(s=e[m])<32||s>126&&s<$){this._printHandler(e,u,m),u=m-1;break}if(++m>=r||(s=e[m])<32||s>126&&s<$){this._printHandler(e,u,m),u=m-1;break}if(++m>=r||(s=e[m])<32||s>126&&s<$){this._printHandler(e,u,m),u=m-1;break}if(++m>=r||(s=e[m])<32||s>126&&s<$){this._printHandler(e,u,m),u=m-1;break}}break;case 3:this._executeHandlers[s]?this._executeHandlers[s]():this._executeHandlerFb(s),this.precedingJoinState=0;break;case 0:break;case 1:if(this._errorHandler({position:u,code:s,currentState:this.currentState,collect:this._collect,params:this._params,abort:!1}).abort)return;break;case 7:let o=this._csiHandlers[this._collect<<8|s],h=o?o.length-1:-1;for(;h>=0&&(c=o[h](this._params),c!==!0);h--)if(c instanceof Promise)return this._preserveStack(3,o,h,a,u),c;h<0&&this._csiHandlerFb(this._collect<<8|s,this._params),this.precedingJoinState=0;break;case 8:do switch(s){case 59:this._params.addParam(0);break;case 58:this._params.addSubParam(-1);break;default:this._params.addDigit(s-48)}while(++u<r&&(s=e[u])>47&&s<60);u--;break;case 9:this._collect<<=8,this._collect|=s;break;case 10:let x=this._escHandlers[this._collect<<8|s],v=x?x.length-1:-1;for(;v>=0&&(c=x[v](),c!==!0);v--)if(c instanceof Promise)return this._preserveStack(4,x,v,a,u),c;v<0&&this._escHandlerFb(this._collect<<8|s),this.precedingJoinState=0;break;case 11:this._params.reset(),this._params.addParam(0),this._collect=0;break;case 12:this._dcsParser.hook(this._collect<<8|s,this._params);break;case 13:for(let m=u+1;;++m)if(m>=r||(s=e[m])===24||s===26||s===27||s>127&&s<$){this._dcsParser.put(e,u,m),u=m-1;break}break;case 14:if(c=this._dcsParser.unhook(s!==24&&s!==26),c)return this._preserveStack(6,[],0,a,u),c;s===27&&(a|=1),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingJoinState=0;break;case 4:this._oscParser.start();break;case 5:for(let m=u+1;;m++)if(m>=r||(s=e[m])<32||s>127&&s<$){this._oscParser.put(e,u,m),u=m-1;break}break;case 6:if(c=this._oscParser.end(s!==24&&s!==26),c)return this._preserveStack(5,[],0,a,u),c;s===27&&(a|=1),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingJoinState=0;break}this.currentState=a&15}}};var Ri=/^([\da-f])\/([\da-f])\/([\da-f])$|^([\da-f]{2})\/([\da-f]{2})\/([\da-f]{2})$|^([\da-f]{3})\/([\da-f]{3})\/([\da-f]{3})$|^([\da-f]{4})\/([\da-f]{4})\/([\da-f]{4})$/,Oi=/^[\da-f]+$/;function sr(n){if(!n)return;let t=n.toLowerCase();if(t.indexOf("rgb:")===0){t=t.slice(4);let e=Ri.exec(t);if(e){let r=e[1]?15:e[4]?255:e[7]?4095:65535;return[Math.round(parseInt(e[1]||e[4]||e[7]||e[10],16)/r*255),Math.round(parseInt(e[2]||e[5]||e[8]||e[11],16)/r*255),Math.round(parseInt(e[3]||e[6]||e[9]||e[12],16)/r*255)]}}else if(t.indexOf("#")===0&&(t=t.slice(1),Oi.exec(t)&&[3,6,9,12].includes(t.length))){let e=t.length/3,r=[0,0,0];for(let i=0;i<3;++i){let s=parseInt(t.slice(e*i,e*i+e),16);r[i]=e===1?s<<4:e===2?s:e===3?s>>4:s>>8}return r}}var wi={"(":0,")":1,"*":2,"+":3,"-":1,".":2},ie=131072,qr=10;function $r(n,t){if(n>24)return t.setWinLines||!1;switch(n){case 1:return!!t.restoreWin;case 2:return!!t.minimizeWin;case 3:return!!t.setWinPosition;case 4:return!!t.setWinSizePixels;case 5:return!!t.raiseWin;case 6:return!!t.lowerWin;case 7:return!!t.refreshWin;case 8:return!!t.setWinSizeChars;case 9:return!!t.maximizeWin;case 10:return!!t.fullscreenWin;case 11:return!!t.getWinState;case 13:return!!t.getWinPosition;case 14:return!!t.getWinSizePixels;case 15:return!!t.getScreenSizePixels;case 16:return!!t.getCellSizePixels;case 18:return!!t.getWinSizeChars;case 19:return!!t.getScreenSizeChars;case 20:return!!t.getIconTitle;case 21:return!!t.getWinTitle;case 22:return!!t.pushTitle;case 23:return!!t.popTitle;case 24:return!!t.setWinLines}return!1}var jr=5e3,zr=0,Ct=class extends A{constructor(e,r,i,s,a,l,c,u,_=new yt){super();this._bufferService=e;this._charsetService=r;this._coreService=i;this._logService=s;this._optionsService=a;this._oscLinkService=l;this._coreMouseService=c;this._unicodeService=u;this._parser=_;this._parseBuffer=new Uint32Array(4096);this._stringDecoder=new Ge;this._utf8Decoder=new Ke;this._windowTitle="";this._iconName="";this._windowTitleStack=[];this._iconNameStack=[];this._curAttrData=P.clone();this._eraseAttrDataInternal=P.clone();this._onRequestBell=this._register(new S);this.onRequestBell=this._onRequestBell.event;this._onRequestRefreshRows=this._register(new S);this.onRequestRefreshRows=this._onRequestRefreshRows.event;this._onRequestReset=this._register(new S);this.onRequestReset=this._onRequestReset.event;this._onRequestSendFocus=this._register(new S);this.onRequestSendFocus=this._onRequestSendFocus.event;this._onRequestSyncScrollBar=this._register(new S);this.onRequestSyncScrollBar=this._onRequestSyncScrollBar.event;this._onRequestWindowsOptionsReport=this._register(new S);this.onRequestWindowsOptionsReport=this._onRequestWindowsOptionsReport.event;this._onA11yChar=this._register(new S);this.onA11yChar=this._onA11yChar.event;this._onA11yTab=this._register(new S);this.onA11yTab=this._onA11yTab.event;this._onCursorMove=this._register(new S);this.onCursorMove=this._onCursorMove.event;this._onLineFeed=this._register(new S);this.onLineFeed=this._onLineFeed.event;this._onScroll=this._register(new S);this.onScroll=this._onScroll.event;this._onTitleChange=this._register(new S);this.onTitleChange=this._onTitleChange.event;this._onColor=this._register(new S);this.onColor=this._onColor.event;this._parseStack={paused:!1,cursorStartX:0,cursorStartY:0,decodedLength:0,position:0};this._specialColors=[256,257,258];this._register(this._parser),this._dirtyRowTracker=new Fe(this._bufferService),this._activeBuffer=this._bufferService.buffer,this._register(this._bufferService.buffers.onBufferActivate(o=>this._activeBuffer=o.activeBuffer)),this._parser.setCsiHandlerFallback((o,h)=>{this._logService.debug("Unknown CSI code: ",{identifier:this._parser.identToString(o),params:h.toArray()})}),this._parser.setEscHandlerFallback(o=>{this._logService.debug("Unknown ESC code: ",{identifier:this._parser.identToString(o)})}),this._parser.setExecuteHandlerFallback(o=>{this._logService.debug("Unknown EXECUTE code: ",{code:o})}),this._parser.setOscHandlerFallback((o,h,x)=>{this._logService.debug("Unknown OSC code: ",{identifier:o,action:h,data:x})}),this._parser.setDcsHandlerFallback((o,h,x)=>{h==="HOOK"&&(x=x.toArray()),this._logService.debug("Unknown DCS code: ",{identifier:this._parser.identToString(o),action:h,payload:x})}),this._parser.setPrintHandler((o,h,x)=>this.print(o,h,x)),this._parser.registerCsiHandler({final:"@"},o=>this.insertChars(o)),this._parser.registerCsiHandler({intermediates:" ",final:"@"},o=>this.scrollLeft(o)),this._parser.registerCsiHandler({final:"A"},o=>this.cursorUp(o)),this._parser.registerCsiHandler({intermediates:" ",final:"A"},o=>this.scrollRight(o)),this._parser.registerCsiHandler({final:"B"},o=>this.cursorDown(o)),this._parser.registerCsiHandler({final:"C"},o=>this.cursorForward(o)),this._parser.registerCsiHandler({final:"D"},o=>this.cursorBackward(o)),this._parser.registerCsiHandler({final:"E"},o=>this.cursorNextLine(o)),this._parser.registerCsiHandler({final:"F"},o=>this.cursorPrecedingLine(o)),this._parser.registerCsiHandler({final:"G"},o=>this.cursorCharAbsolute(o)),this._parser.registerCsiHandler({final:"H"},o=>this.cursorPosition(o)),this._parser.registerCsiHandler({final:"I"},o=>this.cursorForwardTab(o)),this._parser.registerCsiHandler({final:"J"},o=>this.eraseInDisplay(o,!1)),this._parser.registerCsiHandler({prefix:"?",final:"J"},o=>this.eraseInDisplay(o,!0)),this._parser.registerCsiHandler({final:"K"},o=>this.eraseInLine(o,!1)),this._parser.registerCsiHandler({prefix:"?",final:"K"},o=>this.eraseInLine(o,!0)),this._parser.registerCsiHandler({final:"L"},o=>this.insertLines(o)),this._parser.registerCsiHandler({final:"M"},o=>this.deleteLines(o)),this._parser.registerCsiHandler({final:"P"},o=>this.deleteChars(o)),this._parser.registerCsiHandler({final:"S"},o=>this.scrollUp(o)),this._parser.registerCsiHandler({final:"T"},o=>this.scrollDown(o)),this._parser.registerCsiHandler({final:"X"},o=>this.eraseChars(o)),this._parser.registerCsiHandler({final:"Z"},o=>this.cursorBackwardTab(o)),this._parser.registerCsiHandler({final:"`"},o=>this.charPosAbsolute(o)),this._parser.registerCsiHandler({final:"a"},o=>this.hPositionRelative(o)),this._parser.registerCsiHandler({final:"b"},o=>this.repeatPrecedingCharacter(o)),this._parser.registerCsiHandler({final:"c"},o=>this.sendDeviceAttributesPrimary(o)),this._parser.registerCsiHandler({prefix:">",final:"c"},o=>this.sendDeviceAttributesSecondary(o)),this._parser.registerCsiHandler({final:"d"},o=>this.linePosAbsolute(o)),this._parser.registerCsiHandler({final:"e"},o=>this.vPositionRelative(o)),this._parser.registerCsiHandler({final:"f"},o=>this.hVPosition(o)),this._parser.registerCsiHandler({final:"g"},o=>this.tabClear(o)),this._parser.registerCsiHandler({final:"h"},o=>this.setMode(o)),this._parser.registerCsiHandler({prefix:"?",final:"h"},o=>this.setModePrivate(o)),this._parser.registerCsiHandler({final:"l"},o=>this.resetMode(o)),this._parser.registerCsiHandler({prefix:"?",final:"l"},o=>this.resetModePrivate(o)),this._parser.registerCsiHandler({final:"m"},o=>this.charAttributes(o)),this._parser.registerCsiHandler({final:"n"},o=>this.deviceStatus(o)),this._parser.registerCsiHandler({prefix:"?",final:"n"},o=>this.deviceStatusPrivate(o)),this._parser.registerCsiHandler({intermediates:"!",final:"p"},o=>this.softReset(o)),this._parser.registerCsiHandler({intermediates:" ",final:"q"},o=>this.setCursorStyle(o)),this._parser.registerCsiHandler({final:"r"},o=>this.setScrollRegion(o)),this._parser.registerCsiHandler({final:"s"},o=>this.saveCursor(o)),this._parser.registerCsiHandler({final:"t"},o=>this.windowOptions(o)),this._parser.registerCsiHandler({final:"u"},o=>this.restoreCursor(o)),this._parser.registerCsiHandler({intermediates:"'",final:"}"},o=>this.insertColumns(o)),this._parser.registerCsiHandler({intermediates:"'",final:"~"},o=>this.deleteColumns(o)),this._parser.registerCsiHandler({intermediates:'"',final:"q"},o=>this.selectProtected(o)),this._parser.registerCsiHandler({intermediates:"$",final:"p"},o=>this.requestMode(o,!0)),this._parser.registerCsiHandler({prefix:"?",intermediates:"$",final:"p"},o=>this.requestMode(o,!1)),this._parser.setExecuteHandler(k.BEL,()=>this.bell()),this._parser.setExecuteHandler(k.LF,()=>this.lineFeed()),this._parser.setExecuteHandler(k.VT,()=>this.lineFeed()),this._parser.setExecuteHandler(k.FF,()=>this.lineFeed()),this._parser.setExecuteHandler(k.CR,()=>this.carriageReturn()),this._parser.setExecuteHandler(k.BS,()=>this.backspace()),this._parser.setExecuteHandler(k.HT,()=>this.tab()),this._parser.setExecuteHandler(k.SO,()=>this.shiftOut()),this._parser.setExecuteHandler(k.SI,()=>this.shiftIn()),this._parser.setExecuteHandler(Pe.IND,()=>this.index()),this._parser.setExecuteHandler(Pe.NEL,()=>this.nextLine()),this._parser.setExecuteHandler(Pe.HTS,()=>this.tabSet()),this._parser.registerOscHandler(0,new F(o=>(this.setTitle(o),this.setIconName(o),!0))),this._parser.registerOscHandler(1,new F(o=>this.setIconName(o))),this._parser.registerOscHandler(2,new F(o=>this.setTitle(o))),this._parser.registerOscHandler(4,new F(o=>this.setOrReportIndexedColor(o))),this._parser.registerOscHandler(8,new F(o=>this.setHyperlink(o))),this._parser.registerOscHandler(10,new F(o=>this.setOrReportFgColor(o))),this._parser.registerOscHandler(11,new F(o=>this.setOrReportBgColor(o))),this._parser.registerOscHandler(12,new F(o=>this.setOrReportCursorColor(o))),this._parser.registerOscHandler(104,new F(o=>this.restoreIndexedColor(o))),this._parser.registerOscHandler(110,new F(o=>this.restoreFgColor(o))),this._parser.registerOscHandler(111,new F(o=>this.restoreBgColor(o))),this._parser.registerOscHandler(112,new F(o=>this.restoreCursorColor(o))),this._parser.registerEscHandler({final:"7"},()=>this.saveCursor()),this._parser.registerEscHandler({final:"8"},()=>this.restoreCursor()),this._parser.registerEscHandler({final:"D"},()=>this.index()),this._parser.registerEscHandler({final:"E"},()=>this.nextLine()),this._parser.registerEscHandler({final:"H"},()=>this.tabSet()),this._parser.registerEscHandler({final:"M"},()=>this.reverseIndex()),this._parser.registerEscHandler({final:"="},()=>this.keypadApplicationMode()),this._parser.registerEscHandler({final:">"},()=>this.keypadNumericMode()),this._parser.registerEscHandler({final:"c"},()=>this.fullReset()),this._parser.registerEscHandler({final:"n"},()=>this.setgLevel(2)),this._parser.registerEscHandler({final:"o"},()=>this.setgLevel(3)),this._parser.registerEscHandler({final:"|"},()=>this.setgLevel(3)),this._parser.registerEscHandler({final:"}"},()=>this.setgLevel(2)),this._parser.registerEscHandler({final:"~"},()=>this.setgLevel(1)),this._parser.registerEscHandler({intermediates:"%",final:"@"},()=>this.selectDefaultCharset()),this._parser.registerEscHandler({intermediates:"%",final:"G"},()=>this.selectDefaultCharset());for(let o in B)this._parser.registerEscHandler({intermediates:"(",final:o},()=>this.selectCharset("("+o)),this._parser.registerEscHandler({intermediates:")",final:o},()=>this.selectCharset(")"+o)),this._parser.registerEscHandler({intermediates:"*",final:o},()=>this.selectCharset("*"+o)),this._parser.registerEscHandler({intermediates:"+",final:o},()=>this.selectCharset("+"+o)),this._parser.registerEscHandler({intermediates:"-",final:o},()=>this.selectCharset("-"+o)),this._parser.registerEscHandler({intermediates:".",final:o},()=>this.selectCharset("."+o)),this._parser.registerEscHandler({intermediates:"/",final:o},()=>this.selectCharset("/"+o));this._parser.registerEscHandler({intermediates:"#",final:"8"},()=>this.screenAlignmentPattern()),this._parser.setErrorHandler(o=>(this._logService.error("Parsing error: ",o),o)),this._parser.registerDcsHandler({intermediates:"$",final:"q"},new He((o,h)=>this.requestStatusString(o,h)))}getAttrData(){return this._curAttrData}_preserveStack(e,r,i,s){this._parseStack.paused=!0,this._parseStack.cursorStartX=e,this._parseStack.cursorStartY=r,this._parseStack.decodedLength=i,this._parseStack.position=s}_logSlowResolvingAsync(e){this._logService.logLevel<=3&&Promise.race([e,new Promise((r,i)=>setTimeout(()=>i("#SLOW_TIMEOUT"),jr))]).catch(r=>{if(r!=="#SLOW_TIMEOUT")throw r;console.warn(`async parser handler taking longer than ${jr} ms`)})}_getCurrentLinkId(){return this._curAttrData.extended.urlId}parse(e,r){let i,s=this._activeBuffer.x,a=this._activeBuffer.y,l=0,c=this._parseStack.paused;if(c){if(i=this._parser.parse(this._parseBuffer,this._parseStack.decodedLength,r))return this._logSlowResolvingAsync(i),i;s=this._parseStack.cursorStartX,a=this._parseStack.cursorStartY,this._parseStack.paused=!1,e.length>ie&&(l=this._parseStack.position+ie)}if(this._logService.logLevel<=1&&this._logService.debug(`parsing data${typeof e=="string"?` "${e}"`:` "${Array.prototype.map.call(e,o=>String.fromCharCode(o)).join("")}"`}`,typeof e=="string"?e.split("").map(o=>o.charCodeAt(0)):e),this._parseBuffer.length<e.length&&this._parseBuffer.length<ie&&(this._parseBuffer=new Uint32Array(Math.min(e.length,ie))),c||this._dirtyRowTracker.clearRange(),e.length>ie)for(let o=l;o<e.length;o+=ie){let h=o+ie<e.length?o+ie:e.length,x=typeof e=="string"?this._stringDecoder.decode(e.substring(o,h),this._parseBuffer):this._utf8Decoder.decode(e.subarray(o,h),this._parseBuffer);if(i=this._parser.parse(this._parseBuffer,x))return this._preserveStack(s,a,x,o),this._logSlowResolvingAsync(i),i}else if(!c){let o=typeof e=="string"?this._stringDecoder.decode(e,this._parseBuffer):this._utf8Decoder.decode(e,this._parseBuffer);if(i=this._parser.parse(this._parseBuffer,o))return this._preserveStack(s,a,o,0),this._logSlowResolvingAsync(i),i}(this._activeBuffer.x!==s||this._activeBuffer.y!==a)&&this._onCursorMove.fire();let u=this._dirtyRowTracker.end+(this._bufferService.buffer.ybase-this._bufferService.buffer.ydisp),_=this._dirtyRowTracker.start+(this._bufferService.buffer.ybase-this._bufferService.buffer.ydisp);_<this._bufferService.rows&&this._onRequestRefreshRows.fire({start:Math.min(_,this._bufferService.rows-1),end:Math.min(u,this._bufferService.rows-1)})}print(e,r,i){let s,a,l=this._charsetService.charset,c=this._optionsService.rawOptions.screenReaderMode,u=this._bufferService.cols,_=this._coreService.decPrivateModes.wraparound,o=this._coreService.modes.insertMode,h=this._curAttrData,x=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._activeBuffer.x&&i-r>0&&x.getWidth(this._activeBuffer.x-1)===2&&x.setCellFromCodepoint(this._activeBuffer.x-1,0,1,h);let v=this._parser.precedingJoinState;for(let m=r;m<i;++m){if(s=e[m],s<127&&l){let L=l[String.fromCharCode(s)];L&&(s=L.charCodeAt(0))}let d=this._unicodeService.charProperties(s,v);a=q.extractWidth(d);let O=q.extractShouldJoin(d),w=O?q.extractWidth(v):0;if(v=d,c&&this._onA11yChar.fire(j(s)),this._getCurrentLinkId()&&this._oscLinkService.addLineToLink(this._getCurrentLinkId(),this._activeBuffer.ybase+this._activeBuffer.y),this._activeBuffer.x+a-w>u){if(_){let L=x,g=this._activeBuffer.x-w;for(this._activeBuffer.x=w,this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData(),!0)):(this._activeBuffer.y>=this._bufferService.rows&&(this._activeBuffer.y=this._bufferService.rows-1),this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).isWrapped=!0),x=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y),w>0&&x instanceof J&&x.copyCellsFrom(L,g,0,w,!1);g<u;)L.setCellFromCodepoint(g++,0,1,h)}else if(this._activeBuffer.x=u-1,a===2)continue}if(O&&this._activeBuffer.x){let L=x.getWidth(this._activeBuffer.x-1)?1:2;x.addCodepointToCell(this._activeBuffer.x-L,s,a);for(let g=a-w;--g>=0;)x.setCellFromCodepoint(this._activeBuffer.x++,0,0,h);continue}if(o&&(x.insertCells(this._activeBuffer.x,a-w,this._activeBuffer.getNullCell(h)),x.getWidth(u-1)===2&&x.setCellFromCodepoint(u-1,0,1,h)),x.setCellFromCodepoint(this._activeBuffer.x++,s,a,h),a>0)for(;--a;)x.setCellFromCodepoint(this._activeBuffer.x++,0,0,h)}this._parser.precedingJoinState=v,this._activeBuffer.x<u&&i-r>0&&x.getWidth(this._activeBuffer.x)===0&&!x.hasContent(this._activeBuffer.x)&&x.setCellFromCodepoint(this._activeBuffer.x,0,1,h),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}registerCsiHandler(e,r){return e.final==="t"&&!e.prefix&&!e.intermediates?this._parser.registerCsiHandler(e,i=>$r(i.params[0],this._optionsService.rawOptions.windowOptions)?r(i):!0):this._parser.registerCsiHandler(e,r)}registerDcsHandler(e,r){return this._parser.registerDcsHandler(e,new He(r))}registerEscHandler(e,r){return this._parser.registerEscHandler(e,r)}registerOscHandler(e,r){return this._parser.registerOscHandler(e,new F(r))}bell(){return this._onRequestBell.fire(),!0}lineFeed(){return this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._optionsService.rawOptions.convertEol&&(this._activeBuffer.x=0),this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData())):this._activeBuffer.y>=this._bufferService.rows?this._activeBuffer.y=this._bufferService.rows-1:this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).isWrapped=!1,this._activeBuffer.x>=this._bufferService.cols&&this._activeBuffer.x--,this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._onLineFeed.fire(),!0}carriageReturn(){return this._activeBuffer.x=0,!0}backspace(){if(!this._coreService.decPrivateModes.reverseWraparound)return this._restrictCursor(),this._activeBuffer.x>0&&this._activeBuffer.x--,!0;if(this._restrictCursor(this._bufferService.cols),this._activeBuffer.x>0)this._activeBuffer.x--;else if(this._activeBuffer.x===0&&this._activeBuffer.y>this._activeBuffer.scrollTop&&this._activeBuffer.y<=this._activeBuffer.scrollBottom&&this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y)?.isWrapped){this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).isWrapped=!1,this._activeBuffer.y--,this._activeBuffer.x=this._bufferService.cols-1;let e=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);e.hasWidth(this._activeBuffer.x)&&!e.hasContent(this._activeBuffer.x)&&this._activeBuffer.x--}return this._restrictCursor(),!0}tab(){if(this._activeBuffer.x>=this._bufferService.cols)return!0;let e=this._activeBuffer.x;return this._activeBuffer.x=this._activeBuffer.nextStop(),this._optionsService.rawOptions.screenReaderMode&&this._onA11yTab.fire(this._activeBuffer.x-e),!0}shiftOut(){return this._charsetService.setgLevel(1),!0}shiftIn(){return this._charsetService.setgLevel(0),!0}_restrictCursor(e=this._bufferService.cols-1){this._activeBuffer.x=Math.min(e,Math.max(0,this._activeBuffer.x)),this._activeBuffer.y=this._coreService.decPrivateModes.origin?Math.min(this._activeBuffer.scrollBottom,Math.max(this._activeBuffer.scrollTop,this._activeBuffer.y)):Math.min(this._bufferService.rows-1,Math.max(0,this._activeBuffer.y)),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}_setCursor(e,r){this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._coreService.decPrivateModes.origin?(this._activeBuffer.x=e,this._activeBuffer.y=this._activeBuffer.scrollTop+r):(this._activeBuffer.x=e,this._activeBuffer.y=r),this._restrictCursor(),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}_moveCursor(e,r){this._restrictCursor(),this._setCursor(this._activeBuffer.x+e,this._activeBuffer.y+r)}cursorUp(e){let r=this._activeBuffer.y-this._activeBuffer.scrollTop;return r>=0?this._moveCursor(0,-Math.min(r,e.params[0]||1)):this._moveCursor(0,-(e.params[0]||1)),!0}cursorDown(e){let r=this._activeBuffer.scrollBottom-this._activeBuffer.y;return r>=0?this._moveCursor(0,Math.min(r,e.params[0]||1)):this._moveCursor(0,e.params[0]||1),!0}cursorForward(e){return this._moveCursor(e.params[0]||1,0),!0}cursorBackward(e){return this._moveCursor(-(e.params[0]||1),0),!0}cursorNextLine(e){return this.cursorDown(e),this._activeBuffer.x=0,!0}cursorPrecedingLine(e){return this.cursorUp(e),this._activeBuffer.x=0,!0}cursorCharAbsolute(e){return this._setCursor((e.params[0]||1)-1,this._activeBuffer.y),!0}cursorPosition(e){return this._setCursor(e.length>=2?(e.params[1]||1)-1:0,(e.params[0]||1)-1),!0}charPosAbsolute(e){return this._setCursor((e.params[0]||1)-1,this._activeBuffer.y),!0}hPositionRelative(e){return this._moveCursor(e.params[0]||1,0),!0}linePosAbsolute(e){return this._setCursor(this._activeBuffer.x,(e.params[0]||1)-1),!0}vPositionRelative(e){return this._moveCursor(0,e.params[0]||1),!0}hVPosition(e){return this.cursorPosition(e),!0}tabClear(e){let r=e.params[0];return r===0?delete this._activeBuffer.tabs[this._activeBuffer.x]:r===3&&(this._activeBuffer.tabs={}),!0}cursorForwardTab(e){if(this._activeBuffer.x>=this._bufferService.cols)return!0;let r=e.params[0]||1;for(;r--;)this._activeBuffer.x=this._activeBuffer.nextStop();return!0}cursorBackwardTab(e){if(this._activeBuffer.x>=this._bufferService.cols)return!0;let r=e.params[0]||1;for(;r--;)this._activeBuffer.x=this._activeBuffer.prevStop();return!0}selectProtected(e){let r=e.params[0];return r===1&&(this._curAttrData.bg|=536870912),(r===2||r===0)&&(this._curAttrData.bg&=-536870913),!0}_eraseInBufferLine(e,r,i,s=!1,a=!1){let l=this._activeBuffer.lines.get(this._activeBuffer.ybase+e);l.replaceCells(r,i,this._activeBuffer.getNullCell(this._eraseAttrData()),a),s&&(l.isWrapped=!1)}_resetBufferLine(e,r=!1){let i=this._activeBuffer.lines.get(this._activeBuffer.ybase+e);i&&(i.fill(this._activeBuffer.getNullCell(this._eraseAttrData()),r),this._bufferService.buffer.clearMarkers(this._activeBuffer.ybase+e),i.isWrapped=!1)}eraseInDisplay(e,r=!1){this._restrictCursor(this._bufferService.cols);let i;switch(e.params[0]){case 0:for(i=this._activeBuffer.y,this._dirtyRowTracker.markDirty(i),this._eraseInBufferLine(i++,this._activeBuffer.x,this._bufferService.cols,this._activeBuffer.x===0,r);i<this._bufferService.rows;i++)this._resetBufferLine(i,r);this._dirtyRowTracker.markDirty(i);break;case 1:for(i=this._activeBuffer.y,this._dirtyRowTracker.markDirty(i),this._eraseInBufferLine(i,0,this._activeBuffer.x+1,!0,r),this._activeBuffer.x+1>=this._bufferService.cols&&(this._activeBuffer.lines.get(i+1).isWrapped=!1);i--;)this._resetBufferLine(i,r);this._dirtyRowTracker.markDirty(0);break;case 2:if(this._optionsService.rawOptions.scrollOnEraseInDisplay){for(i=this._bufferService.rows,this._dirtyRowTracker.markRangeDirty(0,i-1);i--&&!this._activeBuffer.lines.get(this._activeBuffer.ybase+i)?.getTrimmedLength(););for(;i>=0;i--)this._bufferService.scroll(this._eraseAttrData())}else{for(i=this._bufferService.rows,this._dirtyRowTracker.markDirty(i-1);i--;)this._resetBufferLine(i,r);this._dirtyRowTracker.markDirty(0)}break;case 3:let s=this._activeBuffer.lines.length-this._bufferService.rows;s>0&&(this._activeBuffer.lines.trimStart(s),this._activeBuffer.ybase=Math.max(this._activeBuffer.ybase-s,0),this._activeBuffer.ydisp=Math.max(this._activeBuffer.ydisp-s,0),this._onScroll.fire(0));break}return!0}eraseInLine(e,r=!1){switch(this._restrictCursor(this._bufferService.cols),e.params[0]){case 0:this._eraseInBufferLine(this._activeBuffer.y,this._activeBuffer.x,this._bufferService.cols,this._activeBuffer.x===0,r);break;case 1:this._eraseInBufferLine(this._activeBuffer.y,0,this._activeBuffer.x+1,!1,r);break;case 2:this._eraseInBufferLine(this._activeBuffer.y,0,this._bufferService.cols,!0,r);break}return this._dirtyRowTracker.markDirty(this._activeBuffer.y),!0}insertLines(e){this._restrictCursor();let r=e.params[0]||1;if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;let i=this._activeBuffer.ybase+this._activeBuffer.y,s=this._bufferService.rows-1-this._activeBuffer.scrollBottom,a=this._bufferService.rows-1+this._activeBuffer.ybase-s+1;for(;r--;)this._activeBuffer.lines.splice(a-1,1),this._activeBuffer.lines.splice(i,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.y,this._activeBuffer.scrollBottom),this._activeBuffer.x=0,!0}deleteLines(e){this._restrictCursor();let r=e.params[0]||1;if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;let i=this._activeBuffer.ybase+this._activeBuffer.y,s;for(s=this._bufferService.rows-1-this._activeBuffer.scrollBottom,s=this._bufferService.rows-1+this._activeBuffer.ybase-s;r--;)this._activeBuffer.lines.splice(i,1),this._activeBuffer.lines.splice(s,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.y,this._activeBuffer.scrollBottom),this._activeBuffer.x=0,!0}insertChars(e){this._restrictCursor();let r=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return r&&(r.insertCells(this._activeBuffer.x,e.params[0]||1,this._activeBuffer.getNullCell(this._eraseAttrData())),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}deleteChars(e){this._restrictCursor();let r=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return r&&(r.deleteCells(this._activeBuffer.x,e.params[0]||1,this._activeBuffer.getNullCell(this._eraseAttrData())),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}scrollUp(e){let r=e.params[0]||1;for(;r--;)this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollTop,1),this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollBottom,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollDown(e){let r=e.params[0]||1;for(;r--;)this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollBottom,1),this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollTop,0,this._activeBuffer.getBlankLine(P));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollLeft(e){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;let r=e.params[0]||1;for(let i=this._activeBuffer.scrollTop;i<=this._activeBuffer.scrollBottom;++i){let s=this._activeBuffer.lines.get(this._activeBuffer.ybase+i);s.deleteCells(0,r,this._activeBuffer.getNullCell(this._eraseAttrData())),s.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollRight(e){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;let r=e.params[0]||1;for(let i=this._activeBuffer.scrollTop;i<=this._activeBuffer.scrollBottom;++i){let s=this._activeBuffer.lines.get(this._activeBuffer.ybase+i);s.insertCells(0,r,this._activeBuffer.getNullCell(this._eraseAttrData())),s.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}insertColumns(e){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;let r=e.params[0]||1;for(let i=this._activeBuffer.scrollTop;i<=this._activeBuffer.scrollBottom;++i){let s=this._activeBuffer.lines.get(this._activeBuffer.ybase+i);s.insertCells(this._activeBuffer.x,r,this._activeBuffer.getNullCell(this._eraseAttrData())),s.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}deleteColumns(e){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;let r=e.params[0]||1;for(let i=this._activeBuffer.scrollTop;i<=this._activeBuffer.scrollBottom;++i){let s=this._activeBuffer.lines.get(this._activeBuffer.ybase+i);s.deleteCells(this._activeBuffer.x,r,this._activeBuffer.getNullCell(this._eraseAttrData())),s.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}eraseChars(e){this._restrictCursor();let r=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return r&&(r.replaceCells(this._activeBuffer.x,this._activeBuffer.x+(e.params[0]||1),this._activeBuffer.getNullCell(this._eraseAttrData())),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}repeatPrecedingCharacter(e){let r=this._parser.precedingJoinState;if(!r)return!0;let i=e.params[0]||1,s=q.extractWidth(r),a=this._activeBuffer.x-s,c=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).getString(a),u=new Uint32Array(c.length*i),_=0;for(let h=0;h<c.length;){let x=c.codePointAt(h)||0;u[_++]=x,h+=x>65535?2:1}let o=_;for(let h=1;h<i;++h)u.copyWithin(o,0,_),o+=_;return this.print(u,0,o),!0}sendDeviceAttributesPrimary(e){return e.params[0]>0||(this._is("xterm")||this._is("rxvt-unicode")||this._is("screen")?this._coreService.triggerDataEvent(k.ESC+"[?1;2c"):this._is("linux")&&this._coreService.triggerDataEvent(k.ESC+"[?6c")),!0}sendDeviceAttributesSecondary(e){return e.params[0]>0||(this._is("xterm")?this._coreService.triggerDataEvent(k.ESC+"[>0;276;0c"):this._is("rxvt-unicode")?this._coreService.triggerDataEvent(k.ESC+"[>85;95;0c"):this._is("linux")?this._coreService.triggerDataEvent(e.params[0]+"c"):this._is("screen")&&this._coreService.triggerDataEvent(k.ESC+"[>83;40003;0c")),!0}_is(e){return(this._optionsService.rawOptions.termName+"").indexOf(e)===0}setMode(e){for(let r=0;r<e.length;r++)switch(e.params[r]){case 4:this._coreService.modes.insertMode=!0;break;case 20:this._optionsService.options.convertEol=!0;break}return!0}setModePrivate(e){for(let r=0;r<e.length;r++)switch(e.params[r]){case 1:this._coreService.decPrivateModes.applicationCursorKeys=!0;break;case 2:this._charsetService.setgCharset(0,Z),this._charsetService.setgCharset(1,Z),this._charsetService.setgCharset(2,Z),this._charsetService.setgCharset(3,Z);break;case 3:this._optionsService.rawOptions.windowOptions.setWinLines&&(this._bufferService.resize(132,this._bufferService.rows),this._onRequestReset.fire());break;case 6:this._coreService.decPrivateModes.origin=!0,this._setCursor(0,0);break;case 7:this._coreService.decPrivateModes.wraparound=!0;break;case 12:this._optionsService.options.cursorBlink=!0;break;case 45:this._coreService.decPrivateModes.reverseWraparound=!0;break;case 66:this._logService.debug("Serial port requested application keypad."),this._coreService.decPrivateModes.applicationKeypad=!0,this._onRequestSyncScrollBar.fire();break;case 9:this._coreMouseService.activeProtocol="X10";break;case 1e3:this._coreMouseService.activeProtocol="VT200";break;case 1002:this._coreMouseService.activeProtocol="DRAG";break;case 1003:this._coreMouseService.activeProtocol="ANY";break;case 1004:this._coreService.decPrivateModes.sendFocus=!0,this._onRequestSendFocus.fire();break;case 1005:this._logService.debug("DECSET 1005 not supported (see #2507)");break;case 1006:this._coreMouseService.activeEncoding="SGR";break;case 1015:this._logService.debug("DECSET 1015 not supported (see #2507)");break;case 1016:this._coreMouseService.activeEncoding="SGR_PIXELS";break;case 25:this._coreService.isCursorHidden=!1;break;case 1048:this.saveCursor();break;case 1049:this.saveCursor();case 47:case 1047:this._bufferService.buffers.activateAltBuffer(this._eraseAttrData()),this._coreService.isCursorInitialized=!0,this._onRequestRefreshRows.fire(void 0),this._onRequestSyncScrollBar.fire();break;case 2004:this._coreService.decPrivateModes.bracketedPasteMode=!0;break}return!0}resetMode(e){for(let r=0;r<e.length;r++)switch(e.params[r]){case 4:this._coreService.modes.insertMode=!1;break;case 20:this._optionsService.options.convertEol=!1;break}return!0}resetModePrivate(e){for(let r=0;r<e.length;r++)switch(e.params[r]){case 1:this._coreService.decPrivateModes.applicationCursorKeys=!1;break;case 3:this._optionsService.rawOptions.windowOptions.setWinLines&&(this._bufferService.resize(80,this._bufferService.rows),this._onRequestReset.fire());break;case 6:this._coreService.decPrivateModes.origin=!1,this._setCursor(0,0);break;case 7:this._coreService.decPrivateModes.wraparound=!1;break;case 12:this._optionsService.options.cursorBlink=!1;break;case 45:this._coreService.decPrivateModes.reverseWraparound=!1;break;case 66:this._logService.debug("Switching back to normal keypad."),this._coreService.decPrivateModes.applicationKeypad=!1,this._onRequestSyncScrollBar.fire();break;case 9:case 1e3:case 1002:case 1003:this._coreMouseService.activeProtocol="NONE";break;case 1004:this._coreService.decPrivateModes.sendFocus=!1;break;case 1005:this._logService.debug("DECRST 1005 not supported (see #2507)");break;case 1006:this._coreMouseService.activeEncoding="DEFAULT";break;case 1015:this._logService.debug("DECRST 1015 not supported (see #2507)");break;case 1016:this._coreMouseService.activeEncoding="DEFAULT";break;case 25:this._coreService.isCursorHidden=!0;break;case 1048:this.restoreCursor();break;case 1049:case 47:case 1047:this._bufferService.buffers.activateNormalBuffer(),e.params[r]===1049&&this.restoreCursor(),this._coreService.isCursorInitialized=!0,this._onRequestRefreshRows.fire(void 0),this._onRequestSyncScrollBar.fire();break;case 2004:this._coreService.decPrivateModes.bracketedPasteMode=!1;break}return!0}requestMode(e,r){let i;(E=>(E[E.NOT_RECOGNIZED=0]="NOT_RECOGNIZED",E[E.SET=1]="SET",E[E.RESET=2]="RESET",E[E.PERMANENTLY_SET=3]="PERMANENTLY_SET",E[E.PERMANENTLY_RESET=4]="PERMANENTLY_RESET"))(i||={});let s=this._coreService.decPrivateModes,{activeProtocol:a,activeEncoding:l}=this._coreMouseService,c=this._coreService,{buffers:u,cols:_}=this._bufferService,{active:o,alt:h}=u,x=this._optionsService.rawOptions,v=(O,w)=>(c.triggerDataEvent(`${k.ESC}[${r?"":"?"}${O};${w}$y`),!0),m=O=>O?1:2,d=e.params[0];return r?d===2?v(d,4):d===4?v(d,m(c.modes.insertMode)):d===12?v(d,3):d===20?v(d,m(x.convertEol)):v(d,0):d===1?v(d,m(s.applicationCursorKeys)):d===3?v(d,x.windowOptions.setWinLines?_===80?2:_===132?1:0:0):d===6?v(d,m(s.origin)):d===7?v(d,m(s.wraparound)):d===8?v(d,3):d===9?v(d,m(a==="X10")):d===12?v(d,m(x.cursorBlink)):d===25?v(d,m(!c.isCursorHidden)):d===45?v(d,m(s.reverseWraparound)):d===66?v(d,m(s.applicationKeypad)):d===67?v(d,4):d===1e3?v(d,m(a==="VT200")):d===1002?v(d,m(a==="DRAG")):d===1003?v(d,m(a==="ANY")):d===1004?v(d,m(s.sendFocus)):d===1005?v(d,4):d===1006?v(d,m(l==="SGR")):d===1015?v(d,4):d===1016?v(d,m(l==="SGR_PIXELS")):d===1048?v(d,1):d===47||d===1047||d===1049?v(d,m(o===h)):d===2004?v(d,m(s.bracketedPasteMode)):v(d,0)}_updateAttrColor(e,r,i,s,a){return r===2?(e|=50331648,e&=-16777216,e|=ee.fromColorRGB([i,s,a])):r===5&&(e&=-50331904,e|=33554432|i&255),e}_extractColor(e,r,i){let s=[0,0,-1,0,0,0],a=0,l=0;do{if(s[l+a]=e.params[r+l],e.hasSubParams(r+l)){let c=e.getSubParams(r+l),u=0;do s[1]===5&&(a=1),s[l+u+1+a]=c[u];while(++u<c.length&&u+l+1+a<s.length);break}if(s[1]===5&&l+a>=2||s[1]===2&&l+a>=5)break;s[1]&&(a=1)}while(++l+r<e.length&&l+a<s.length);for(let c=2;c<s.length;++c)s[c]===-1&&(s[c]=0);switch(s[0]){case 38:i.fg=this._updateAttrColor(i.fg,s[1],s[3],s[4],s[5]);break;case 48:i.bg=this._updateAttrColor(i.bg,s[1],s[3],s[4],s[5]);break;case 58:i.extended=i.extended.clone(),i.extended.underlineColor=this._updateAttrColor(i.extended.underlineColor,s[1],s[3],s[4],s[5])}return l}_processUnderline(e,r){r.extended=r.extended.clone(),(!~e||e>5)&&(e=1),r.extended.underlineStyle=e,r.fg|=268435456,e===0&&(r.fg&=-268435457),r.updateExtended()}_processSGR0(e){e.fg=P.fg,e.bg=P.bg,e.extended=e.extended.clone(),e.extended.underlineStyle=0,e.extended.underlineColor&=-67108864,e.updateExtended()}charAttributes(e){if(e.length===1&&e.params[0]===0)return this._processSGR0(this._curAttrData),!0;let r=e.length,i,s=this._curAttrData;for(let a=0;a<r;a++)i=e.params[a],i>=30&&i<=37?(s.fg&=-50331904,s.fg|=16777216|i-30):i>=40&&i<=47?(s.bg&=-50331904,s.bg|=16777216|i-40):i>=90&&i<=97?(s.fg&=-50331904,s.fg|=16777216|i-90|8):i>=100&&i<=107?(s.bg&=-50331904,s.bg|=16777216|i-100|8):i===0?this._processSGR0(s):i===1?s.fg|=134217728:i===3?s.bg|=67108864:i===4?(s.fg|=268435456,this._processUnderline(e.hasSubParams(a)?e.getSubParams(a)[0]:1,s)):i===5?s.fg|=536870912:i===7?s.fg|=67108864:i===8?s.fg|=1073741824:i===9?s.fg|=2147483648:i===2?s.bg|=134217728:i===21?this._processUnderline(2,s):i===22?(s.fg&=-134217729,s.bg&=-134217729):i===23?s.bg&=-67108865:i===24?(s.fg&=-268435457,this._processUnderline(0,s)):i===25?s.fg&=-536870913:i===27?s.fg&=-67108865:i===28?s.fg&=-1073741825:i===29?s.fg&=2147483647:i===39?(s.fg&=-67108864,s.fg|=P.fg&16777215):i===49?(s.bg&=-67108864,s.bg|=P.bg&16777215):i===38||i===48||i===58?a+=this._extractColor(e,a,s):i===53?s.bg|=1073741824:i===55?s.bg&=-1073741825:i===59?(s.extended=s.extended.clone(),s.extended.underlineColor=-1,s.updateExtended()):i===100?(s.fg&=-67108864,s.fg|=P.fg&16777215,s.bg&=-67108864,s.bg|=P.bg&16777215):this._logService.debug("Unknown SGR attribute: %d.",i);return!0}deviceStatus(e){switch(e.params[0]){case 5:this._coreService.triggerDataEvent(`${k.ESC}[0n`);break;case 6:let r=this._activeBuffer.y+1,i=this._activeBuffer.x+1;this._coreService.triggerDataEvent(`${k.ESC}[${r};${i}R`);break}return!0}deviceStatusPrivate(e){switch(e.params[0]){case 6:let r=this._activeBuffer.y+1,i=this._activeBuffer.x+1;this._coreService.triggerDataEvent(`${k.ESC}[?${r};${i}R`);break;case 15:break;case 25:break;case 26:break;case 53:break}return!0}softReset(e){return this._coreService.isCursorHidden=!1,this._onRequestSyncScrollBar.fire(),this._activeBuffer.scrollTop=0,this._activeBuffer.scrollBottom=this._bufferService.rows-1,this._curAttrData=P.clone(),this._coreService.reset(),this._charsetService.reset(),this._activeBuffer.savedX=0,this._activeBuffer.savedY=this._activeBuffer.ybase,this._activeBuffer.savedCurAttrData.fg=this._curAttrData.fg,this._activeBuffer.savedCurAttrData.bg=this._curAttrData.bg,this._activeBuffer.savedCharset=this._charsetService.charset,this._coreService.decPrivateModes.origin=!1,!0}setCursorStyle(e){let r=e.length===0?1:e.params[0];if(r===0)this._coreService.decPrivateModes.cursorStyle=void 0,this._coreService.decPrivateModes.cursorBlink=void 0;else{switch(r){case 1:case 2:this._coreService.decPrivateModes.cursorStyle="block";break;case 3:case 4:this._coreService.decPrivateModes.cursorStyle="underline";break;case 5:case 6:this._coreService.decPrivateModes.cursorStyle="bar";break}let i=r%2===1;this._coreService.decPrivateModes.cursorBlink=i}return!0}setScrollRegion(e){let r=e.params[0]||1,i;return(e.length<2||(i=e.params[1])>this._bufferService.rows||i===0)&&(i=this._bufferService.rows),i>r&&(this._activeBuffer.scrollTop=r-1,this._activeBuffer.scrollBottom=i-1,this._setCursor(0,0)),!0}windowOptions(e){if(!$r(e.params[0],this._optionsService.rawOptions.windowOptions))return!0;let r=e.length>1?e.params[1]:0;switch(e.params[0]){case 14:r!==2&&this._onRequestWindowsOptionsReport.fire(0);break;case 16:this._onRequestWindowsOptionsReport.fire(1);break;case 18:this._bufferService&&this._coreService.triggerDataEvent(`${k.ESC}[8;${this._bufferService.rows};${this._bufferService.cols}t`);break;case 22:(r===0||r===2)&&(this._windowTitleStack.push(this._windowTitle),this._windowTitleStack.length>qr&&this._windowTitleStack.shift()),(r===0||r===1)&&(this._iconNameStack.push(this._iconName),this._iconNameStack.length>qr&&this._iconNameStack.shift());break;case 23:(r===0||r===2)&&this._windowTitleStack.length&&this.setTitle(this._windowTitleStack.pop()),(r===0||r===1)&&this._iconNameStack.length&&this.setIconName(this._iconNameStack.pop());break}return!0}saveCursor(e){return this._activeBuffer.savedX=this._activeBuffer.x,this._activeBuffer.savedY=this._activeBuffer.ybase+this._activeBuffer.y,this._activeBuffer.savedCurAttrData.fg=this._curAttrData.fg,this._activeBuffer.savedCurAttrData.bg=this._curAttrData.bg,this._activeBuffer.savedCharset=this._charsetService.charset,!0}restoreCursor(e){return this._activeBuffer.x=this._activeBuffer.savedX||0,this._activeBuffer.y=Math.max(this._activeBuffer.savedY-this._activeBuffer.ybase,0),this._curAttrData.fg=this._activeBuffer.savedCurAttrData.fg,this._curAttrData.bg=this._activeBuffer.savedCurAttrData.bg,this._charsetService.charset=this._savedCharset,this._activeBuffer.savedCharset&&(this._charsetService.charset=this._activeBuffer.savedCharset),this._restrictCursor(),!0}setTitle(e){return this._windowTitle=e,this._onTitleChange.fire(e),!0}setIconName(e){return this._iconName=e,!0}setOrReportIndexedColor(e){let r=[],i=e.split(";");for(;i.length>1;){let s=i.shift(),a=i.shift();if(/^\d+$/.exec(s)){let l=parseInt(s);if(Xr(l))if(a==="?")r.push({type:0,index:l});else{let c=sr(a);c&&r.push({type:1,index:l,color:c})}}}return r.length&&this._onColor.fire(r),!0}setHyperlink(e){let r=e.indexOf(";");if(r===-1)return!0;let i=e.slice(0,r).trim(),s=e.slice(r+1);return s?this._createHyperlink(i,s):i.trim()?!1:this._finishHyperlink()}_createHyperlink(e,r){this._getCurrentLinkId()&&this._finishHyperlink();let i=e.split(":"),s,a=i.findIndex(l=>l.startsWith("id="));return a!==-1&&(s=i[a].slice(3)||void 0),this._curAttrData.extended=this._curAttrData.extended.clone(),this._curAttrData.extended.urlId=this._oscLinkService.registerLink({id:s,uri:r}),this._curAttrData.updateExtended(),!0}_finishHyperlink(){return this._curAttrData.extended=this._curAttrData.extended.clone(),this._curAttrData.extended.urlId=0,this._curAttrData.updateExtended(),!0}_setOrReportSpecialColor(e,r){let i=e.split(";");for(let s=0;s<i.length&&!(r>=this._specialColors.length);++s,++r)if(i[s]==="?")this._onColor.fire([{type:0,index:this._specialColors[r]}]);else{let a=sr(i[s]);a&&this._onColor.fire([{type:1,index:this._specialColors[r],color:a}])}return!0}setOrReportFgColor(e){return this._setOrReportSpecialColor(e,0)}setOrReportBgColor(e){return this._setOrReportSpecialColor(e,1)}setOrReportCursorColor(e){return this._setOrReportSpecialColor(e,2)}restoreIndexedColor(e){if(!e)return this._onColor.fire([{type:2}]),!0;let r=[],i=e.split(";");for(let s=0;s<i.length;++s)if(/^\d+$/.exec(i[s])){let a=parseInt(i[s]);Xr(a)&&r.push({type:2,index:a})}return r.length&&this._onColor.fire(r),!0}restoreFgColor(e){return this._onColor.fire([{type:2,index:256}]),!0}restoreBgColor(e){return this._onColor.fire([{type:2,index:257}]),!0}restoreCursorColor(e){return this._onColor.fire([{type:2,index:258}]),!0}nextLine(){return this._activeBuffer.x=0,this.index(),!0}keypadApplicationMode(){return this._logService.debug("Serial port requested application keypad."),this._coreService.decPrivateModes.applicationKeypad=!0,this._onRequestSyncScrollBar.fire(),!0}keypadNumericMode(){return this._logService.debug("Switching back to normal keypad."),this._coreService.decPrivateModes.applicationKeypad=!1,this._onRequestSyncScrollBar.fire(),!0}selectDefaultCharset(){return this._charsetService.setgLevel(0),this._charsetService.setgCharset(0,Z),!0}selectCharset(e){return e.length!==2?(this.selectDefaultCharset(),!0):(e[0]==="/"||this._charsetService.setgCharset(wi[e[0]],B[e[1]]||Z),!0)}index(){return this._restrictCursor(),this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData())):this._activeBuffer.y>=this._bufferService.rows&&(this._activeBuffer.y=this._bufferService.rows-1),this._restrictCursor(),!0}tabSet(){return this._activeBuffer.tabs[this._activeBuffer.x]=!0,!0}reverseIndex(){if(this._restrictCursor(),this._activeBuffer.y===this._activeBuffer.scrollTop){let e=this._activeBuffer.scrollBottom-this._activeBuffer.scrollTop;this._activeBuffer.lines.shiftElements(this._activeBuffer.ybase+this._activeBuffer.y,e,1),this._activeBuffer.lines.set(this._activeBuffer.ybase+this._activeBuffer.y,this._activeBuffer.getBlankLine(this._eraseAttrData())),this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom)}else this._activeBuffer.y--,this._restrictCursor();return!0}fullReset(){return this._parser.reset(),this._onRequestReset.fire(),!0}reset(){this._curAttrData=P.clone(),this._eraseAttrDataInternal=P.clone()}_eraseAttrData(){return this._eraseAttrDataInternal.bg&=-67108864,this._eraseAttrDataInternal.bg|=this._curAttrData.bg&67108863,this._eraseAttrDataInternal}setgLevel(e){return this._charsetService.setgLevel(e),!0}screenAlignmentPattern(){let e=new G;e.content=1<<22|69,e.fg=this._curAttrData.fg,e.bg=this._curAttrData.bg,this._setCursor(0,0);for(let r=0;r<this._bufferService.rows;++r){let i=this._activeBuffer.ybase+this._activeBuffer.y+r,s=this._activeBuffer.lines.get(i);s&&(s.fill(e),s.isWrapped=!1)}return this._dirtyRowTracker.markAllDirty(),this._setCursor(0,0),!0}requestStatusString(e,r){let i=c=>(this._coreService.triggerDataEvent(`${k.ESC}${c}${k.ESC}\\`),!0),s=this._bufferService.buffer,a=this._optionsService.rawOptions,l={block:2,underline:4,bar:6};return i(e==='"q'?`P1$r${this._curAttrData.isProtected()?1:0}"q`:e==='"p'?'P1$r61;1"p':e==="r"?`P1$r${s.scrollTop+1};${s.scrollBottom+1}r`:e==="m"?"P1$r0m":e===" q"?`P1$r${l[a.cursorStyle]-(a.cursorBlink?1:0)} q`:"P0$r")}markRangeDirty(e,r){this._dirtyRowTracker.markRangeDirty(e,r)}},Fe=class{constructor(t){this._bufferService=t;this.clearRange()}clearRange(){this.start=this._bufferService.buffer.y,this.end=this._bufferService.buffer.y}markDirty(t){t<this.start?this.start=t:t>this.end&&(this.end=t)}markRangeDirty(t,e){t>e&&(zr=t,t=e,e=zr),t<this.start&&(this.start=t),e>this.end&&(this.end=e)}markAllDirty(){this.markRangeDirty(0,this._bufferService.rows-1)}};Fe=K([W(0,X)],Fe);function Xr(n){return 0<=n&&n<256}var ki=5e7,Yr=12,Pi=50,Dt=class extends A{constructor(e){super();this._action=e;this._writeBuffer=[];this._callbacks=[];this._pendingData=0;this._bufferOffset=0;this._isSyncWriting=!1;this._syncCalls=0;this._didUserInput=!1;this._onWriteParsed=this._register(new S);this.onWriteParsed=this._onWriteParsed.event}handleUserInput(){this._didUserInput=!0}writeSync(e,r){if(r!==void 0&&this._syncCalls>r){this._syncCalls=0;return}if(this._pendingData+=e.length,this._writeBuffer.push(e),this._callbacks.push(void 0),this._syncCalls++,this._isSyncWriting)return;this._isSyncWriting=!0;let i;for(;i=this._writeBuffer.shift();){this._action(i);let s=this._callbacks.shift();s&&s()}this._pendingData=0,this._bufferOffset=2147483647,this._isSyncWriting=!1,this._syncCalls=0}write(e,r){if(this._pendingData>ki)throw new Error("write data discarded, use flow control to avoid losing data");if(!this._writeBuffer.length){if(this._bufferOffset=0,this._didUserInput){this._didUserInput=!1,this._pendingData+=e.length,this._writeBuffer.push(e),this._callbacks.push(r),this._innerWrite();return}setTimeout(()=>this._innerWrite())}this._pendingData+=e.length,this._writeBuffer.push(e),this._callbacks.push(r)}_innerWrite(e=0,r=!0){let i=e||Date.now();for(;this._writeBuffer.length>this._bufferOffset;){let s=this._writeBuffer[this._bufferOffset],a=this._action(s,r);if(a){let c=u=>Date.now()-i>=Yr?setTimeout(()=>this._innerWrite(0,u)):this._innerWrite(i,u);a.catch(u=>(queueMicrotask(()=>{throw u}),Promise.resolve(!1))).then(c);return}let l=this._callbacks[this._bufferOffset];if(l&&l(),this._bufferOffset++,this._pendingData-=s.length,Date.now()-i>=Yr)break}this._writeBuffer.length>this._bufferOffset?(this._bufferOffset>Pi&&(this._writeBuffer=this._writeBuffer.slice(this._bufferOffset),this._callbacks=this._callbacks.slice(this._bufferOffset),this._bufferOffset=0),setTimeout(()=>this._innerWrite())):(this._writeBuffer.length=0,this._callbacks.length=0,this._pendingData=0,this._bufferOffset=0),this._onWriteParsed.fire()}};var Ee=class{constructor(t){this._bufferService=t;this._nextId=1;this._entriesWithId=new Map;this._dataByLinkId=new Map}registerLink(t){let e=this._bufferService.buffer;if(t.id===void 0){let c=e.addMarker(e.ybase+e.y),u={data:t,id:this._nextId++,lines:[c]};return c.onDispose(()=>this._removeMarkerFromLink(u,c)),this._dataByLinkId.set(u.id,u),u.id}let r=t,i=this._getEntryIdKey(r),s=this._entriesWithId.get(i);if(s)return this.addLineToLink(s.id,e.ybase+e.y),s.id;let a=e.addMarker(e.ybase+e.y),l={id:this._nextId++,key:this._getEntryIdKey(r),data:r,lines:[a]};return a.onDispose(()=>this._removeMarkerFromLink(l,a)),this._entriesWithId.set(l.key,l),this._dataByLinkId.set(l.id,l),l.id}addLineToLink(t,e){let r=this._dataByLinkId.get(t);if(r&&r.lines.every(i=>i.line!==e)){let i=this._bufferService.buffer.addMarker(e);r.lines.push(i),i.onDispose(()=>this._removeMarkerFromLink(r,i))}}getLinkData(t){return this._dataByLinkId.get(t)?.data}_getEntryIdKey(t){return`${t.id};;${t.uri}`}_removeMarkerFromLink(t,e){let r=t.lines.indexOf(e);r!==-1&&(t.lines.splice(r,1),t.lines.length===0&&(t.data.id!==void 0&&this._entriesWithId.delete(t.key),this._dataByLinkId.delete(t.id)))}};Ee=K([W(0,X)],Ee);var Qr=!1,At=class extends A{constructor(e){super();this._windowsWrappingHeuristics=this._register(new Je);this._onBinary=this._register(new S);this.onBinary=this._onBinary.event;this._onData=this._register(new S);this.onData=this._onData.event;this._onLineFeed=this._register(new S);this.onLineFeed=this._onLineFeed.event;this._onResize=this._register(new S);this.onResize=this._onResize.event;this._onWriteParsed=this._register(new S);this.onWriteParsed=this._onWriteParsed.event;this._onScroll=this._register(new S);this._instantiationService=new ft,this.optionsService=this._register(new It(e)),this._instantiationService.setService(re,this.optionsService),this._bufferService=this._register(this._instantiationService.createInstance(ve)),this._instantiationService.setService(X,this._bufferService),this._logService=this._register(this._instantiationService.createInstance(_e)),this._instantiationService.setService(ht,this._logService),this.coreService=this._register(this._instantiationService.createInstance(xe)),this._instantiationService.setService(dt,this.coreService),this.coreMouseService=this._register(this._instantiationService.createInstance(Te)),this._instantiationService.setService(Dr,this.coreMouseService),this.unicodeService=this._register(this._instantiationService.createInstance(q)),this._instantiationService.setService(wr,this.unicodeService),this._charsetService=this._instantiationService.createInstance(Tt),this._instantiationService.setService(Ar,this._charsetService),this._oscLinkService=this._instantiationService.createInstance(Ee),this._instantiationService.setService(Or,this._oscLinkService),this._inputHandler=this._register(new Ct(this._bufferService,this._charsetService,this.coreService,this._logService,this.optionsService,this._oscLinkService,this.coreMouseService,this.unicodeService)),this._register(V.forward(this._inputHandler.onLineFeed,this._onLineFeed)),this._register(this._inputHandler),this._register(V.forward(this._bufferService.onResize,this._onResize)),this._register(V.forward(this.coreService.onData,this._onData)),this._register(V.forward(this.coreService.onBinary,this._onBinary)),this._register(this.coreService.onRequestScrollToBottom(()=>this.scrollToBottom(!0))),this._register(this.coreService.onUserInput(()=>this._writeBuffer.handleUserInput())),this._register(this.optionsService.onMultipleOptionChange(["windowsMode","windowsPty"],()=>this._handleWindowsPtyOptionChange())),this._register(this._bufferService.onScroll(()=>{this._onScroll.fire({position:this._bufferService.buffer.ydisp}),this._inputHandler.markRangeDirty(this._bufferService.buffer.scrollTop,this._bufferService.buffer.scrollBottom)})),this._writeBuffer=this._register(new Dt((r,i)=>this._inputHandler.parse(r,i))),this._register(V.forward(this._writeBuffer.onWriteParsed,this._onWriteParsed))}get onScroll(){return this._onScrollApi||(this._onScrollApi=this._register(new S),this._onScroll.event(e=>{this._onScrollApi?.fire(e.position)})),this._onScrollApi.event}get cols(){return this._bufferService.cols}get rows(){return this._bufferService.rows}get buffers(){return this._bufferService.buffers}get options(){return this.optionsService.options}set options(e){for(let r in e)this.optionsService.options[r]=e[r]}write(e,r){this._writeBuffer.write(e,r)}writeSync(e,r){this._logService.logLevel<=3&&!Qr&&(this._logService.warn("writeSync is unreliable and will be removed soon."),Qr=!0),this._writeBuffer.writeSync(e,r)}input(e,r=!0){this.coreService.triggerDataEvent(e,r)}resize(e,r){isNaN(e)||isNaN(r)||(e=Math.max(e,Yt),r=Math.max(r,Qt),this._bufferService.resize(e,r))}scroll(e,r=!1){this._bufferService.scroll(e,r)}scrollLines(e,r){this._bufferService.scrollLines(e,r)}scrollPages(e){this.scrollLines(e*(this.rows-1))}scrollToTop(){this.scrollLines(-this._bufferService.buffer.ydisp)}scrollToBottom(e){this.scrollLines(this._bufferService.buffer.ybase-this._bufferService.buffer.ydisp)}scrollToLine(e){let r=e-this._bufferService.buffer.ydisp;r!==0&&this.scrollLines(r)}registerEscHandler(e,r){return this._inputHandler.registerEscHandler(e,r)}registerDcsHandler(e,r){return this._inputHandler.registerDcsHandler(e,r)}registerCsiHandler(e,r){return this._inputHandler.registerCsiHandler(e,r)}registerOscHandler(e,r){return this._inputHandler.registerOscHandler(e,r)}_setup(){this._handleWindowsPtyOptionChange()}reset(){this._inputHandler.reset(),this._bufferService.reset(),this._charsetService.reset(),this.coreService.reset(),this.coreMouseService.reset()}_handleWindowsPtyOptionChange(){let e=!1,r=this.optionsService.rawOptions.windowsPty;r&&r.buildNumber!==void 0&&r.buildNumber!==void 0?e=r.backend==="conpty"&&r.buildNumber<21376:this.optionsService.rawOptions.windowsMode&&(e=!0),e?this._enableWindowsWrappingHeuristics():this._windowsWrappingHeuristics.clear()}_enableWindowsWrappingHeuristics(){if(!this._windowsWrappingHeuristics.value){let e=[];e.push(this.onLineFeed(tr.bind(null,this._bufferService))),e.push(this.registerCsiHandler({final:"H"},()=>(tr(this._bufferService),!1))),this._windowsWrappingHeuristics.value=Q(()=>{for(let r of e)r.dispose()})}}};var Rt=class extends At{constructor(e={}){super(e);this._onBell=this._register(new S);this.onBell=this._onBell.event;this._onCursorMove=this._register(new S);this.onCursorMove=this._onCursorMove.event;this._onTitleChange=this._register(new S);this.onTitleChange=this._onTitleChange.event;this._onA11yCharEmitter=this._register(new S);this.onA11yChar=this._onA11yCharEmitter.event;this._onA11yTabEmitter=this._register(new S);this.onA11yTab=this._onA11yTabEmitter.event;this._setup(),this._register(this._inputHandler.onRequestBell(()=>this.bell())),this._register(this._inputHandler.onRequestReset(()=>this.reset())),this._register(V.forward(this._inputHandler.onCursorMove,this._onCursorMove)),this._register(V.forward(this._inputHandler.onTitleChange,this._onTitleChange)),this._register(V.forward(this._inputHandler.onA11yChar,this._onA11yCharEmitter)),this._register(V.forward(this._inputHandler.onA11yTab,this._onA11yTabEmitter))}get buffer(){return this.buffers.active}get markers(){return this.buffer.markers}addMarker(e){if(this.buffer===this.buffers.normal)return this.buffer.addMarker(this.buffer.ybase+this.buffer.y+e)}bell(){this._onBell.fire()}input(e,r=!0){this.coreService.triggerDataEvent(e,r)}resize(e,r){e===this.cols&&r===this.rows||super.resize(e,r)}clear(){if(!(this.buffer.ybase===0&&this.buffer.y===0)){this.buffer.lines.set(0,this.buffer.lines.get(this.buffer.ybase+this.buffer.y)),this.buffer.lines.length=1,this.buffer.ydisp=0,this.buffer.ybase=0,this.buffer.y=0;for(let e=1;e<this.rows;e++)this.buffer.lines.push(this.buffer.getBlankLine(P));this._onScroll.fire({position:this.buffer.ydisp})}}reset(){this.options.rows=this.rows,this.options.cols=this.cols,this._setup(),super.reset()}};var Ot=class{constructor(){this._addons=[]}dispose(){for(let t=this._addons.length-1;t>=0;t--)this._addons[t].instance.dispose()}loadAddon(t,e){let r={instance:e,dispose:e.dispose,isDisposed:!1};this._addons.push(r),e.dispose=()=>this._wrappedAddonDispose(r),e.activate(t)}_wrappedAddonDispose(t){if(t.isDisposed)return;let e=-1;for(let r=0;r<this._addons.length;r++)if(this._addons[r]===t){e=r;break}if(e===-1)throw new Error("Could not dispose an addon that has not been loaded");t.isDisposed=!0,t.dispose.apply(t.instance),this._addons.splice(e,1)}};var Bi=["cols","rows"],Jr=class extends A{constructor(t){super(),this._core=this._register(new Rt(t)),this._addonManager=this._register(new Ot),this._publicOptions={...this._core.options};let e=i=>this._core.options[i],r=(i,s)=>{this._checkReadonlyOptions(i),this._core.options[i]=s};for(let i in this._core.options){Object.defineProperty(this._publicOptions,i,{get:()=>this._core.options[i],set:a=>{this._checkReadonlyOptions(i),this._core.options[i]=a}});let s={get:e.bind(this,i),set:r.bind(this,i)};Object.defineProperty(this._publicOptions,i,s)}}_checkReadonlyOptions(t){if(Bi.includes(t))throw new Error(`Option "${t}" can only be set in the constructor`)}_checkProposedApi(){if(!this._core.optionsService.options.allowProposedApi)throw new Error("You must set the allowProposedApi option to true to use proposed API")}get onBell(){return this._core.onBell}get onBinary(){return this._core.onBinary}get onCursorMove(){return this._core.onCursorMove}get onData(){return this._core.onData}get onLineFeed(){return this._core.onLineFeed}get onResize(){return this._core.onResize}get onScroll(){return this._core.onScroll}get onTitleChange(){return this._core.onTitleChange}get onWriteParsed(){return this._core.onWriteParsed}get parser(){return this._checkProposedApi(),this._parser||(this._parser=new lt(this._core)),this._parser}get unicode(){return this._checkProposedApi(),new ct(this._core)}get rows(){return this._core.rows}get cols(){return this._core.cols}get buffer(){return this._checkProposedApi(),this._buffer||(this._buffer=this._register(new at(this._core))),this._buffer}get markers(){return this._checkProposedApi(),this._core.markers}get modes(){let t=this._core.coreService.decPrivateModes,e="none";switch(this._core.coreMouseService.activeProtocol){case"X10":e="x10";break;case"VT200":e="vt200";break;case"DRAG":e="drag";break;case"ANY":e="any";break}return{applicationCursorKeysMode:t.applicationCursorKeys,applicationKeypadMode:t.applicationKeypad,bracketedPasteMode:t.bracketedPasteMode,insertMode:this._core.coreService.modes.insertMode,mouseTrackingMode:e,originMode:t.origin,reverseWraparoundMode:t.reverseWraparound,sendFocusMode:t.sendFocus,wraparoundMode:t.wraparound}}get options(){return this._publicOptions}set options(t){for(let e in t)this._publicOptions[e]=t[e]}input(t,e=!0){this._core.input(t,e)}resize(t,e){this._verifyIntegers(t,e),this._core.resize(t,e)}registerMarker(t=0){return this._checkProposedApi(),this._verifyIntegers(t),this._core.addMarker(t)}addMarker(t){return this.registerMarker(t)}dispose(){super.dispose()}scrollLines(t){this._verifyIntegers(t),this._core.scrollLines(t)}scrollPages(t){this._verifyIntegers(t),this._core.scrollPages(t)}scrollToTop(){this._core.scrollToTop()}scrollToBottom(){this._core.scrollToBottom()}scrollToLine(t){this._verifyIntegers(t),this._core.scrollToLine(t)}clear(){this._core.clear()}write(t,e){this._core.write(t,e)}writeln(t,e){this._core.write(t),this._core.write(`\r
`,e)}reset(){this._core.reset()}loadAddon(t){this._addonManager.loadAddon(this,t)}_verifyIntegers(...t){for(let e of t)if(e===1/0||isNaN(e)||e%1!==0)throw new Error("This API only accepts integers")}};export{Jr as Terminal};
//# sourceMappingURL=xterm-headless.mjs.map
